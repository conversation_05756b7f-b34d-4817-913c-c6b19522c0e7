/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    LlmClient.cpp

Abstract:

    This common module implements the LLM service communication.
    Handles HTTP communication with the LLM backend service.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 04/20/2025

--*/

#include <windows.h>
#include <windef.h>
#include <winhttp.h>

#include "LlmClient.h"
#include "Aimxconstants.h"
#include "AimxCommon.h"
#include "wpp.h"
#include "StringUtils.h"
#include "nlohmann\json.hpp"
#include <codecvt>
#include <locale>
#include <sstream>
#include <filesystem>
#include <shared_mutex>

#include "LlmClientlib.cpp.tmh"

/*++

Routine Description:

    Constructor for LlmClient class with configuration parameters.
    Initializes the LLM service with specified configuration.

Arguments:

    endpointUrl - The URL of the LLM service endpoint.
    model - The model name to use.
    temperature - The temperature setting for the LLM.
    topK - The top-K setting for the LLM.
    topP - The top-P setting for the LLM.

Return Value:

    None.

--*/
LlmClient::LlmClient(
    _In_ const std::wstring& endpointUrl,
    _In_ const std::wstring& model,
    _In_ float temperature,
    _In_ int topK,
    _In_ float topP
)
    : m_endpointUrl(endpointUrl)
    , m_model(model)
    , m_temperature(temperature)
    , m_topK(topK)
    , m_topP(topP)
{
}

/*++

Routine Description:

    Destructor for LlmClient class. Performs cleanup.

Arguments:

    None.

Return Value:

    None.

--*/
LlmClient::~LlmClient()
{
}

/*++

Routine Description:

    Sends a prompt to the LLM service and receives a response.
    Handles formatting the prompt, creating the JSON payload, and
    processing the response.

Arguments:

    prompt - The user's prompt to send to the LLM service.
    response - Output parameter that will receive the LLM response.

Return Value:

    bool indicating success or failure of the operation.

--*/
bool
LlmClient::SendPrompt(
    _In_ const std::wstring& prompt,
    _Out_ std::wstring& response
    )
{
    try
    {
        // Log the prompt
        TraceVerb(LlmClientLib, "Sending prompt: '%s'", WideToUtf8(prompt).c_str());

        // Format the prompt with proper instruction template before sending
        std::wstring formattedPrompt = FormatPrompt(prompt);
        TraceVerb(LlmClientLib, "Formatted prompt: '%s'...", WideToUtf8(formattedPrompt).c_str());

        // Create JSON payload using the proper JSON library
        nlohmann::json requestJson;
        requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_PROMPT] = WideToUtf8(formattedPrompt); 
        requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_TEMPERATURE] = m_temperature;
        requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_TOP_K] = m_topK;
        requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_TOP_P] = m_topP;
        requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_N_PREDICT] = AimxConstants::LlmInferenceDefaults::LLM_DEFAULT_N_PREDICT_VALUE;
        requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_CACHE_PROMPT] = AimxConstants::LlmInferenceDefaults::LLM_DEFAULT_CACHE_PROMPT_VALUE;
        requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_STOP] = {AimxConstants::LlmProtocol::AIMX_LLM_STOP_TOKEN};
        requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_STREAM] = AimxConstants::LlmInferenceDefaults::LLM_DEFAULT_STREAM_VALUE;

        // Log the JSON payload
        std::string jsonStr = requestJson.dump();
        TraceVerb(LlmClientLib, "LLM request payload: %s", jsonStr.c_str());

        // Convert JSON to string and then to wide string
        std::wstring jsonWide = Utf8ToWide(jsonStr);

        // Send the request to the LLM service
        bool success = HttpPost(m_endpointUrl, jsonWide, response);

        // Log the success state
        if (success)
        {
            TraceInfo(LlmClientLib, "LLM request succeeded. Response length: %d", static_cast<int>(response.length()));
            if (!response.empty())
            {
                std::string responseUtf8 = WideToUtf8(response);
                TraceInfo(LlmClientLib, "LLM response preview: %s", responseUtf8.c_str());
            }
        }
        else
        {
            TraceErr(LlmClientLib, "LLM request failed");
        }

        return success;
    }
    catch (const std::exception& e)
    {
        TraceErr(LlmClientLib, "Exception in SendPrompt: %s", e.what());
        response = Utf8ToWide(std::string("Error: ") + e.what());
        return false;
    }
}

/*++

Routine Description:

    Sends a prompt to the Foundry LLM service and receives a response.
    Handles formatting the prompt, creating the JSON payload, and
    processing the response.

Arguments:

    systemPrompt - The system prompt to send to the LLM service.
    userPrompt - The user's prompt to send to the LLM service.
    model - The model to use for the LLM service.
    response - Output parameter that will receive the LLM response.

Return Value:

    bool indicating success or failure of the operation.

--*/
bool
LlmClient::SendFoundryPrompt(
    _In_ const std::wstring& systemPrompt,
    _In_ const std::wstring& userPrompt,
    _In_ const std::wstring& model,
    _Out_ std::wstring& response
    )
{
    try
    {
        // Log the prompt
        TraceVerb(LlmClientLib, "Sending Foundry prompt with model '%ws'", model.c_str());
        TraceVerb(LlmClientLib, "System prompt: '%ws'", systemPrompt.c_str());
        TraceVerb(LlmClientLib, "User prompt: '%ws'", userPrompt.c_str());

        // Create JSON payload using the proper JSON library
        nlohmann::json requestJson;
        requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_MODEL] = WideToUtf8(model);
        requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_MESSAGES] = {
            {{AimxConstants::JsonFields::AIMX_JSON_KEY_ROLE, AimxConstants::LlmFoundryRoleType::AIMX_LLM_FOUNDRY_ROLE_TYPE_SYSTEM}, {AimxConstants::JsonFields::AIMX_JSON_KEY_CONTENT, WideToUtf8(systemPrompt)}},
            {{AimxConstants::JsonFields::AIMX_JSON_KEY_ROLE, AimxConstants::LlmFoundryRoleType::AIMX_LLM_FOUNDRY_ROLE_TYPE_USER}, {AimxConstants::JsonFields::AIMX_JSON_KEY_CONTENT, WideToUtf8(userPrompt)}}
        };

        requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_TEMPERATURE] = m_temperature;
        requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_TOP_K] = m_topK;
        requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_TOP_P] = m_topP;
        requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_STREAM] = AimxConstants::LlmInferenceDefaults::LLM_DEFAULT_STREAM_VALUE;

        // Log the JSON payload
        std::string jsonStr = requestJson.dump();
        TraceVerb(LlmClientLib, "LLM request payload: %s", jsonStr.c_str());

        // Convert JSON to string and then to wide string
        std::wstring jsonWide = Utf8ToWide(jsonStr);

        // Send the request to the LLM service
        bool success = HttpPost(m_endpointUrl, jsonWide, response);

        // Log the success state
        if (success)
        {
            TraceInfo(LlmClientLib, "LLM request succeeded. Response length: %d", static_cast<int>(response.length()));
            if (!response.empty())
            {
                std::string responseUtf8 = WideToUtf8(response);
                TraceInfo(LlmClientLib, "LLM response preview: %s", responseUtf8.c_str());
            }
        }
        else
        {
            TraceErr(LlmClientLib, "LLM request failed");
        }

        return success;
    }
    catch (const std::exception& e)
    {
        TraceErr(LlmClientLib, "Exception in SendPrompt: %s", e.what());
        response = Utf8ToWide(std::string("Error: ") + e.what());
        return false;
    }
}

/*++

Routine Description:

    Sends a prompt with context (for retrieval-augmented generation) to the LLM
    service and receives a response.

Arguments:

    prompt - The user's prompt to send to the LLM service.
    context - The context information to augment the prompt.
    response - Output parameter that will receive the LLM response.

Return Value:

    bool indicating success or failure of the operation.

--*/
bool
LlmClient::SendPromptWithContext(
    _In_ const std::wstring& prompt,
    _In_ const std::wstring& context,
    _Out_ std::wstring& response
    )
{
    // Format the prompt with context
    std::wstring formattedPrompt = FormatPromptWithContext(prompt, context);

    // Reuse the existing SendPrompt method
    return SendPrompt(formattedPrompt, response);
}

/*++

Routine Description:

    Formats a prompt with context for retrieval-augmented generation (RAG).
    Combines the instruction, context, and prompt in the required format.

Arguments:

    prompt - The user's question or prompt.
    context - The context information to be used for answering.

Return Value:

    std::wstring containing the formatted prompt with context.

--*/
std::wstring
LlmClient::FormatPromptWithContext(
    _In_ const std::wstring& prompt,
    _In_ const std::wstring& context
    )
{
    // Use the simpler formatting approach from the C# example
    std::wstring instruction = L"You are a helpful assistant. Use the following context to answer the question. If you don't know the answer based on the context, say that you don't know.";
    std::wostringstream formatted;
    formatted << L"[INST] <<SYS>>\n" << instruction << L"\n<</SYS>>\n\n";
    formatted << L"Context:\n" << context << L"\n\n";
    formatted << L"Question: " << prompt << L" [/INST]";

    return formatted.str();
}

/*++

Routine Description:

    Formats a simple prompt (without context) with the appropriate system
    instruction and formatting.

Arguments:

    prompt - The user's prompt to format.

Return Value:

    std::wstring containing the formatted prompt.

--*/
std::wstring
LlmClient::FormatPrompt(
    _In_ const std::wstring& prompt
    )
{
    // Format a simple prompt (no context) with the same structure
    std::wstring instruction = L"You are a helpful assistant.";
    std::wostringstream formatted;
    formatted << L"[INST] <<SYS>>\n" << instruction << L"\n<</SYS>>\n\n";
    formatted << prompt << L" [/INST]";

    return formatted.str();
}

/*++

Routine Description:

    Performs an HTTP POST request to the LLM endpoint using WinHTTP.
    Handles URL parsing, connection setup, sending the request, and
    processing the response.

Arguments:

    url - The URL of the LLM endpoint.
    data - The request data to send (JSON payload).
    response - Output parameter that will receive the HTTP response.

Return Value:

    bool indicating success or failure of the HTTP request.

--*/
bool
LlmClient::HttpPost(
    _In_ const std::wstring& url,
    _In_ const std::wstring& data,
    _Out_ std::wstring& response
    )
{
    TraceVerb(LlmClientLib, "Sending HTTP POST to: %s", WideToUtf8(url).c_str());

    // Parse the URL
    URL_COMPONENTS urlComp = { 0 };
    urlComp.dwStructSize = sizeof(urlComp);

    // Set up buffers for the URL components
    wchar_t hostName[AimxConstants::BufferSizes::AIMX_URL_HOSTNAME_BUFFER] = { 0 };
    wchar_t urlPath[AimxConstants::BufferSizes::AIMX_URL_PATH_BUFFER] = { 0 };

    urlComp.lpszHostName = hostName;
    urlComp.dwHostNameLength = sizeof(hostName) / sizeof(wchar_t);
    urlComp.lpszUrlPath = urlPath;
    urlComp.dwUrlPathLength = sizeof(urlPath) / sizeof(wchar_t);
    urlComp.dwSchemeLength = 1;

    if (!WinHttpCrackUrl(url.c_str(), static_cast<DWORD>(url.size()), 0, &urlComp))
    {
        TraceErr(LlmClientLib, "Failed to parse URL: %s", WideToUtf8(url).c_str());
        return false;
    }

    // Initialize WinHTTP
    TraceInfo(LlmClientLib, "Initializing WinHTTP session");
    HINTERNET hSession = WinHttpOpen(AimxConstants::Http::AIMX_HTTP_USER_AGENT, WINHTTP_ACCESS_TYPE_DEFAULT_PROXY,
                                     WINHTTP_NO_PROXY_NAME, WINHTTP_NO_PROXY_BYPASS, 0);
    if (!hSession)
    {
        TraceErr(LlmClientLib, "Failed to create WinHTTP session. Error: %lu", GetLastError());
        return false;
    }

    // Connect to the server
    std::string host = WideToUtf8(hostName);
    TraceInfo(LlmClientLib, "Connecting to server: %s:%d", host.c_str(), urlComp.nPort);
    HINTERNET hConnect = WinHttpConnect(hSession, hostName, urlComp.nPort, 0);
    if (!hConnect)
    {
        TraceErr(LlmClientLib, "Failed to connect to server: %s. Error: %lu", host.c_str(), GetLastError());
        WinHttpCloseHandle(hSession);
        return false;
    }

    // Create the request
    HINTERNET hRequest = WinHttpOpenRequest(hConnect, AimxConstants::Http::AIMX_HTTP_METHOD_POST, urlPath,
                                           NULL, WINHTTP_NO_REFERER,
                                           WINHTTP_DEFAULT_ACCEPT_TYPES,
                                           urlComp.nScheme == INTERNET_SCHEME_HTTPS ? WINHTTP_FLAG_SECURE : 0);
    if (!hRequest)
    {
        TraceErr(LlmClientLib, "Failed to open HTTP request. Error: %lu", GetLastError());
        WinHttpCloseHandle(hConnect);
        WinHttpCloseHandle(hSession);
        return false;
    }

    // Set extended timeouts for LLM service (LLM inference can take longer)
    DWORD connectTimeout = AimxConstants::Http::AIMX_HTTP_CONNECT_TIMEOUT;
    DWORD sendTimeout = AimxConstants::Http::AIMX_HTTP_SEND_TIMEOUT;
    DWORD receiveTimeout = AimxConstants::Http::AIMX_HTTP_RECEIVE_TIMEOUT;

    if (!WinHttpSetOption(hRequest, WINHTTP_OPTION_CONNECT_TIMEOUT, &connectTimeout, sizeof(connectTimeout)))
    {
        TraceWarn(LlmClientLib, "Failed to set connect timeout. Error: %lu", GetLastError());
    }

    if (!WinHttpSetOption(hRequest, WINHTTP_OPTION_SEND_TIMEOUT, &sendTimeout, sizeof(sendTimeout)))
    {
        TraceWarn(LlmClientLib, "Failed to set send timeout. Error: %lu", GetLastError());
    }

    if (!WinHttpSetOption(hRequest, WINHTTP_OPTION_RECEIVE_TIMEOUT, &receiveTimeout, sizeof(receiveTimeout)))
    {
        TraceWarn(LlmClientLib, "Failed to set receive timeout. Error: %lu", GetLastError());
    }

    TraceInfo(LlmClientLib, "Set timeouts - Connect: %lums, Send: %lums, Receive: %lums",
              connectTimeout, sendTimeout, receiveTimeout);

    // Convert data to UTF-8
    std::string utf8Data = WideToUtf8(data);

    // Log details for debugging
    TraceInfo(LlmClientLib, "Request details - Host: %s, Port: %d, Path: %s",
              WideToUtf8(hostName).c_str(), urlComp.nPort, WideToUtf8(urlPath).c_str());
    TraceInfo(LlmClientLib, "Request data size: %d bytes", static_cast<int>(utf8Data.size()));

    // Set up request headers
    std::wstring headers = AimxConstants::Http::AIMX_HTTP_CONTENT_TYPE_JSON;
    TraceVerb(LlmClientLib, "Adding headers: %s", WideToUtf8(headers).c_str());

    BOOL headerResult = WinHttpAddRequestHeaders(hRequest, headers.c_str(), static_cast<DWORD>(headers.length()), WINHTTP_ADDREQ_FLAG_ADD);
    if (!headerResult)
    {
        TraceWarn(LlmClientLib, "Failed to add request headers. Error: %lu", GetLastError());
    }

    // Validate endpoint URL accessibility before sending request
    if (m_endpointUrl.empty())
    {
        TraceErr(LlmClientLib, "Endpoint URL is empty");
        WinHttpCloseHandle(hRequest);
        WinHttpCloseHandle(hConnect);
        WinHttpCloseHandle(hSession);
        return false;
    }

    // Send the request
    TraceVerb(LlmClientLib, "Sending HTTP request with %d bytes of data", static_cast<int>(utf8Data.size()));
    if (!WinHttpSendRequest(hRequest, WINHTTP_NO_ADDITIONAL_HEADERS, 0,
                           const_cast<char*>(utf8Data.c_str()), static_cast<DWORD>(utf8Data.size()),
                           static_cast<DWORD>(utf8Data.size()), 0))
    {
        DWORD error = GetLastError();
        TraceErr(LlmClientLib, "Failed to send HTTP request. Error: %lu (0x%08X)", error, error);

        // Provide more specific error information
        switch (error)
        {
        case ERROR_WINHTTP_CANNOT_CONNECT:
            TraceErr(LlmClientLib, "Cannot connect to the server. Check if the LLM service is running and the endpoint URL is correct.");
            break;
        case ERROR_WINHTTP_NAME_NOT_RESOLVED:
            TraceErr(LlmClientLib, "The server name cannot be resolved. Check the hostname in the endpoint URL.");
            break;
        case ERROR_WINHTTP_TIMEOUT:
            TraceErr(LlmClientLib, "The request timed out. The LLM service may be unresponsive.");
            break;
        case ERROR_WINHTTP_INVALID_URL:
            TraceErr(LlmClientLib, "Invalid URL format. Check the endpoint URL configuration.");
            break;
        case ERROR_WINHTTP_UNRECOGNIZED_SCHEME:
            TraceErr(LlmClientLib, "Unrecognized URL scheme. Ensure URL starts with http:// or https://");
            break;
        case ERROR_WINHTTP_CONNECTION_ERROR:
            TraceErr(LlmClientLib, "Connection error. The LLM service may not be listening on the specified port.");
            break;
        default:
            TraceErr(LlmClientLib, "Unexpected WinHTTP error. Check network connectivity and firewall settings.");
            break;
        }

        WinHttpCloseHandle(hRequest);
        WinHttpCloseHandle(hConnect);
        WinHttpCloseHandle(hSession);
        return false;
    }

    // Receive the response
    TraceVerb(LlmClientLib, "Receiving HTTP response");
    if (!WinHttpReceiveResponse(hRequest, NULL))
    {
        DWORD error = GetLastError();
        TraceErr(LlmClientLib, "Failed to receive HTTP response. Error: %lu (0x%08X)", error, error);

        // Provide specific diagnostics for WinHttpReceiveResponse failures
        switch (error)
        {
        case ERROR_WINHTTP_TIMEOUT:
            TraceErr(LlmClientLib, "Response timeout. The LLM service is not responding within the expected time. This could indicate:");
            TraceErr(LlmClientLib, "  - The LLM service is processing a complex request");
            TraceErr(LlmClientLib, "  - The service is overloaded or unresponsive");
            TraceErr(LlmClientLib, "  - Network latency issues");
            break;
        case ERROR_WINHTTP_CONNECTION_ERROR:
            TraceErr(LlmClientLib, "Connection error during response. This could indicate:");
            TraceErr(LlmClientLib, "  - The LLM service terminated the connection unexpectedly");
            TraceErr(LlmClientLib, "  - Network connectivity issues");
            TraceErr(LlmClientLib, "  - The service crashed while processing the request");
            break;
        case ERROR_WINHTTP_INVALID_SERVER_RESPONSE:
            TraceErr(LlmClientLib, "Invalid server response. This could indicate:");
            TraceErr(LlmClientLib, "  - The LLM service returned malformed HTTP headers");
            TraceErr(LlmClientLib, "  - The service is not a proper HTTP server");
            TraceErr(LlmClientLib, "  - Wrong endpoint URL (pointing to non-HTTP service)");
            break;
        case ERROR_WINHTTP_SECURE_FAILURE:
            TraceErr(LlmClientLib, "SSL/TLS failure. This could indicate:");
            TraceErr(LlmClientLib, "  - Certificate validation issues");
            TraceErr(LlmClientLib, "  - SSL/TLS version mismatch");
            TraceErr(LlmClientLib, "  - Attempting HTTPS on HTTP port or vice versa");
            break;
        case ERROR_WINHTTP_HEADER_NOT_FOUND:
            TraceErr(LlmClientLib, "Required HTTP headers missing. This could indicate:");
            TraceErr(LlmClientLib, "  - The service returned incomplete HTTP headers");
            TraceErr(LlmClientLib, "  - Non-HTTP service responding on the endpoint");
            break;
        case ERROR_WINHTTP_INCORRECT_HANDLE_STATE:
            TraceErr(LlmClientLib, "Incorrect handle state. This indicates an internal error:");
            TraceErr(LlmClientLib, "  - WinHTTP API usage error");
            TraceErr(LlmClientLib, "  - Request handle in invalid state");
            break;
        case ERROR_WINHTTP_OPERATION_CANCELLED:
            TraceErr(LlmClientLib, "Operation was cancelled. This could indicate:");
            TraceErr(LlmClientLib, "  - Request was aborted by timeout");
            TraceErr(LlmClientLib, "  - System shutdown or service termination");
            break;
        default:
            TraceErr(LlmClientLib, "Unexpected error during response reception:");
            TraceErr(LlmClientLib, "  - Check if the endpoint URL is correct");
            TraceErr(LlmClientLib, "  - Verify the LLM service is running and responding");
            TraceErr(LlmClientLib, "  - Check firewall and network connectivity");
            TraceErr(LlmClientLib, "  - Verify the service accepts the request format being sent");
            break;
        }

        WinHttpCloseHandle(hRequest);
        WinHttpCloseHandle(hConnect);
        WinHttpCloseHandle(hSession);
        return false;
    }

    // Check the HTTP status code first
    DWORD statusCode = 0;
    DWORD statusCodeSize = sizeof(statusCode);
    if (WinHttpQueryHeaders(hRequest, WINHTTP_QUERY_STATUS_CODE | WINHTTP_QUERY_FLAG_NUMBER,
                           NULL, &statusCode, &statusCodeSize, NULL))
    {
        TraceInfo(LlmClientLib, "HTTP status code: %lu", statusCode);

        if (statusCode != AimxConstants::Http::AIMX_HTTP_STATUS_OK)
        {
            // Log non-200 status codes with detailed information
            DWORD bufferSize = 0;
            WinHttpQueryHeaders(hRequest, WINHTTP_QUERY_STATUS_TEXT, NULL, NULL, &bufferSize, NULL);

            if (bufferSize > 0 && GetLastError() == ERROR_INSUFFICIENT_BUFFER)
            {
                std::vector<wchar_t> buffer(bufferSize / sizeof(wchar_t));
                if (WinHttpQueryHeaders(hRequest, WINHTTP_QUERY_STATUS_TEXT, NULL, buffer.data(), &bufferSize, NULL))
                {
                    std::wstring statusText(buffer.data(), buffer.size());
                    TraceErr(LlmClientLib, "HTTP request failed with status code: %lu - %s", statusCode, WideToUtf8(statusText).c_str());
                }
            }

            // Read error response body for more details
            std::string errorResponse;
            DWORD bytesAvailable = 0;
            DWORD bytesRead = 0;
            char buffer[AimxConstants::BufferSizes::AIMX_LARGE_BUFFER] = { 0 };

            while (WinHttpQueryDataAvailable(hRequest, &bytesAvailable) && bytesAvailable > 0)
            {
                if (WinHttpReadData(hRequest, buffer, static_cast<DWORD>(min(sizeof(buffer), static_cast<size_t>(bytesAvailable))), &bytesRead))
                {
                    if (bytesRead > 0)
                    {
                        errorResponse.append(buffer, bytesRead);
                    }
                }
            }

            if (!errorResponse.empty())
            {
                TraceErr(LlmClientLib, "Server error response: %s", errorResponse.c_str());
            }

            // Common HTTP error codes and their meanings
            switch (statusCode)
            {
            case AimxConstants::Http::AIMX_HTTP_STATUS_BAD_REQUEST:
                TraceErr(LlmClientLib, "Bad Request - The LLM service rejected the request format");
                break;
            case AimxConstants::Http::AIMX_HTTP_STATUS_NOT_FOUND:
                TraceErr(LlmClientLib, "Not Found - The endpoint path does not exist on the LLM service");
                break;
            case AimxConstants::Http::AIMX_HTTP_STATUS_METHOD_NOT_ALLOWED:
                TraceErr(LlmClientLib, "Method Not Allowed - The LLM service does not accept POST requests on this endpoint");
                break;
            case AimxConstants::Http::AIMX_HTTP_STATUS_INTERNAL_SERVER_ERROR:
                TraceErr(LlmClientLib, "Internal Server Error - The LLM service encountered an error processing the request");
                break;
            case AimxConstants::Http::AIMX_HTTP_STATUS_SERVICE_UNAVAILABLE:
                TraceErr(LlmClientLib, "Service Unavailable - The LLM service is temporarily unavailable");
                break;
            default:
                TraceErr(LlmClientLib, "HTTP error %lu - Check LLM service configuration and request format", statusCode);
                break;
            }

            WinHttpCloseHandle(hRequest);
            WinHttpCloseHandle(hConnect);
            WinHttpCloseHandle(hSession);
            return false;
        }
    }
    else
    {
        TraceWarn(LlmClientLib, "Failed to query HTTP status code. Error: %lu", GetLastError());
    }

    // Read the response data
    std::string responseData;
    DWORD bytesAvailable = 0;
    DWORD bytesRead = 0;
    char buffer[AimxConstants::BufferSizes::AIMX_LARGE_BUFFER] = { 0 };

    while (WinHttpQueryDataAvailable(hRequest, &bytesAvailable) && bytesAvailable > 0)
    {
        bytesRead = 0;
        if (WinHttpReadData(hRequest, buffer, static_cast<DWORD>(min(sizeof(buffer), static_cast<size_t>(bytesAvailable))), &bytesRead))
        {
            responseData.append(buffer, bytesRead);
        }
        else
        {
            TraceErr(LlmClientLib, "Failed to read HTTP response data. Error: %lu", GetLastError());
            break;
        }
    }

    // Log raw response
    TraceVerb(LlmClientLib, "LLM Raw Response: %s", responseData.c_str());

    // Clean up the response before trying to parse it
    // Some LLM engines return responses with ? or newline at the beginning
    while (!responseData.empty() && (responseData[0] == '?' || responseData[0] == '\n' || responseData[0] == '\r'))
    {
        responseData.erase(0, 1);
    }

    // Trim whitespace from both ends
    size_t start = responseData.find_first_not_of(AimxConstants::LlmResponseParsing::AIMX_TRIM_CHARS);
    if (start != std::string::npos)
    {
        size_t end = responseData.find_last_not_of(AimxConstants::LlmResponseParsing::AIMX_TRIM_CHARS);
        responseData = responseData.substr(start, end - start + 1);
    }
    else
    {
        responseData.clear(); // String contains only whitespace
    }

    // Parse JSON response to extract content if possible
    try
    {
        // Only try to parse as JSON if it's non-empty and starts with a valid JSON character
        if (!responseData.empty() && responseData.length() > 1 && (responseData[0] == '{' || responseData[0] == '['))
        {
            auto jsonResponse = nlohmann::json::parse(responseData);

            // Check if the response is a valid JSON object
            if (jsonResponse.contains(AimxConstants::JsonFields::AIMX_JSON_KEY_CHOICES) && jsonResponse[AimxConstants::JsonFields::AIMX_JSON_KEY_CHOICES].is_array() &&
                !jsonResponse[AimxConstants::JsonFields::AIMX_JSON_KEY_CHOICES].empty() && jsonResponse[AimxConstants::JsonFields::AIMX_JSON_KEY_CHOICES][0].contains(AimxConstants::JsonFields::AIMX_JSON_KEY_DELTA))
            {
                // Extract the content from the first choice
                auto& choice = jsonResponse[AimxConstants::JsonFields::AIMX_JSON_KEY_CHOICES][0][AimxConstants::JsonFields::AIMX_JSON_KEY_DELTA];
                if (choice.contains(AimxConstants::JsonFields::AIMX_JSON_KEY_CONTENT))
                {
                    responseData = choice[AimxConstants::JsonFields::AIMX_JSON_KEY_CONTENT];
                    TraceInfo(LlmClientLib, "Extracted content from JSON response: %s", responseData.c_str());
                }
            }
            else if (jsonResponse.contains(AimxConstants::JsonFields::AIMX_JSON_KEY_CONTENT))
            {
                // Check for different response formats
                std::string content;
                content = jsonResponse[AimxConstants::JsonFields::AIMX_JSON_KEY_CONTENT];
                responseData = content;
            }
        }
        else
        {
            // Not valid JSON, check if it has a response marker
            size_t responseMarker = responseData.find(AimxConstants::LlmProtocol::AIMX_LLM_RESPONSE_MARKER);
            if (responseMarker != std::string::npos)
            {
                responseData = responseData.substr(responseMarker + strlen(AimxConstants::LlmProtocol::AIMX_LLM_RESPONSE_MARKER)); // Skip "[response]:"
                TraceInfo(LlmClientLib, "Found [response]: marker, removed prefix");
            }
        }
    }
    catch (const std::exception& e)
    {
        TraceErr(LlmClientLib, "Failed to parse LLM response as JSON: %s", e.what());
    }

    // Convert response to wide string
    response = Utf8ToWide(responseData);

    // Clean up
    WinHttpCloseHandle(hRequest);
    WinHttpCloseHandle(hConnect);
    WinHttpCloseHandle(hSession);

    return true;
}

/*++

Routine Description:

    Streams a prompt to the LLM service and processes the response in chunks.
    Uses a callback function to handle each chunk as it arrives.

Arguments:

    prompt - The user's prompt to send to the LLM service.
    callback - Callback function that processes each response chunk.

Return Value:

    bool indicating success or failure of the operation.

--*/
bool
LlmClient::StreamPrompt(
    _In_ const std::wstring& prompt,
    _In_ StreamingResponseCallback callback
    )
{
    try
    {
        TraceVerb(LlmClientLib, "StreamPrompt: Starting streaming request");

        // Format the prompt appropriately
        std::wstring formattedPrompt = FormatPrompt(prompt);

        // Create the JSON payload with correct parameters for streaming
        nlohmann::json jsonPayload;
        jsonPayload[AimxConstants::JsonFields::AIMX_JSON_KEY_PROMPT] = WideToUtf8(formattedPrompt);
        jsonPayload[AimxConstants::JsonFields::AIMX_JSON_KEY_STREAM] = AimxConstants::LlmInferenceDefaults::LLM_DEFAULT_STREAM_VALUE;
        jsonPayload[AimxConstants::JsonFields::AIMX_JSON_KEY_TEMPERATURE] = m_temperature;
        jsonPayload[AimxConstants::JsonFields::AIMX_JSON_KEY_TOP_K] = m_topK;
        jsonPayload[AimxConstants::JsonFields::AIMX_JSON_KEY_TOP_P] = m_topP;
        jsonPayload[AimxConstants::JsonFields::AIMX_JSON_KEY_N_PREDICT] = AimxConstants::LlmInferenceDefaults::LLM_DEFAULT_N_PREDICT_VALUE;
        jsonPayload[AimxConstants::JsonFields::AIMX_JSON_KEY_STOP] = {AimxConstants::LlmProtocol::AIMX_LLM_STOP_TOKEN};

        // Convert to string
        std::string jsonStr = jsonPayload.dump();
        std::wstring jsonData = Utf8ToWide(jsonStr);

        // Use the same endpoint as non-streaming requests
        TraceVerb(LlmClientLib, "StreamPrompt: JSON payload: %s", jsonStr.c_str());
        TraceVerb(LlmClientLib, "StreamPrompt: Calling HttpPostStreaming with URL: %s", WideToUtf8(m_endpointUrl).c_str());
        bool success = HttpPostStreaming(m_endpointUrl, jsonData, callback);

        if (!success)
        {
            TraceErr(LlmClientLib, "StreamPrompt: Failed to stream from LLM service");
            callback(L"Error communicating with LLM service", true);
        }

        return success;
    }
    catch (const std::exception& e)
    {
        TraceErr(LlmClientLib, "StreamPrompt: Exception: %s", e.what());
        callback(L"Error: " + Utf8ToWide(e.what()), true);
        return false;
    }
}

/*++

Routine Description:

    Streams a prompt with context to the LLM service and processes the response in chunks.
    Uses a callback function to handle each chunk as it arrives.

Arguments:

    prompt - The user's prompt to send to the LLM service.
    context - The context information to augment the prompt.
    callback - Callback function that processes each response chunk.

Return Value:

    bool indicating success or failure of the operation.

--*/
bool
LlmClient::StreamPromptWithContext(
    _In_ const std::wstring& prompt,
    _In_ const std::wstring& context,
    _In_ StreamingResponseCallback callback
    )
{
    // Format the prompt with context
    std::wstring formattedPrompt = FormatPromptWithContext(prompt, context);

    // Reuse the existing StreamPrompt method
    return StreamPrompt(formattedPrompt, callback);
}

/*++

Routine Description:

    Performs an HTTP POST request to the LLM endpoint using WinHTTP with streaming.
    Handles URL parsing, connection setup, sending the request, and processing
    the streaming response through a callback function.

Arguments:

    url - The URL of the LLM endpoint.
    data - The request data to send (JSON payload).
    callback - Callback function that processes each response chunk.

Return Value:

    bool indicating success or failure of the HTTP request.

--*/
bool
LlmClient::HttpPostStreaming(
    _In_ const std::wstring& url,
    _In_ const std::wstring& data,
    _In_ StreamingResponseCallback callback
    )
{
    TraceVerb(LlmClientLib, "HttpPostStreaming: Starting streaming HTTP request to: %s", WideToUtf8(url).c_str());

    // Parse the URL
    URL_COMPONENTS urlComp = { 0 };
    urlComp.dwStructSize = sizeof(urlComp);

    // Set up buffers for the URL components
    wchar_t hostName[AimxConstants::BufferSizes::AIMX_URL_HOSTNAME_BUFFER] = { 0 };
    wchar_t urlPath[AimxConstants::BufferSizes::AIMX_URL_PATH_BUFFER] = { 0 };

    urlComp.lpszHostName = hostName;
    urlComp.dwHostNameLength = sizeof(hostName) / sizeof(wchar_t);
    urlComp.lpszUrlPath = urlPath;
    urlComp.dwUrlPathLength = sizeof(urlPath) / sizeof(wchar_t);
    urlComp.dwSchemeLength = 1;

    if (!WinHttpCrackUrl(url.c_str(), static_cast<DWORD>(url.size()), 0, &urlComp))
    {
        TraceErr(LlmClientLib, "HttpPostStreaming: Failed to parse URL: %s", WideToUtf8(url).c_str());
        return false;
    }

    // Initialize WinHTTP
    TraceVerb(LlmClientLib, "HttpPostStreaming: Initializing WinHTTP session");
    HINTERNET hSession = WinHttpOpen(AimxConstants::Http::AIMX_HTTP_USER_AGENT, WINHTTP_ACCESS_TYPE_DEFAULT_PROXY,
                                     WINHTTP_NO_PROXY_NAME, WINHTTP_NO_PROXY_BYPASS, 0);
    if (!hSession)
    {
        TraceErr(LlmClientLib, "HttpPostStreaming: Failed to create WinHTTP session. Error: %lu", GetLastError());
        return false;
    }

    // Connect to the server
    std::string host = WideToUtf8(hostName);
    TraceVerb(LlmClientLib, "HttpPostStreaming: Connecting to server: %s:%d", host.c_str(), urlComp.nPort);
    HINTERNET hConnect = WinHttpConnect(hSession, hostName, urlComp.nPort, 0);
    if (!hConnect)
    {
        TraceErr(LlmClientLib, "HttpPostStreaming: Failed to connect to server: %s. Error: %lu", host.c_str(), GetLastError());
        WinHttpCloseHandle(hSession);
        return false;
    }

    // Create the request
    HINTERNET hRequest = WinHttpOpenRequest(hConnect, AimxConstants::Http::AIMX_HTTP_METHOD_POST, urlPath,
                                           NULL, WINHTTP_NO_REFERER,
                                           WINHTTP_DEFAULT_ACCEPT_TYPES,
                                           urlComp.nScheme == INTERNET_SCHEME_HTTPS ? WINHTTP_FLAG_SECURE : 0);

    if (!hRequest)
    {
        TraceErr(LlmClientLib, "HttpPostStreaming: Failed to create HTTP request. Error: %lu", GetLastError());
        WinHttpCloseHandle(hConnect);
        WinHttpCloseHandle(hSession);
        return false;
    }

    // Set timeouts for streaming (longer than usual)
    DWORD timeout = AimxConstants::Http::AIMX_HTTP_STREAMING_TIMEOUT;
    WinHttpSetOption(hRequest, WINHTTP_OPTION_CONNECT_TIMEOUT, &timeout, sizeof(timeout));
    WinHttpSetOption(hRequest, WINHTTP_OPTION_SEND_TIMEOUT, &timeout, sizeof(timeout));
    WinHttpSetOption(hRequest, WINHTTP_OPTION_RECEIVE_TIMEOUT, &timeout, sizeof(timeout));

    // Set required headers
    std::wstring headers = AimxConstants::Http::AIMX_HTTP_CONTENT_TYPE_JSON;

    // Convert data to UTF-8
    std::string utf8Data = WideToUtf8(data);

    // Send the request with POST data
    TraceVerb(LlmClientLib, "HttpPostStreaming: Sending HTTP request with data length: %d", static_cast<int>(utf8Data.size()));
    if (!WinHttpSendRequest(hRequest, headers.c_str(), static_cast<DWORD>(-1),
                           const_cast<char*>(utf8Data.c_str()), static_cast<DWORD>(utf8Data.size()),
                           static_cast<DWORD>(utf8Data.size()), 0))
    {
        DWORD error = GetLastError();
        TraceErr(LlmClientLib, "HttpPostStreaming: Failed to send HTTP request. Error: %lu (0x%08X)", error, error);

        // Provide more specific error information
        switch (error)
        {
        case ERROR_WINHTTP_CANNOT_CONNECT:
            TraceErr(LlmClientLib, "HttpPostStreaming: Cannot connect to the server. Check if the LLM service is running and the endpoint URL is correct.");
            break;
        case ERROR_WINHTTP_NAME_NOT_RESOLVED:
            TraceErr(LlmClientLib, "HttpPostStreaming: The server name cannot be resolved. Check the hostname in the endpoint URL.");
            break;
        case ERROR_WINHTTP_TIMEOUT:
            TraceErr(LlmClientLib, "HttpPostStreaming: The request timed out. The LLM service may be unresponsive.");
            break;
        case ERROR_WINHTTP_INVALID_URL:
            TraceErr(LlmClientLib, "HttpPostStreaming: Invalid URL format. Check the endpoint URL configuration.");
            break;
        case ERROR_WINHTTP_UNRECOGNIZED_SCHEME:
            TraceErr(LlmClientLib, "HttpPostStreaming: Unrecognized URL scheme. Ensure URL starts with http:// or https://");
            break;
        case ERROR_WINHTTP_CONNECTION_ERROR:
            TraceErr(LlmClientLib, "HttpPostStreaming: Connection error. The LLM service may not be listening on the specified port.");
            break;
        default:
            TraceErr(LlmClientLib, "HttpPostStreaming: Unexpected WinHTTP error. Check network connectivity and firewall settings.");
            break;
        }

        WinHttpCloseHandle(hRequest);
        WinHttpCloseHandle(hConnect);
        WinHttpCloseHandle(hSession);
        return false;
    }

    // Receive the response
    TraceVerb(LlmClientLib, "HttpPostStreaming: Receiving HTTP response");
    if (!WinHttpReceiveResponse(hRequest, NULL))
    {
        TraceErr(LlmClientLib, "HttpPostStreaming: Failed to receive HTTP response. Error: %lu", GetLastError());
        WinHttpCloseHandle(hRequest);
        WinHttpCloseHandle(hConnect);
        WinHttpCloseHandle(hSession);
        return false;
    }

    // Check the HTTP status code
    DWORD statusCode = 0;
    DWORD statusCodeSize = sizeof(statusCode);
    if (WinHttpQueryHeaders(hRequest, WINHTTP_QUERY_STATUS_CODE | WINHTTP_QUERY_FLAG_NUMBER,
                           NULL, &statusCode, &statusCodeSize, NULL))
    {
        TraceVerb(LlmClientLib, "HttpPostStreaming: HTTP status code: %lu", statusCode);

        if (statusCode != AimxConstants::Http::AIMX_HTTP_STATUS_OK)
        {
            // Get more detailed error information if available
            DWORD bufferSize = 0;
            WinHttpQueryHeaders(hRequest, WINHTTP_QUERY_STATUS_TEXT, NULL, NULL, &bufferSize, NULL);

            if (bufferSize > 0 && GetLastError() == ERROR_INSUFFICIENT_BUFFER)
            {
                std::vector<wchar_t> buffer(bufferSize / sizeof(wchar_t));
                if (WinHttpQueryHeaders(hRequest, WINHTTP_QUERY_STATUS_TEXT, NULL, buffer.data(), &bufferSize, NULL))
                {
                    std::wstring statusText(buffer.data(), buffer.size());
                    TraceErr(LlmClientLib, "HttpPostStreaming: HTTP request failed with status code: %lu - %s", statusCode, WideToUtf8(statusText).c_str());
                }
            }
            else
            {
                TraceErr(LlmClientLib, "HttpPostStreaming: HTTP request failed with status code: %lu", statusCode);
            }

            // Try to read the response body for more error details
            std::string errorResponse;
            DWORD bytesAvailable = 0;
            DWORD bytesRead = 0;
            char buffer[4096] = { 0 };

            while (WinHttpQueryDataAvailable(hRequest, &bytesAvailable) && bytesAvailable > 0)
            {
                if (WinHttpReadData(hRequest, buffer, min(sizeof(buffer), static_cast<DWORD>(bytesAvailable)), &bytesRead))
                {
                    if (bytesRead > 0)
                    {
                        errorResponse.append(buffer, bytesRead);
                    }
                }
            }

            if (!errorResponse.empty())
            {
                TraceErr(LlmClientLib, "Error response from server: %s", errorResponse.c_str());
                callback(Utf8ToWide("Server error: " + errorResponse), true);
            }
            else
            {
                callback(Utf8ToWide("Server returned error code: " + std::to_string(statusCode)), true);
            }

            WinHttpCloseHandle(hRequest);
            WinHttpCloseHandle(hConnect);
            WinHttpCloseHandle(hSession);
            return false;
        }
    }

    // Process the streaming response
    TraceVerb(LlmClientLib, "HttpPostStreaming: Processing streaming response");

    // Buffer for reading data
    char buffer[4096];
    DWORD bytesRead = 0;
    DWORD bytesAvailable = 0;
    std::string fullResponse;
    bool success = true;

    // Create a buffer to store incomplete JSON chunks
    std::string jsonBuffer;

    // Read data in chunks as it becomes available
    while (WinHttpQueryDataAvailable(hRequest, &bytesAvailable) && bytesAvailable > 0)
    {
        // Reset the buffer
        ZeroMemory(buffer, sizeof(buffer));

        // Read available data
        if (WinHttpReadData(hRequest, buffer, static_cast<DWORD>(min(sizeof(buffer) - 1, static_cast<size_t>(bytesAvailable))), &bytesRead))
        {
            if (bytesRead > 0)
            {
                // Null-terminate for string operations
                buffer[bytesRead] = '\0';                

                // Add raw data to the jsonBuffer
                jsonBuffer.append(buffer, bytesRead);

                // Process complete JSON objects from the buffer
                size_t dataPrefix = 0;
                size_t lineEnd = 0;

                // Process each line that might contain a complete JSON object
                while ((lineEnd = jsonBuffer.find('\n', dataPrefix)) != std::string::npos)
                {
                    // Extract the line
                    std::string line = jsonBuffer.substr(dataPrefix, lineEnd - dataPrefix);

                    // Update the start position for the next search
                    dataPrefix = lineEnd + 1;

                    // Skip empty lines
                    if (line.empty())
                    {
                        continue;
                    }

                    // Handle "data:" prefix (server-sent events format)
                    if (line.compare(0, strlen(AimxConstants::LlmProtocol::AIMX_SSE_DATA_PREFIX), AimxConstants::LlmProtocol::AIMX_SSE_DATA_PREFIX) == 0)
                    {
                        line = line.substr(strlen(AimxConstants::LlmProtocol::AIMX_SSE_DATA_PREFIX));
                    }

                    // Handle special markers
                    if (line == AimxConstants::LlmProtocol::AIMX_LLM_DONE_MARKER)
                    {
                        TraceVerb(LlmClientLib, "HttpPostStreaming: Received [DONE] marker");
                        continue;
                    }

                    // Process JSON content
                    try
                    {
                        // Trim whitespace from the line
                        size_t start = line.find_first_not_of(AimxConstants::LlmResponseParsing::AIMX_TRIM_CHARS);
                        if (start != std::string::npos)
                        {
                            size_t end = line.find_last_not_of(AimxConstants::LlmResponseParsing::AIMX_TRIM_CHARS);
                            line = line.substr(start, end - start + 1);
                        }
                        else
                        {
                            continue; // Skip whitespace-only lines
                        }

                        // Only attempt to parse if it looks like valid JSON and has minimum length
                        if (!line.empty() && line.length() > 1 && (line[0] == '{' || line[0] == '['))
                        {
                            // Parse the JSON
                            auto jsonObj = nlohmann::json::parse(line);

                            // Extract content based on known response formats
                            std::string chunkContent;

                            if (jsonObj.contains(AimxConstants::JsonFields::AIMX_JSON_KEY_CONTENT) && jsonObj[AimxConstants::JsonFields::AIMX_JSON_KEY_CONTENT].is_string())
                            {
                                // Direct content field
                                chunkContent = jsonObj[AimxConstants::JsonFields::AIMX_JSON_KEY_CONTENT].get<std::string>();
                            }
                            else if (jsonObj.contains(AimxConstants::JsonFields::AIMX_JSON_KEY_CHOICES) && jsonObj[AimxConstants::JsonFields::AIMX_JSON_KEY_CHOICES].is_array() && !jsonObj[AimxConstants::JsonFields::AIMX_JSON_KEY_CHOICES].empty())
                            {
                                auto& choices = jsonObj[AimxConstants::JsonFields::AIMX_JSON_KEY_CHOICES];

                                // Try various formats used by different models
                                if (choices[0].contains(AimxConstants::JsonFields::AIMX_JSON_KEY_DELTA) && choices[0][AimxConstants::JsonFields::AIMX_JSON_KEY_DELTA].contains(AimxConstants::JsonFields::AIMX_JSON_KEY_CONTENT))
                                {
                                    chunkContent = choices[0][AimxConstants::JsonFields::AIMX_JSON_KEY_DELTA][AimxConstants::JsonFields::AIMX_JSON_KEY_CONTENT].get<std::string>();
                                }
                                else if (choices[0].contains(AimxConstants::JsonFields::AIMX_JSON_KEY_TEXT))
                                {
                                    chunkContent = choices[0][AimxConstants::JsonFields::AIMX_JSON_KEY_TEXT].get<std::string>();
                                }
                                else if (choices[0].contains(AimxConstants::JsonFields::AIMX_JSON_KEY_CONTENT))
                                {
                                    chunkContent = choices[0][AimxConstants::JsonFields::AIMX_JSON_KEY_CONTENT].get<std::string>();
                                }
                            }
                            else if (jsonObj.contains(AimxConstants::JsonFields::AIMX_JSON_KEY_RESPONSE))
                            {
                                chunkContent = jsonObj[AimxConstants::JsonFields::AIMX_JSON_KEY_RESPONSE].get<std::string>();
                            }

                            // If we got content, process it
                            if (!chunkContent.empty())
                            {
                                // Add to full response
                                fullResponse += chunkContent;

                                // Send it to the client
                                TraceVerb(LlmClientLib, "HttpPostStreaming: Extracted content from JSON: %s", chunkContent.c_str());
                                callback(Utf8ToWide(chunkContent), false);
                            }
                            else
                            {
                                TraceVerb(LlmClientLib, "HttpPostStreaming: JSON object with no content: %s", line.c_str());
                            }
                        }
                        else
                        {
                            // Handle plain text chunks
                            TraceVerb(LlmClientLib, "HttpPostStreaming: Plain text chunk: %s", line.c_str());
                            fullResponse += line;
                            callback(Utf8ToWide(line), false);
                        }
                    }
                    catch (const std::exception& e)
                    {
                        TraceErr(LlmClientLib, "HttpPostStreaming: JSON parsing error: %s for line: %s", e.what(), line.c_str());

                        // If it's not JSON but looks like human-readable text, send it anyway
                        if (line.find_first_not_of(" \t\r\n") != std::string::npos &&
                            line.find_first_of("{}[]") == std::string::npos)
                        {
                            fullResponse += line;
                            callback(Utf8ToWide(line), false);
                        }
                    }
                }

                // Keep any remaining incomplete data in the buffer
                jsonBuffer.erase(0, dataPrefix);
            }
        }
        else
        {
            TraceErr(LlmClientLib, "HttpPostStreaming: Error reading data. Error: %lu", GetLastError());
            success = false;
            break;
        }
    }

    // Process any remaining data in the buffer
    if (!jsonBuffer.empty())
    {
        TraceVerb(LlmClientLib, "HttpPostStreaming: Processing remaining buffer: %s", jsonBuffer.c_str());

        try
        {
            // Trim whitespace from the buffer
            size_t start = jsonBuffer.find_first_not_of(" \t\r\n");
            if (start != std::string::npos)
            {
                size_t end = jsonBuffer.find_last_not_of(" \t\r\n");
                jsonBuffer = jsonBuffer.substr(start, end - start + 1);
            }
            else
            {
                jsonBuffer.clear(); // Buffer contains only whitespace
            }

            // Try to parse as JSON if it looks like valid JSON and has minimum length
            if (!jsonBuffer.empty() && jsonBuffer.length() > 1 && (jsonBuffer[0] == '{' || jsonBuffer[0] == '['))
            {
                auto jsonObj = nlohmann::json::parse(jsonBuffer);

                // Extract content if available
                std::string chunkContent;
                if (jsonObj.contains(AimxConstants::JsonFields::AIMX_JSON_KEY_CONTENT) && jsonObj[AimxConstants::JsonFields::AIMX_JSON_KEY_CONTENT].is_string())
                {
                    chunkContent = jsonObj[AimxConstants::JsonFields::AIMX_JSON_KEY_CONTENT].get<std::string>();

                    // Add to full response
                    fullResponse += chunkContent;

                    // Send it to the client
                    TraceVerb(LlmClientLib, "HttpPostStreaming: Extracted content from final JSON: %s", chunkContent.c_str());
                    callback(Utf8ToWide(chunkContent), false);
                }
            }
            else if (jsonBuffer.find_first_not_of(" \t\r\n") != std::string::npos)
            {
                // If it's plain text, send it
                fullResponse += jsonBuffer;
                callback(Utf8ToWide(jsonBuffer), false);
            }
        }
        catch (const std::exception& e)
        {
            TraceErr(LlmClientLib, "HttpPostStreaming: Error processing remaining buffer: %s", e.what());

            // If it looks like text, send it anyway
            if (jsonBuffer.find_first_not_of(" \t\r\n") != std::string::npos &&
                jsonBuffer.find_first_of("{}[]") == std::string::npos)
            {
                fullResponse += jsonBuffer;
                callback(Utf8ToWide(jsonBuffer), false);
            }
        }
    }

    // Signal completion
    callback(L"", true);

    // Clean up
    WinHttpCloseHandle(hRequest);
    WinHttpCloseHandle(hConnect);
    WinHttpCloseHandle(hSession);

    TraceVerb(LlmClientLib, "HttpPostStreaming: Completed. Success: %s", success ? "true" : "false");
    return success;
}

/*++

Routine Description:

    Returns the LLM endpoint URL as a UTF-8 string.

Arguments:

    None.

Return Value:

    std::Wstring containing the endpoint URL.

--*/
std::wstring
LlmClient::GetEndpointUrl() const
{
    return m_endpointUrl;
}

/*++

Routine Description:

    Returns the LLM endpoint URL as a UTF-8 string.

Arguments:

    None.

Return Value:

    std::string containing the endpoint URL in UTF-8 format.

--*/
std::string
LlmClient::GetEndpointUrlUtf8() const
{
    return WideToUtf8(m_endpointUrl);
}

void
LlmClient::SetEndpointUrl(
    _In_ const  std::wstring& url
)
{
    m_endpointUrl = url;
}

/*++

Routine Description:

    Returns the current model name.

Arguments:

    None.

Return Value:

    std::wstring containing the model name.

--*/
std::wstring
LlmClient::GetModel() const
{
    return m_model;
}

/*++

Routine Description:

    Sets the model name.

Arguments:

    model - The model name to set.

Return Value:

    None.

--*/
void
LlmClient::SetModel(
    _In_ const std::wstring& model
)
{
    m_model = model;
}

/*++

Routine Description:

    Returns the current temperature setting.

Arguments:

    None.

Return Value:

    float containing the temperature value.

--*/
float
LlmClient::GetTemperature() const
{
    return m_temperature;
}

/*++

Routine Description:

    Sets the temperature setting.

Arguments:

    temperature - The temperature value to set.

Return Value:

    None.

--*/
void
LlmClient::SetTemperature(
    _In_ float temperature
)
{
    m_temperature = temperature;
}

/*++

Routine Description:

    Returns the current top-K setting.

Arguments:

    None.

Return Value:

    int containing the top-K value.

--*/
int
LlmClient::GetTopK() const
{
    return m_topK;
}

/*++

Routine Description:

    Sets the top-K setting.

Arguments:

    topK - The top-K value to set.

Return Value:

    None.

--*/
void
LlmClient::SetTopK(
    _In_ int topK
)
{
    m_topK = topK;
}

/*++

Routine Description:

    Returns the current top-P setting.

Arguments:

    None.

Return Value:

    float containing the top-P value.

--*/
float
LlmClient::GetTopP() const
{
    return m_topP;
}

/*++

Routine Description:

    Sets the top-P setting.

Arguments:

    topP - The top-P value to set.

Return Value:

    None.

--*/
void
LlmClient::SetTopP(
    _In_ float topP
)
{
    m_topP = topP;
}

/*++

Routine Description:

    Sets all LLM configuration parameters at once.

Arguments:

    endpointUrl - The URL of the LLM service endpoint.
    model - The model name to use.
    temperature - The temperature setting for the LLM.
    topK - The top-K setting for the LLM.
    topP - The top-P setting for the LLM.

Return Value:

    None.

--*/
void
LlmClient::SetLlmConfiguration(
    _In_ const std::wstring& endpointUrl,
    _In_ const std::wstring& model,
    _In_ float temperature,
    _In_ int topK,
    _In_ float topP
)
{
    m_endpointUrl = endpointUrl;
    m_model = model;
    m_temperature = temperature;
    m_topK = topK;
    m_topP = topP;
}
