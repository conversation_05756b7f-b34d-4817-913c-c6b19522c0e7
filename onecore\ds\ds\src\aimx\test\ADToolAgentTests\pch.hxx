/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    pch.hxx

Abstract:
    Precompiled header for ADToolAgentTestTaef test suite.

Author:
    <PERSON><PERSON><PERSON><PERSON> (pumathur) 06/18/2025

--*/
#pragma once

// Base OS headers
#include <nt.h>
#include <ntrtl.h>
#include <nturtl.h>
#include <ntseapi_x.h>
#include <ntdef.h>
#include <ntintsafe.h>
#include <ntstrsafe.h>
#include <windows.h>
#include <wchar.h>
#include <stdio.h>
#include <stdlib.h>
#include <rpc.h>
#include <rpcdce.h>

// C++ Standard Library headers
#include <vector>
#include <string>
#include <memory>
#include <iostream>
#include <thread>
#include <atomic>
#include <psapi.h>  // For GetProcessMemoryInfo and PROCESS_MEMORY_COUNTERS_EX
#include <nlohmann/json.hpp>  // Include JSON library

// Project headers
#include "..\..\prod\Agents\ADToolAgent\ADToolAgent.h"
#include "..\..\prod\Agents\ADToolAgent\ADReplicationToolAgent.h"
#include "AimxConstants.h"