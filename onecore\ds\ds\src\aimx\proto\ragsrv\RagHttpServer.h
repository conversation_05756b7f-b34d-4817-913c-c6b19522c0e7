/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    RagHttpServer.h

Abstract:

    This module defines the HTTP server class for the RAG service.
    Provides HTTP endpoints for RAG database operations.

Author:

    <PERSON><PERSON><PERSON> (r<PERSON><PERSON>) 04/12/2025

--*/

#pragma once

#ifndef _WIN64
#pragma warning(push)
#pragma warning (disable:4244)
#endif // _WIN64

#include "../common/httplib/httplib.h"

#ifndef _WIN64
#pragma warning(pop)
#endif // _WIN64

#include <string>
#include <memory>
#include <thread>
#include <atomic>
#include <mutex>
#include <vector>
#include <map>

// Forward declarations
class RagDatabaseBuilder;
class RagJobManager;

class RagHttpServer 
{
public:
    RagHttpServer();
    ~RagHttpServer();
    
    // Start the server
    bool 
    Start(
        _Inout_ std::atomic<bool>& runningFlag
        );
    
    // Stop the server
    void 
    Stop();

private:
    // Register API endpoints
    void 
    RegisterEndpoints();
    
    // HTTP endpoint handlers
    void 
    HandleBuildRequest(
        _In_ const httplib::Request& req, 
        _Out_ httplib::Response& res
        );
    
    void 
    HandleStatusRequest(
        _In_ const httplib::Request& req, 
        _Out_ httplib::Response& res
        );
    
    void 
    HandleJobsListRequest(
        _In_ const httplib::Request& req, 
        _Out_ httplib::Response& res
        );
    
    void 
    HandleTestRequest(
        _In_ const httplib::Request& req, 
        _Out_ httplib::Response& res
        );
    
    void 
    HandleHealthRequest(
        _In_ const httplib::Request& req, 
        _Out_ httplib::Response& res
        );
    
    // Server thread function
    void 
    RunServerThread(
        _Inout_ std::atomic<bool>& runningFlag
        );
    
    // Server configuration
    std::string m_host;
    int m_port;
    bool m_initialized;
    bool m_running;
    
    // Thread management
    std::thread m_serverThread;
    std::mutex m_mutex;
    
    // HTTP server instance
    std::unique_ptr<httplib::Server> m_server;
    
    // RAG components
    std::unique_ptr<RagDatabaseBuilder> m_ragBuilder;
    std::unique_ptr<RagJobManager> m_jobManager;
};