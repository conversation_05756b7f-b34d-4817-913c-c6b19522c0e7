/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    ADToolAgentTests.h

Abstract:
    TAEF test class declaration for ADToolAgent component tests.

Author:
    <PERSON><PERSON><PERSON><PERSON> (pumathur) 06/18/2025

--*/
#pragma once

#include "WexTestClass.h"

using namespace WEX::Logging;
using namespace WEX::Common;
using namespace WEX::TestExecution;

class ADToolAgentTests : public WEX::TestClass<ADToolAgentTests>
{

public:
    BEGIN_TEST_CLASS(ADToolAgentTests)
        TEST_CLASS_PROPERTY(L"TestClass", L"ADToolAgent")
        TEST_CLASS_PROPERTY(L"Description", L"Tests for the ADToolAgent component")
        END_TEST_CLASS()
        
        // Test singleton instance creation and basic functionality
    BEGIN_TEST_METHOD(TestSingletonInstance)
        TEST_METHOD_PROPERTY(L"Priority", L"1")
        TEST_METHOD_PROPERTY(L"Description", L"Tests singleton pattern implementation")
    END_TEST_METHOD()

    // Test the ExecuteAction method with various inputs
    BEGIN_TEST_METHOD(TestExecuteAction)
        TEST_METHOD_PROPERTY(L"Priority", L"1")
        TEST_METHOD_PROPERTY(L"Description", L"Tests the ExecuteAction method with various inputs")
    END_TEST_METHOD()

    // Test AD Area classification
    BEGIN_TEST_METHOD(TestADAreaClassification)
        TEST_METHOD_PROPERTY(L"Priority", L"2")
        TEST_METHOD_PROPERTY(L"Description", L"Tests the classification of AD areas from string input")
    END_TEST_METHOD()

    // Test command execution functionality
    BEGIN_TEST_METHOD(TestRunCommand)
        TEST_METHOD_PROPERTY(L"Priority", L"2")
        TEST_METHOD_PROPERTY(L"Description", L"Tests the RunCommand method")
    END_TEST_METHOD()
      // Test concurrent execution of multiple requests
    BEGIN_TEST_METHOD(TestConcurrentExecution)
        TEST_METHOD_PROPERTY(L"Priority", L"3")
        TEST_METHOD_PROPERTY(L"Description", L"Tests the thread safety of ADToolAgent with concurrent requests")
    END_TEST_METHOD()
    
    // Test edge cases and input validation for ExecuteAction
    BEGIN_TEST_METHOD(TestExecuteActionEdgeCases)
        TEST_METHOD_PROPERTY(L"Priority", L"2")
        TEST_METHOD_PROPERTY(L"Description", L"Tests ExecuteAction with edge cases and unusual inputs")
    END_TEST_METHOD()
      // Test error recovery capabilities
    BEGIN_TEST_METHOD(TestErrorRecovery)
        TEST_METHOD_PROPERTY(L"Priority", L"3")
        TEST_METHOD_PROPERTY(L"Description", L"Tests ADToolAgent's ability to recover from errors")
    END_TEST_METHOD()
      // Test performance characteristics
    BEGIN_TEST_METHOD(TestPerformance)
        TEST_METHOD_PROPERTY(L"Priority", L"3")
        TEST_METHOD_PROPERTY(L"Description", L"Tests performance of ADToolAgent operations")
    END_TEST_METHOD()
    
    // Utility test for AD environment discovery and integration
    BEGIN_TEST_METHOD(TestADEnvironmentDiscovery)
        TEST_METHOD_PROPERTY(L"Priority", L"2")
        TEST_METHOD_PROPERTY(L"Description", L"Tests discovery of AD environment and integration with AD components")
    END_TEST_METHOD()

private:
    // Helper method to execute PowerShell command that gets AD domain controller info
    bool GetDomainControllerInfo(ADToolAgent& agent, std::wstring& dcName);
};