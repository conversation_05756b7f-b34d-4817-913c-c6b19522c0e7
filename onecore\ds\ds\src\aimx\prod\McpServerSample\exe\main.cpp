/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    main.cpp

Abstract:

    Main entry point for the Hello MCP Server executable.
    Implements a stdio-based MCP server that reuses the core logic from HelloMcpServer
    for out-of-process execution via JSON-RPC over stdin/stdout.

Author:

    <PERSON><PERSON><PERSON> (r<PERSON><PERSON>) 07/19/2025

Note:

    How to test the MCP server manually in console: 

    helloMcpServer.exe '{"jsonrpc":"2.0","id":1,"method":"initialize"}'
    helloMcpServer.exe '{"jsonrpc":"2.0","id":2,"method":"tools/list"}'
    helloMcpServer.exe '{"jsonrpc":"2.0","id":3,"method":"tools/call","params":{"name":"HelloFromMcpTool","arguments":{"name":"WSD CFE WSAI Team"}}}'

--*/

#include <windows.h>
#include <objbase.h>
#include <iostream>
#include <string>
#include <sstream>
#include <atomic>
#include <map>
#include <nlohmann/json.hpp>
#include "McpServerUtils.h"
#include "HelloMcpServer.h"
#include "StringUtils.h"

// Global state
std::atomic<bool> g_running(true);
std::unique_ptr<HelloMcpServer> g_server;

// Tool registry
std::map<std::string, McpProtocol::Server::ToolFunction<HelloMcpServer>> g_toolRegistry =
{
    // {toolName, toolFunction}
    {"HelloFromMcpTool", &HelloMcpServer::HelloFromMcpTool}
};

// Initialize global server instance using shared utilities
HRESULT InitializeGlobalServer()
{
    return McpProtocol::Server::InitializeServer(g_server);
}

// Process a single JSON-RPC request using shared library
void ProcessRequest(const std::string& requestLine)
{
    // Initialize server if needed
    HRESULT hr = InitializeGlobalServer();
    if (FAILED(hr))
    {
        std::cout << L"Server initialization failed" << std::endl;
        return;
    }

    // Create method handlers
    auto methodHandlers = McpProtocol::Server::CreateStandardHandlers(g_server.get(), g_toolRegistry);

    // Custom shutdown handler that sets the running flag
    methodHandlers[McpProtocol::Mcp::Methods::SHUTDOWN] = [](const nlohmann::json& params) {
        g_running = false;
        return McpProtocol::Server::HandleShutdown(params);
    };

    // Process request
    std::string response = McpProtocol::Server::ProcessRequest(requestLine, methodHandlers);
    std::cout << response << std::endl;
    std::cout.flush();
}

// Main entry point
int __cdecl wmain(int argc, wchar_t* argv[])
{
    // Initialize COM (required for some Windows APIs)
    HRESULT hr = CoInitializeEx(nullptr, COINIT_APARTMENTTHREADED);
    if (FAILED(hr))
    {
        std::wcerr << L"Failed to initialize COM: " << std::hex << hr << std::endl;
        return 1;
    }

    try
    {
        // Check if we have command line arguments for JSON-RPC input
        if (argc >= 2)
        {
            // Process JSON-RPC request from command line argument
            std::wstring jsonRpcInput = argv[1];

            // Convert wide string to UTF-8 for JSON processing
            std::string jsonRpcInputUtf8 = WideToUtf8(jsonRpcInput);

            // Process the single request
            ProcessRequest(jsonRpcInputUtf8);
        }
        else
        {
            // Fallback to stdin mode for interactive testing
            std::string line;
            while (g_running && std::getline(std::cin, line))
            {
                if (!line.empty())
                {
                    ProcessRequest(line);
                }
            }
        }
    }
    catch (const std::exception& e)
    {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        CoUninitialize();
        return 1;
    }

    CoUninitialize();
    return 0;
}
