/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    GetADUserTool.cpp

Abstract:

    Implementation of Get-ADUser tool for Active Directory MCP server.
    Provides comprehensive AD user query functionality using native Win32 LDAP APIs
    while maintaining PowerShell cmdlet parameter compatibility.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 2025-7-16

--*/

#include "AdMcpSvr.h"
#include "StringUtils.h"
#include "AimxConstants.h"
#include "../aimxsrv/inc/wpp.h"

// WPP tracing
#include "GetADUserTool.cpp.tmh"

HRESULT AdMcpSvr::GetADUserTool(
    _In_ const nlohmann::json& parameters,
    _Out_ nlohmann::json& result
    )
/*++

Routine Description:
    Implementation of Get-ADUser tool.

Arguments:
    parameters - Input parameters matching PowerShell Get-ADUser cmdlet
    result - Receives user query results as JSON

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    TraceInfo(AdMcpSvr, L"AdMcpSvr::GetADUserTool called");

    try
    {
        result = nlohmann::json::object();

        // Initialize LDAP connection if needed
        std::wstring serverName;
        if (parameters.contains("Server") && parameters["Server"].is_string())
        {
            serverName = Utf8ToWide(parameters["Server"].get<std::string>());
        }

        HRESULT hr = InitializeLdapConnection(serverName);
        if (FAILED(hr))
        {
            return CreateLdapErrorResponse(hr, L"Failed to initialize LDAP connection", result);
        }

        // Parse search parameters
        std::wstring searchBase = m_defaultNamingContext;
        if (parameters.contains("SearchBase") && parameters["SearchBase"].is_string())
        {
            searchBase = Utf8ToWide(parameters["SearchBase"].get<std::string>());
        }

        std::wstring filter = L"(objectClass=user)";
        if (parameters.contains("Identity") && parameters["Identity"].is_string())
        {
            std::wstring identity = Utf8ToWide(parameters["Identity"].get<std::string>());
            // Build filter for identity - could be DN, GUID, SID, or SAM account name
            filter = L"(|(distinguishedName=" + identity + L")(objectGUID=" + identity +
                    L")(objectSid=" + identity + L")(sAMAccountName=" + identity + L"))";
        }
        else if (parameters.contains("Filter") && parameters["Filter"].is_string())
        {
            hr = ParseFilterParameter(parameters["Filter"], filter);
            if (FAILED(hr))
            {
                return CreateLdapErrorResponse(hr, L"Failed to parse Filter parameter", result);
            }
        }
        else if (parameters.contains("LDAPFilter") && parameters["LDAPFilter"].is_string())
        {
            filter = Utf8ToWide(parameters["LDAPFilter"].get<std::string>());
        }

        // Parse attributes to retrieve
        std::vector<std::wstring> attributes = GetDefaultUserAttributes();
        if (parameters.contains("Properties"))
        {
            hr = ParsePropertiesParameter(parameters["Properties"], attributes);
            if (FAILED(hr))
            {
                return CreateLdapErrorResponse(hr, L"Failed to parse Properties parameter", result);
            }
        }

        // Parse search scope
        ULONG searchScope = LDAP_SCOPE_SUBTREE;
        if (parameters.contains("SearchScope"))
        {
            hr = ParseSearchScopeParameter(parameters["SearchScope"], searchScope);
            if (FAILED(hr))
            {
                return CreateLdapErrorResponse(hr, L"Failed to parse SearchScope parameter", result);
            }
        }

        // Execute LDAP search
        std::vector<nlohmann::json> users;
        hr = ExecuteLdapSearch(searchBase, filter, attributes, searchScope, users);
        if (FAILED(hr))
        {
            return CreateLdapErrorResponse(hr, L"LDAP search failed", result);
        }

        result["users"] = users;
        result["count"] = users.size();
        result["server"] = WideToUtf8(m_domainController);

        TraceInfo(AdMcpSvr, L"AdMcpSvr::GetADUserTool completed successfully, found %d users", static_cast<int>(users.size()));
        return S_OK;
    }
    catch (const std::exception& ex)
    {
        TraceErr(AdMcpSvr, L"Exception in GetADUserTool: %s", ex.what());
        result = CreateErrorResponse(L"Failed to execute Get-ADUser: " + Utf8ToWide(ex.what()), L"execution_error");
        return E_FAIL;
    }
}

std::vector<std::wstring> AdMcpSvr::GetDefaultUserAttributes()
/*++

Routine Description:
    Get default attributes for user objects.

Return Value:
    Vector of default user attribute names

--*/
{
    return {
        L"distinguishedName", L"objectGUID", L"objectSid", L"sAMAccountName",
        L"name", L"givenName", L"sn", L"displayName", L"userPrincipalName",
        L"mail", L"enabled", L"objectClass", L"whenCreated", L"whenChanged",
        L"lastLogonTimestamp", L"pwdLastSet", L"userAccountControl"
    };
}
