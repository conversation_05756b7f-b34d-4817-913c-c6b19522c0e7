/*
 * AD Health Dashboard styles
 * Custom styling for the Active Directory Health Dashboard
 */

.dashboard-container {
    padding: 20px;
    height: 100%;
    overflow-y: auto;
    background-color: var(--primary-bg);
    color: var(--primary-text);
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dashboard-header h1 {
    margin: 0;
    font-size: 24px;
    color: var(--accent-color);
}

.dashboard-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 10px;
}

@media (min-width: 768px) {
    .dashboard-controls {
        margin-top: 0;
    }
}

.timeframe-selector {
    display: flex;
    background-color: var(--tertiary-bg);
    border-radius: 4px;
    overflow: hidden;
}

.timeframe-btn {
    background-color: transparent;
    border: none;
    color: var(--primary-text);
    padding: 8px 12px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s;
}

.timeframe-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.timeframe-btn.active {
    background-color: var(--accent-color);
    color: white;
}

.control-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 12px;
    background-color: var(--tertiary-bg);
    color: var(--primary-text);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.control-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.primary-btn {
    background-color: var(--accent-color);
    color: white;
}

.primary-btn:hover {
    background-color: rgba(0, 120, 215, 0.8);
}

.refresh-btn {
    position: relative;
}

.refresh-btn .icon {
    transition: transform 0.5s;
}

.refresh-btn.refreshing .icon {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.dashboard-overview {
    margin-bottom: 20px;
}

.overview-card {
    background-color: var(--secondary-bg);
    border-radius: var(--border-radius);
    padding: 15px;
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 10px;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    color: var(--secondary-text);
}

.health-indicator {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
}

.health-good {
    background-color: #4CAF50;
    color: white;
}

.health-warning {
    background-color: #FFC107;
    color: black;
}

.health-critical {
    background-color: #F44336;
    color: white;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    /* Ensure grid items don't overflow */
    min-width: 0;
}

.dashboard-card {
    background-color: var(--secondary-bg);
    border-radius: var(--border-radius);
    padding: 15px;
    height: 300px;
    display: flex;
    flex-direction: column;
    /* Prevent content overflow */
    overflow: hidden;
    min-width: 0;
}

.dashboard-card h2 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 500;
    /* Fixed header height to prevent it from expanding */
    height: 20px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.wide-card {
    grid-column: span 2;
}

.chart-container {
    flex-grow: 1;
    position: relative;
    /* Important constraint to prevent overflow */
    width: 100%;
    height: calc(100% - 35px); /* Account for header height */
    /* Make sure content doesn't overflow */
    overflow: hidden;
}

/* Ensure canvas takes up available space but doesn't overflow */
.chart-container canvas {
    max-width: 100%;
    max-height: 100%;
}

/* Specific chart adjustments */
#serverStatusChart, #securityIncidentsChart {
    /* Give more room for legends on right */
    padding-right: 10px;
}

#replicationChart {
    /* Adjust gauge-type charts to center nicely */
    max-height: 90%;
    margin: 0 auto;
}

/* Responsive grid adjustments */
@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-card {
        height: 250px; /* Smaller on mobile */
    }
}

.map-container {
    height: 250px;
}

.alerts-container,
.issues-container {
    flex-grow: 1;
    overflow-y: auto;
}

.alert-placeholder,
.issue-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--secondary-text);
    font-style: italic;
}

.alert-item {
    padding: 10px;
    margin-bottom: 8px;
    border-radius: 4px;
    border-left: 4px solid transparent;
}

.alert-item:last-child {
    margin-bottom: 0;
}

.alert-critical {
    background-color: rgba(244, 67, 54, 0.1);
    border-left-color: #F44336;
}

.alert-warning {
    background-color: rgba(255, 152, 0, 0.1);
    border-left-color: #FF9800;
}

.alert-info {
    background-color: rgba(33, 150, 243, 0.1);
    border-left-color: #2196F3;
}

.alert-title {
    font-weight: 500;
    margin-bottom: 5px;
}

.alert-details {
    font-size: 13px;
    color: var(--secondary-text);
    margin-bottom: 5px;
}

.alert-timestamp {
    font-size: 12px;
    color: var(--secondary-text);
    text-align: right;
}

.issue-item {
    padding: 10px;
    margin-bottom: 8px;
    background-color: rgba(244, 67, 54, 0.1);
    border-radius: 4px;
}

.issue-title {
    font-weight: 500;
    margin-bottom: 5px;
}

.issue-details {
    font-size: 13px;
    color: var(--secondary-text);
    margin-bottom: 8px;
}

.issue-action {
    font-size: 13px;
    color: var(--accent-color);
    font-weight: 500;
}

/* Report Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background-color: var(--secondary-bg);
    border-radius: 8px;
    width: 500px;
    max-width: 90%;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: var(--tertiary-bg);
}

.modal-header h2 {
    margin: 0;
    font-size: 18px;
}

.close-button {
    background: transparent;
    border: none;
    color: var(--primary-text);
    font-size: 24px;
    cursor: pointer;
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group select {
    width: 100%;
    padding: 8px;
    background-color: var(--tertiary-bg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    color: var(--primary-text);
}

.checkbox-group {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
}

.checkbox-group label {
    font-weight: normal;
    display: flex;
    align-items: center;
    gap: 5px;
}

.modal-footer {
    padding: 15px 20px;
    background-color: var(--tertiary-bg);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.primary-btn, .cancel-btn {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.primary-btn {
    background-color: var(--accent-color);
    color: white;
}

.cancel-btn {
    background-color: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--primary-text);
}

/* Notification */
#notificationContainer {
    position: fixed;
    top: 20px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    z-index: 1100;
    pointer-events: none;
}

.notification {
    background-color: var(--accent-color);
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 10px;
    pointer-events: auto;
}

.notification .spinner {
    width: 16px;
    height: 16px;
    border: 2px solid white;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}
