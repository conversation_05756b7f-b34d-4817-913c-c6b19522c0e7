/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    PshManager.cpp

Abstract:

    PowerShell manager for execution and command search via NetRag service.

--*/

#include "pch.hxx"
#include "PshManager.h"
#include "StringUtils.h"
#include "AimxConstants.h"
#include <fstream>
#include <sstream>
#include <set>
#include <algorithm>
#include <cpprest/http_client.h>

#include "PshManager.cpp.tmh"

#define HRESULT_FROM_WIN32(x)       ((HRESULT)(x) <= 0 ? ((HRESULT)(x)) : ((HRESULT) (((x) & 0x0000FFFF) | (FACILITY_WIN32 << 16) | 0x80000000)))

//
// Static member variable definitions
//
std::unordered_map<std::wstring, nlohmann::json> PshManager::s_powerShellContextCache;
std::shared_mutex PshManager::s_contextCacheMutex;
bool PshManager::s_contextDataLoaded = false;

HRESULT
PshManager::ExecuteTool(
    _In_ const std::wstring& toolName,
    _In_ const nlohmann::json& parameters,
    _Out_ POWERSHELL_EXECUTION_RESULT& result
    )
/*++

Routine Description:
    Execute PowerShell tool via HTTP to NetRag service.

--*/
{
    // Build PowerShell command
    std::wstring psCommand;
    HRESULT hr = BuildPowerShellCommand(toolName, parameters, psCommand);
    if (FAILED(hr))
    {
        result.success = false;
        result.errorMessage = L"Failed to build PowerShell command";
        return hr;
    }

    // Execute via NetRag service HTTP endpoint
    nlohmann::json httpResponse;
    hr = ExecutePowerShellViaHttp(psCommand, 120, httpResponse);
    if (FAILED(hr))
    {
        result.success = false;
        result.errorMessage = L"NetRag service execution failed";
        return hr;
    }

    // Parse the response
    return ParseNetRagResponse(httpResponse, result);
}

HRESULT
PshManager::SearchPowerShellCommands(
    _In_ const std::wstring& query,
    _In_ int topK,
    _Out_ std::vector<POWERSHELL_COMMAND_SEARCH_RESULT>& results
    )
/*++

Routine Description:
    Search for PowerShell commands with full context using semantic similarity.

Arguments:
    query - Search query string
    topK - Number of top results to return
    results - Output vector of PowerShell command search results

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxPshManager, "=== POWERSHELL COMMAND SEARCH START ===");
    TraceInfo(AimxPshManager, "Query: %s", WideToUtf8(query).c_str());
    TraceInfo(AimxPshManager, "TopK: %d", topK);

    try
    {
        // Validate input parameters
        if (query.empty())
        {
            TraceErr(AimxPshManager, "Search query is empty");
            return E_INVALIDARG;
        }

        if (topK <= 0 || topK > 20)
        {
            TraceErr(AimxPshManager, "Invalid topK value: %d (must be 1-20)", topK);
            return E_INVALIDARG;
        }

        // URL encode the query parameter properly
        std::wstring queryWide = query;
        std::wstring encodedQuery;

        try
        {
            encodedQuery = web::http::uri::encode_uri(queryWide);
            TraceInfo(AimxPshManager, "URL encoded query: %s", WideToUtf8(encodedQuery).c_str());
        }
        catch (const std::exception& e)
        {
            TraceErr(AimxPshManager, "Failed to URL encode query: %s", e.what());
            return E_FAIL;
        }
        catch (...)
        {
            TraceErr(AimxPshManager, "Unknown exception during URL encoding");
            return E_FAIL;
        }

        // Create search request URL with query parameters using centralized constants
        std::wstring endpoint = std::wstring(AimxConstants::ApiEndpoints::AIMX_POWERSHELL_COMMAND_SEARCH) +
                               L"?" + AimxConstants::QueryParameters::AIMX_QUERY_PARAM + L"=" + encodedQuery +
                               L"&" + AimxConstants::QueryParameters::AIMX_LIMIT_PARAM + L"=" + std::to_wstring(topK);
        TraceInfo(AimxPshManager, "Search endpoint: %s", WideToUtf8(endpoint).c_str());

        nlohmann::json response;
        TraceInfo(AimxPshManager, "Sending HTTP GET request to NetRag service...");
        HRESULT hr = SendHttpGetRequest(endpoint, response);
        if (FAILED(hr))
        {
            TraceErr(AimxPshManager, "SendHttpGetRequest failed with HRESULT: %!HRESULT!", hr);
            TraceErr(AimxPshManager, "This could indicate:");
            TraceErr(AimxPshManager, "  1. NetRag service is not running on localhost:5000");
            TraceErr(AimxPshManager, "  2. Network connectivity issues");
            TraceErr(AimxPshManager, "  3. HTTP client configuration problems");
            TraceErr(AimxPshManager, "  4. Service returned non-200 status code");
            TraceErr(AimxPshManager, "  5. JSON parsing failure");
            return hr;
        }

        TraceInfo(AimxPshManager, "HTTP request successful, parsing response...");

        // Log the raw response for debugging
        try
        {
            std::string responseStr = response.dump(2);
            TraceInfo(AimxPshManager, "Raw JSON response (first 500 chars): %s",
                     responseStr.length() > 500 ? responseStr.substr(0, 500).c_str() : responseStr.c_str());
        }
        catch (...)
        {
            TraceErr(AimxPshManager, "Failed to dump JSON response for logging");
        }

        // Parse search results
        results.clear();

        if (!response.is_array())
        {
            TraceErr(AimxPshManager, "Response is not a JSON array. Response type: %s",
                     response.type_name());

            // Check if it's an error response
            if (response.is_object())
            {
                if (response.contains("error"))
                {
                    std::string errorMsg = response["error"].is_string() ?
                                         response["error"].get<std::string>() : "Unknown error";
                    TraceErr(AimxPshManager, "Service returned error: %s", errorMsg.c_str());
                }

                if (response.contains("message"))
                {
                    std::string message = response["message"].is_string() ?
                                        response["message"].get<std::string>() : "Unknown message";
                    TraceErr(AimxPshManager, "Service message: %s", message.c_str());
                }
            }
            return E_FAIL;
        }

        TraceInfo(AimxPshManager, "Response is valid JSON array with %d elements", (int)response.size());

        for (size_t i = 0; i < response.size(); ++i)
        {
            const auto& commandResult = response[i];
            TraceInfo(AimxPshManager, "Processing result %d...", (int)i);

            if (!commandResult.is_object())
            {
                TraceErr(AimxPshManager, "Result %d is not a JSON object, skipping", (int)i);
                continue;
            }

            POWERSHELL_COMMAND_SEARCH_RESULT result;

            // Extract command information with detailed logging
            if (commandResult.contains("commandName") && commandResult["commandName"].is_string())
            {
                result.commandName = Utf8ToWide(commandResult["commandName"].get<std::string>());
                TraceInfo(AimxPshManager, "Result %d commandName: %s", (int)i, WideToUtf8(result.commandName).c_str());
            }
            else
            {
                TraceWarn(AimxPshManager, "Result %d missing or invalid commandName field", (int)i);
            }

            if (commandResult.contains("fullText") && commandResult["fullText"].is_string())
            {
                result.fullText = Utf8ToWide(commandResult["fullText"].get<std::string>());
                TraceInfo(AimxPshManager, "Result %d fullText length: %d", (int)i, (int)result.fullText.length());
            }
            else
            {
                TraceWarn(AimxPshManager, "Result %d missing or invalid fullText field", (int)i);
            }

            if (commandResult.contains("parameterNames") && commandResult["parameterNames"].is_string())
            {
                result.parameterNames = Utf8ToWide(commandResult["parameterNames"].get<std::string>());
                TraceInfo(AimxPshManager, "Result %d parameterNames: %s", (int)i, WideToUtf8(result.parameterNames).c_str());
            }
            else
            {
                TraceWarn(AimxPshManager, "Result %d missing or invalid parameterNames field", (int)i);
            }

            if (commandResult.contains("id") && commandResult["id"].is_string())
            {
                result.id = Utf8ToWide(commandResult["id"].get<std::string>());
                TraceInfo(AimxPshManager, "Result %d id: %s", (int)i, WideToUtf8(result.id).c_str());
            }
            else
            {
                TraceWarn(AimxPshManager, "Result %d missing or invalid id field", (int)i);
            }

            if (commandResult.contains("parameterCount") && commandResult["parameterCount"].is_number())
            {
                result.parameterCount = commandResult["parameterCount"].get<int>();
                TraceInfo(AimxPshManager, "Result %d parameterCount: %d", (int)i, result.parameterCount);
            }
            else
            {
                TraceWarn(AimxPshManager, "Result %d missing or invalid parameterCount field", (int)i);
                result.parameterCount = 0;
            }

            if (commandResult.contains("exampleCount") && commandResult["exampleCount"].is_number())
            {
                result.exampleCount = commandResult["exampleCount"].get<int>();
                TraceInfo(AimxPshManager, "Result %d exampleCount: %d", (int)i, result.exampleCount);
            }
            else
            {
                TraceWarn(AimxPshManager, "Result %d missing or invalid exampleCount field", (int)i);
                result.exampleCount = 0;
            }

            if (commandResult.contains("score") && commandResult["score"].is_number())
            {
                result.score = commandResult["score"].get<double>();
                TraceInfo(AimxPshManager, "Result %d score: %f", (int)i, result.score);
            }
            else
            {
                TraceWarn(AimxPshManager, "Result %d missing or invalid score field", (int)i);
                result.score = 0.0;
            }

            if (commandResult.contains("metadata") && commandResult["metadata"].is_object())
            {
                result.metadata = commandResult["metadata"];
                TraceInfo(AimxPshManager, "Result %d has metadata with %d fields", (int)i, (int)result.metadata.size());
            }
            else
            {
                TraceWarn(AimxPshManager, "Result %d missing or invalid metadata field", (int)i);
            }

            results.push_back(result);
            TraceInfo(AimxPshManager, "Successfully processed result %d", (int)i);
        }

        TraceInfo(AimxPshManager, "Successfully parsed %d PowerShell command results", (int)results.size());
        TraceInfo(AimxPshManager, "=== POWERSHELL COMMAND SEARCH END ===");
        return S_OK;
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxPshManager, "Exception in SearchPowerShellCommands: %s", e.what());
        TraceErr(AimxPshManager, "=== POWERSHELL COMMAND SEARCH FAILED ===");
        return E_FAIL;
    }
    catch (...)
    {
        TraceErr(AimxPshManager, "Unknown exception in SearchPowerShellCommands");
        TraceErr(AimxPshManager, "=== POWERSHELL COMMAND SEARCH FAILED ===");
        return E_FAIL;
    }
}

HRESULT
PshManager::BuildPowerShellCommand(
    _In_ const std::wstring& toolName,
    _In_ const nlohmann::json& parameters,
    _Out_ std::wstring& command
    )
{
    command = toolName;

    if (!parameters.is_null() && parameters.is_object())
    {
        for (auto& [key, value] : parameters.items())
        {
            command += L" -" + Utf8ToWide(key);

            if (value.is_string())
            {
                std::wstring strValue = Utf8ToWide(value.get<std::string>());
                command += L" \"" + strValue + L"\"";
            }
            else if (value.is_number())
            {
                command += L" " + std::to_wstring(value.get<double>());
            }
            else if (value.is_boolean() && value.get<bool>())
            {
                // For boolean true, just add the switch parameter (already added above)
            }
        }
    }

    return S_OK;
}

HRESULT
PshManager::ParseNetRagResponse(
    _In_ const nlohmann::json& httpResponse,
    _Out_ POWERSHELL_EXECUTION_RESULT& result
    )
{
    result.success = httpResponse.contains("success") && httpResponse["success"].get<bool>();
    
    if (httpResponse.contains("result"))
    {
        result.result = httpResponse["result"];
    }
    else
    {
        result.result = httpResponse;
    }

    if (httpResponse.contains("error"))
    {
        result.errorMessage = Utf8ToWide(httpResponse["error"].get<std::string>());
    }
    else if (!result.success)
    {
        result.errorMessage = L"PowerShell execution failed";
    }

    return S_OK;
}

HRESULT
PshManager::ExecutePowerShellViaHttp(
    _In_ const std::wstring& command,
    _In_ int timeoutSeconds,
    _Out_ nlohmann::json& response
    )
{
    try
    {
        // Create the request body
        nlohmann::json requestBody;
        requestBody["command"] = WideToUtf8(command);
        requestBody["timeoutSeconds"] = timeoutSeconds;

        // Log the full request details
        std::string requestBodyStr = requestBody.dump(4);
        TraceInfo(AimxPshManager, "=== POWERSHELL EXECUTION REQUEST START ===");
        TraceInfo(AimxPshManager, "Endpoint: %s", WideToUtf8(AimxConstants::ApiEndpoints::AIMX_POWERSHELL_EXECUTION).c_str());
        TraceInfo(AimxPshManager, "Command: %s", WideToUtf8(command).c_str());
        TraceInfo(AimxPshManager, "Timeout: %d seconds", timeoutSeconds);
        TraceInfo(AimxPshManager, "Request body JSON:");
        TraceInfo(AimxPshManager, "%s", requestBodyStr.c_str());
        TraceInfo(AimxPshManager, "=== POWERSHELL EXECUTION REQUEST END ===");

        // Send POST request to PowerShell execution endpoint using centralized constant
        HRESULT hr = SendHttpPostRequest(AimxConstants::ApiEndpoints::AIMX_POWERSHELL_EXECUTION, requestBody, response);

        if (FAILED(hr))
        {
            TraceErr(AimxPshManager, "PowerShell execution HTTP request failed: %!HRESULT!", hr);
        }

        return hr;
    }
    catch (...)
    {
        TraceErr(AimxPshManager, "Exception in ExecutePowerShellViaHttp");
        return E_FAIL;
    }
}

HRESULT
PshManager::SendHttpGetRequest(
    _In_ const std::wstring& endpoint,
    _Out_ nlohmann::json& response
    )
{
    try
    {
        using namespace web::http;
        using namespace web::http::client;

        // Create HTTP client for NetRag service using centralized base URL
        http_client client(AimxConstants::ServiceConfig::NetRagServiceBaseUrl);

        // Send GET request
        auto task = client.request(methods::GET, endpoint);
        auto httpResponse = task.get();

        if (httpResponse.status_code() != status_codes::OK)
        {
            return E_FAIL;
        }

        // Parse JSON response
        auto bodyTask = httpResponse.extract_string();
        auto bodyString = bodyTask.get();
        std::string bodyUtf8 = WideToUtf8(bodyString);
        response = nlohmann::json::parse(bodyUtf8);

        return S_OK;
    }
    catch (...)
    {
        return E_FAIL;
    }
}

HRESULT
PshManager::SendHttpPostRequest(
    _In_ const std::wstring& endpoint,
    _In_ const nlohmann::json& requestBody,
    _Out_ nlohmann::json& response
    )
{
    try
    {
        using namespace web::http;
        using namespace web::http::client;

        // Create HTTP client for NetRag service using centralized base URL
        http_client client(AimxConstants::ServiceConfig::NetRagServiceBaseUrl);

        // Create POST request
        http_request request(methods::POST);
        request.set_request_uri(endpoint);
        request.headers().set_content_type(L"application/json");

        // Set request body
        std::string jsonStr = requestBody.dump();
        std::wstring jsonWide = Utf8ToWide(jsonStr);
        request.set_body(jsonWide, L"application/json");

        // Send request
        auto task = client.request(request);
        auto httpResponse = task.get();

        if (httpResponse.status_code() != status_codes::OK)
        {
            return E_FAIL;
        }

        // Parse JSON response
        auto bodyTask = httpResponse.extract_string();
        auto bodyString = bodyTask.get();
        std::string bodyUtf8 = WideToUtf8(bodyString);
        response = nlohmann::json::parse(bodyUtf8);

        return S_OK;
    }
    catch (...)
    {
        return E_FAIL;
    }
}

HRESULT
PshManager::LoadPowerShellContextData()
/*++

Routine Description:
    Load comprehensive PowerShell command context data from JSON file into cache.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    std::unique_lock<std::shared_mutex> lock(s_contextCacheMutex);

    if (s_contextDataLoaded)
    {
        return S_OK; // Already loaded
    }

    try
    {
        TraceInfo(AimxPshManager, "Loading PowerShell context data from: %s",
                 WideToUtf8(AimxConstants::ServiceConfig::PowerShellContextPath).c_str());

        // Read JSON file
        std::ifstream file(AimxConstants::ServiceConfig::PowerShellContextPath);
        if (!file.is_open())
        {
            TraceErr(AimxPshManager, "Failed to open PowerShell context file: %s",
                    WideToUtf8(AimxConstants::ServiceConfig::PowerShellContextPath).c_str());
            return E_FAIL;
        }

        nlohmann::json jsonData;
        file >> jsonData;
        file.close();

        if (!jsonData.is_array())
        {
            TraceErr(AimxPshManager, "PowerShell context file is not a JSON array");
            return E_FAIL;
        }

        // Store each command context as raw JSON
        for (const auto& commandJson : jsonData)
        {
            if (!commandJson.is_object() || !commandJson.contains("CommandName"))
            {
                continue; // Skip invalid entries
            }

            std::string commandNameUtf8 = commandJson["CommandName"].get<std::string>();
            std::wstring commandName = Utf8ToWide(commandNameUtf8);

            // Store the entire JSON object as-is for this command
            s_powerShellContextCache[commandName] = commandJson;
        }

        s_contextDataLoaded = true;
        TraceInfo(AimxPshManager, "Successfully loaded %d PowerShell command contexts",
                 (int)s_powerShellContextCache.size());

        return S_OK;
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxPshManager, "Exception loading PowerShell context data: %s", e.what());
        return E_FAIL;
    }
    catch (...)
    {
        TraceErr(AimxPshManager, "Unknown exception loading PowerShell context data");
        return E_FAIL;
    }
}

HRESULT
PshManager::GetPowerShellCommandContextJson(
    _In_ const std::wstring& commandName,
    _Out_ nlohmann::json& contextJson
    )
/*++

Routine Description:
    Retrieve PowerShell command context JSON from cache.

Arguments:
    commandName - Name of the PowerShell command
    contextJson - Output JSON context object

Return Value:
    S_OK on success, ERROR_NOT_FOUND if command not found, or an error HRESULT on failure.

--*/
{
    // Ensure context data is loaded
    HRESULT hr = LoadPowerShellContextData();
    if (FAILED(hr))
    {
        return hr;
    }

    std::shared_lock<std::shared_mutex> lock(s_contextCacheMutex);

    auto it = s_powerShellContextCache.find(commandName);
    if (it == s_powerShellContextCache.end())
    {
        TraceWarn(AimxPshManager, "PowerShell command context not found: %s",
                 WideToUtf8(commandName).c_str());
        return HRESULT_FROM_WIN32(ERROR_NOT_FOUND);
    }

    contextJson = it->second;
    return S_OK;
}

std::wstring
PshManager::FormatPowerShellCommandJsonForPrompt(
    _In_ const nlohmann::json& contextJson,
    _In_ const POWERSHELL_COMMAND_SEARCH_RESULT& searchResult
    )
/*++

Routine Description:
    Format PowerShell command JSON context directly for LLM prompt.

Arguments:
    contextJson - Raw JSON context data from the comprehensive dataset
    searchResult - Search result with relevance score

Return Value:
    Formatted string for LLM prompt with raw JSON context.

--*/
{
    std::wstring formatted;

    try
    {
        // Command header with relevance score
        if (contextJson.contains("CommandName") && contextJson["CommandName"].is_string())
        {
            std::string commandNameUtf8 = contextJson["CommandName"].get<std::string>();
            std::wstring commandName = Utf8ToWide(commandNameUtf8);
            formatted += L"## " + commandName + L" (Relevance Score: " + std::to_wstring(searchResult.score) + L")\n\n";
        }

        // Add the raw JSON context directly for the LLM
        formatted += L"**Comprehensive Command Context (JSON):**\n";
        formatted += L"```json\n";

        // Convert JSON to pretty-printed string
        std::string jsonStr = contextJson.dump(2); // 2-space indentation
        std::wstring jsonWide = Utf8ToWide(jsonStr);
        formatted += jsonWide;

        formatted += L"\n```\n\n";

        // Add brief instruction for the LLM
        formatted += L"**Instructions:** Use the above JSON context to understand the command's syntax, parameters, and examples. ";
        formatted += L"Generate the appropriate PowerShell command based on the user's intent and the comprehensive context provided.\n\n";
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxPshManager, "Exception formatting PowerShell JSON context: %s", e.what());
        formatted += L"**Error:** Failed to format command context\n\n";
    }

    return formatted;
}

HRESULT
PshManager::SelectBestPowerShellCommand(
    _In_ const std::wstring& userQuery,
    _In_ const std::vector<POWERSHELL_COMMAND_SEARCH_RESULT>& searchResults,
    _Out_ std::wstring& selectedCommandName,
    _Out_ std::wstring& selectionReasoning
    )
/*++

Routine Description:
    Use LLM to analyze user intent and select the best PowerShell command from search results.

Arguments:
    userQuery - Original user query/intent
    searchResults - Vector of PowerShell command search results from RAG
    selectedCommandName - Output: Name of the selected command
    selectionReasoning - Output: LLM's reasoning for the selection

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    try
    {
        TraceInfo(AimxPshManager, "=== POWERSHELL COMMAND SELECTION START ===");
        TraceInfo(AimxPshManager, "User Query: %s", WideToUtf8(userQuery).c_str());
        TraceInfo(AimxPshManager, "Number of candidates: %d", (int)searchResults.size());

        if (searchResults.empty())
        {
            TraceErr(AimxPshManager, "No search results provided for command selection");
            return E_INVALIDARG;
        }

        if (searchResults.size() == 1)
        {
            // Only one candidate, select it directly
            selectedCommandName = searchResults[0].commandName;
            selectionReasoning = L"Only one command candidate available: " + selectedCommandName;
            TraceInfo(AimxPshManager, "Single candidate selected: %s", WideToUtf8(selectedCommandName).c_str());

            // Validate that the selected command is read-only
            if (!IsReadOnlyCommand(selectedCommandName))
            {
                TraceErr(AimxPshManager, "Single candidate command '%s' is not in the read-only whitelist", WideToUtf8(selectedCommandName).c_str());
                return E_ACCESSDENIED;
            }

            TraceInfo(AimxPshManager, "Single candidate command '%s' validated as read-only", WideToUtf8(selectedCommandName).c_str());
            return S_OK;
        }

        // Build LLM prompt for command selection
        std::wstring selectionPrompt = L"# PowerShell Command Selection Task\n\n";
        selectionPrompt += L"**User Intent:** " + userQuery + L"\n\n";
        selectionPrompt += L"**Available Commands:**\n";

        // Add each candidate with its context
        for (size_t i = 0; i < searchResults.size(); ++i)
        {
            const auto& result = searchResults[i];
            selectionPrompt += L"\n## Option " + std::to_wstring(i + 1) + L": " + result.commandName + L"\n";
            selectionPrompt += L"- **Relevance Score:** " + std::to_wstring(result.score) + L"\n";
            selectionPrompt += L"- **Parameter Count:** " + std::to_wstring(result.parameterCount) + L"\n";
            selectionPrompt += L"- **Example Count:** " + std::to_wstring(result.exampleCount) + L"\n";

            if (!result.parameterNames.empty())
            {
                selectionPrompt += L"- **Key Parameters:** " + result.parameterNames + L"\n";
            }

            if (!result.fullText.empty() && result.fullText.length() < 200)
            {
                selectionPrompt += L"- **Description:** " + result.fullText + L"\n";
            }
        }

        selectionPrompt += L"\n# Selection Instructions\n\n";
        selectionPrompt += L"Analyze the user intent and select the MOST APPROPRIATE command. Consider:\n";
        selectionPrompt += L"1. **Semantic match** - Which command best matches what the user wants to accomplish?\n";
        selectionPrompt += L"2. **Parameter alignment** - Which command has parameters that fit the user's needs?\n";
        selectionPrompt += L"3. **Complexity appropriateness** - Choose simpler commands when possible\n";
        selectionPrompt += L"4. **Common usage patterns** - Prefer widely-used commands for standard tasks\n\n";
        selectionPrompt += L"**Response Format:**\n";
        selectionPrompt += L"```json\n";
        selectionPrompt += L"{\n";
        selectionPrompt += L"  \"selectedCommand\": \"<exact_command_name>\",\n";
        selectionPrompt += L"  \"reasoning\": \"<brief_explanation_of_why_this_command_is_best>\"\n";
        selectionPrompt += L"}\n";
        selectionPrompt += L"```\n";

        TraceInfo(AimxPshManager, "Sending command selection prompt to LLM...");

        // Call LLM for intelligent command selection
        std::wstring llmResponse;
        HRESULT hr = SendCommandSelectionLlmRequest(selectionPrompt, llmResponse);
        if (FAILED(hr))
        {
            TraceWarn(AimxPshManager, "LLM command selection failed, falling back to heuristic");

            // Fallback: Use semantic heuristic instead of just highest score
            // Prefer Get-* commands for "get" queries, Search-* for "search" queries, etc.
            std::wstring lowerQuery = userQuery;
            std::transform(lowerQuery.begin(), lowerQuery.end(), lowerQuery.begin(), ::towlower);

            size_t bestIndex = 0;
            double bestScore = -1.0;

            for (size_t i = 0; i < searchResults.size(); ++i)
            {
                double adjustedScore = searchResults[i].score;
                std::wstring lowerCommand = searchResults[i].commandName;
                std::transform(lowerCommand.begin(), lowerCommand.end(), lowerCommand.begin(), ::towlower);

                // Boost score for semantic matches
                if (lowerQuery.find(L"get") != std::wstring::npos && lowerCommand.find(L"get-") == 0)
                {
                    adjustedScore += 0.5; // Boost Get-* commands for "get" queries
                }
                else if (lowerQuery.find(L"search") != std::wstring::npos && lowerCommand.find(L"search-") == 0)
                {
                    adjustedScore += 0.5; // Boost Search-* commands for "search" queries
                }
                else if (lowerQuery.find(L"test") != std::wstring::npos && lowerCommand.find(L"test-") == 0)
                {
                    adjustedScore += 0.5; // Boost Test-* commands for "test" queries
                }

                // Penalize modification commands for read-only queries
                if (lowerCommand.find(L"unlock-") == 0 || lowerCommand.find(L"set-") == 0 ||
                    lowerCommand.find(L"remove-") == 0 || lowerCommand.find(L"add-") == 0)
                {
                    adjustedScore -= 0.3; // Penalize modification commands
                }

                if (adjustedScore > bestScore)
                {
                    bestScore = adjustedScore;
                    bestIndex = i;
                }
            }

            selectedCommandName = searchResults[bestIndex].commandName;
            selectionReasoning = L"Selected '" + selectedCommandName + L"' using semantic heuristic (LLM unavailable). Adjusted score: " + std::to_wstring(bestScore);
        }
        else
        {
            // Parse LLM response for command selection
            hr = ParseCommandSelectionResponse(llmResponse, searchResults, selectedCommandName, selectionReasoning);
            if (FAILED(hr))
            {
                TraceWarn(AimxPshManager, "Failed to parse LLM response, using first valid command");
                selectedCommandName = searchResults[0].commandName;
                selectionReasoning = L"Used first command due to LLM parsing failure";
            }
        }

        TraceInfo(AimxPshManager, "Selected command: %s", WideToUtf8(selectedCommandName).c_str());
        TraceInfo(AimxPshManager, "Selection reasoning: %s", WideToUtf8(selectionReasoning).c_str());

        // Validate that the selected command is read-only
        if (!IsReadOnlyCommand(selectedCommandName))
        {
            TraceErr(AimxPshManager, "Selected command '%s' is not in the read-only whitelist", WideToUtf8(selectedCommandName).c_str());
            return E_ACCESSDENIED;
        }

        TraceInfo(AimxPshManager, "Command '%s' validated as read-only", WideToUtf8(selectedCommandName).c_str());
        TraceInfo(AimxPshManager, "=== POWERSHELL COMMAND SELECTION END ===");

        return S_OK;
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxPshManager, "Exception in SelectBestPowerShellCommand: %s", e.what());
        return E_FAIL;
    }
    catch (...)
    {
        TraceErr(AimxPshManager, "Unknown exception in SelectBestPowerShellCommand");
        return E_FAIL;
    }
}

bool
PshManager::IsReadOnlyCommand(
    _In_ const std::wstring& commandName
    )
/*++

Routine Description:
    Check if a PowerShell command is in the read-only whitelist.

Arguments:
    commandName - Name of the PowerShell command to validate

Return Value:
    true if command is read-only and allowed, false otherwise.

--*/
{
    // Convert to lowercase for case-insensitive comparison
    std::wstring lowerCommandName = commandName;
    std::transform(lowerCommandName.begin(), lowerCommandName.end(), lowerCommandName.begin(), ::towlower);

    // Whitelist of allowed read-only PowerShell commands
    static const std::set<std::wstring> readOnlyCommands = {
        // Active Directory - Read operations (from official Microsoft docs)
        L"get-adaccountauthorizationgroup",
        L"get-adaccountresultantpasswordreplicationpolicy",
        L"get-adauthenticationpolicy",
        L"get-adauthenticationpolicysilo",
        L"get-adcentralaccesspolicy",
        L"get-adcentralaccessrule",
        L"get-adclaimtransformpolicy",
        L"get-adclaimtype",
        L"get-adcomputer",
        L"get-adcomputerserviceaccount",
        L"get-addccloningexcludedapplicationlist",
        L"get-addefaultdomainpasswordpolicy",
        L"get-addomain",
        L"get-addomaincontroller",
        L"get-addomaincontrollerpasswordreplicationpolicy",
        L"get-addomaincontrollerpasswordreplicationpolicyusage",
        L"get-adfinegrainedpasswordpolicy",
        L"get-adfinegrainedpasswordpolicysubject",
        L"get-adforest",
        L"get-adgroup",
        L"get-adgroupmember",
        L"get-adobject",
        L"get-adoptionalfeature",
        L"get-adorganizationalunit",
        L"get-adprincipalgroupmembership",
        L"get-adreplicationattributemetadata",
        L"get-adreplicationconnection",
        L"get-adreplicationfailure",
        L"get-adreplicationpartnermetadata",
        L"get-adreplicationqueueoperation",
        L"get-adreplicationsite",
        L"get-adreplicationsitelink",
        L"get-adreplicationsitelinkbridge",
        L"get-adreplicationsubnet",
        L"get-adreplicationuptodatenessvectortable",
        L"get-adresourceproperty",
        L"get-adresourcepropertylist",
        L"get-adresourcepropertyvaluetype",
        L"get-adrootdse",
        L"get-adserviceaccount",
        L"get-adtrust",
        L"get-aduser",
        L"get-aduserresultantpasswordpolicy",
        L"search-adaccount",
        L"test-adserviceaccount",
    };

    bool isAllowed = readOnlyCommands.find(lowerCommandName) != readOnlyCommands.end();

    TraceInfo(AimxPshManager, "Command '%s' read-only validation: %s",
              WideToUtf8(commandName).c_str(), isAllowed ? "ALLOWED" : "BLOCKED");

    return isAllowed;
}

HRESULT
PshManager::SendCommandSelectionLlmRequest(
    _In_ const std::wstring& selectionPrompt,
    _Out_ std::wstring& response
    )
/*++

Routine Description:
    Send a command selection request to the LLM service.

Arguments:
    selectionPrompt - The prompt for command selection
    response - Output LLM response

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    try
    {
        TraceInfo(AimxPshManager, "Sending command selection request to LLM service");

        // Get configuration from global singleton
        AimxLlmConfig& llmConfig = AimxLlmConfig::Instance();
        std::wstring llmEndpointUrl = llmConfig.GetEndpointUrl();

        if (llmEndpointUrl.empty())
        {
            TraceErr(AimxPshManager, "LLM endpoint URL is not configured");
            return E_FAIL;
        }

        TraceInfo(AimxPshManager, "Using LLM endpoint: %s", WideToUtf8(llmEndpointUrl).c_str());

        // Create HTTP client configuration
        web::http::client::http_client_config config;
        config.set_timeout(std::chrono::seconds(30));

        // Create HTTP client
        web::http::client::http_client client(llmEndpointUrl, config);

        // Build request JSON
        web::json::value requestJson = web::json::value::object();
        requestJson[L"model"] = web::json::value::string(L"gpt-4");
        requestJson[L"max_tokens"] = web::json::value::number(500);
        requestJson[L"temperature"] = web::json::value::number(0.1);

        web::json::value messages = web::json::value::array();

        web::json::value systemMessage = web::json::value::object();
        systemMessage[L"role"] = web::json::value::string(L"system");
        systemMessage[L"content"] = web::json::value::string(L"You are an expert PowerShell command selector. Analyze user intent and select the most appropriate command from the given options.");
        messages[0] = systemMessage;

        web::json::value userMessage = web::json::value::object();
        userMessage[L"role"] = web::json::value::string(L"user");
        userMessage[L"content"] = web::json::value::string(selectionPrompt);
        messages[1] = userMessage;

        requestJson[L"messages"] = messages;

        // Create request
        web::http::http_request request(web::http::methods::POST);
        request.headers().set_content_type(L"application/json");
        request.set_body(requestJson);

        TraceInfo(AimxPshManager, "Sending HTTP POST request to LLM service");

        // Send request and wait for response
        auto responseTask = client.request(request);
        auto httpResponse = responseTask.get();

        auto statusCode = httpResponse.status_code();
        TraceInfo(AimxPshManager, "HTTP Response Status Code: %d", static_cast<int>(statusCode));

        if (statusCode != web::http::status_codes::OK)
        {
            TraceErr(AimxPshManager, "LLM service returned error status: %d", static_cast<int>(statusCode));
            return E_FAIL;
        }

        // Get response body
        auto responseBodyTask = httpResponse.extract_json();
        auto responseBody = responseBodyTask.get();

        TraceInfo(AimxPshManager, "Successfully received LLM response");

        // Extract content from response
        if (!responseBody.has_field(L"choices"))
        {
            TraceErr(AimxPshManager, "Response missing 'choices' field");
            return E_FAIL;
        }

        if (responseBody[L"choices"].size() == 0)
        {
            TraceErr(AimxPshManager, "Response 'choices' array is empty");
            return E_FAIL;
        }

        auto choice = responseBody[L"choices"][0];

        if (!choice.has_field(L"message"))
        {
            TraceErr(AimxPshManager, "First choice missing 'message' field");
            return E_FAIL;
        }

        if (!choice[L"message"].has_field(L"content"))
        {
            TraceErr(AimxPshManager, "Message missing 'content' field");
            return E_FAIL;
        }

        response = choice[L"message"][L"content"].as_string();
        TraceInfo(AimxPshManager, "Successfully extracted LLM response, length: %lu characters", static_cast<unsigned long>(response.length()));

        return S_OK;
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxPshManager, "Exception in SendCommandSelectionLlmRequest: %s", e.what());
        return E_FAIL;
    }
    catch (...)
    {
        TraceErr(AimxPshManager, "Unknown exception in SendCommandSelectionLlmRequest");
        return E_FAIL;
    }
}
