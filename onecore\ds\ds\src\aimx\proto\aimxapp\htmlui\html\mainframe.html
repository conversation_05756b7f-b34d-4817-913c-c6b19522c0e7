<!--++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    mainframe.html

Abstract:

    This module serves as the main HTML container for the AIMX application.
    Provides the navigation structure and loads child components dynamically,
    serving as the primary entry point for the WebView interface.

Author:

    <PERSON><PERSON><PERSON> (r<PERSON><PERSON>) 03/29/2025

---->
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>aimx</title>
    <link rel="stylesheet" href="../css/styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
    <script>
        // Add immediate error handling before any other scripts
        window.onerror = function (message, source, lineno, colno, error) {
            console.error("JavaScript error:", message, "at", source, lineno, colno);
            // Don't alert as it might cause cascade of errors
            return true;
        };

        // Create a global namespace for our application
        window.aimx = window.aimx || {};
    </script>
</head>

<body>
    <div class="app-container">
        <!-- Navigation Pane -->
        <div class="nav-pane">
            <div class="nav-header">AD AIMX Console</div>
            <ul class="nav-items">
                <li class="nav-item active" data-page="chat-page">
                    <span class="nav-item-icon">💬</span>
                    <span class="nav-item-text">Chat</span>
                </li>
                <li class="nav-item" data-page="rag-builder-page">
                    <span class="nav-item-icon">📚</span>
                    <span class="nav-item-text">RAG Builder</span>
                </li>
                <li class="nav-item" data-page="ad-dashboard-page">
                    <span class="nav-item-icon">📈</span>
                    <span class="nav-item-text">AD Dashboard</span>
                </li>
                <li class="nav-item" data-page="entraid-migration-page">
                    <span class="nav-item-icon">🔄</span>
                    <span class="nav-item-text">Entra ID Migration</span>
                </li>
                <li class="nav-item" data-page="rag-management-page">
                    <span class="nav-item-icon">📖</span>
                    <span class="nav-item-text">RAG DB management</span>
                </li>
            </ul>

            <!-- Settings menu item at bottom -->
            <div class="nav-bottom">
                <div class="nav-item" id="settingsButton">
                    <span class="nav-item-icon">⚙️</span>
                    <span class="nav-item-text">Configuration</span>
                </div>
            </div>
        </div>

        <!-- Content Container -->
        <div class="content-container">
            <!-- Chat Page - dynamically loaded -->
            <div id="chat-page" class="page active">
                <div id="chat-content-container" class="dynamic-content">
                    <div class="loading-indicator">Loading Chat Interface...</div>
                </div>
            </div>

            <!-- RAG Builder Page - dynamically loaded -->
            <div id="rag-builder-page" class="page">
                <div id="rag-builder-container" class="dynamic-content">
                    <div class="loading-indicator">Loading RAG Builder...</div>
                </div>
            </div>

            <!-- AD Dashboard Page - dynamically loaded -->
            <div id="ad-dashboard-page" class="page">
                <div id="ad-dashboard-container" class="dynamic-content">
                    <div class="loading-indicator">Loading AD Dashboard...</div>
                </div>
            </div>

            <!-- Entra ID Migration Page - dynamically loaded -->
            <div id="entraid-migration-page" class="page">
                <div id="entraid-migration-container" class="dynamic-content">
                    <div class="loading-indicator">Loading Entra ID Migration Assessment...</div>
                </div>
            </div>

            <!-- Rag Management page - dynamically loaded -->
            <div id="rag-management-page" class="page">
                <div id="rag-management-container" class="dynamic-content">
                    <div class="loading-indicator">Loading RAG Management...</div>
                </div>
            </div>
        </div>

        <!-- The settings modal will be dynamically loaded when needed -->        <!-- Include scripts in correct order -->
        <script src="../js/ragmanagement.js"></script>
        <script src="../js/chat.js"></script>
        <script src="../js/settings.js"></script>
        <script src="../js/entraid-migration.js"></script>
        <script src="../js/mainframe.js"></script>
        <script src="https://code.jquery.com/jquery-3.7.1.slim.min.js" integrity="sha256-kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=" crossorigin="anonymous"></script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js" integrity="sha384-j1CDi7MgGQ12Z7Qab0qlWQ/Qqz24Gc6BM0thvEMVjHnfYGF0rmFCozFSxQBxwHKO" crossorigin="anonymous"></script>
        <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
        <script>
            // Additional initialization check for debugging
            document.addEventListener('DOMContentLoaded', function () {
                console.log("DOM fully loaded - Debug check for modules:");
                console.log("- chat module:", typeof window.aimx.chat !== 'undefined' ? "Available" : "Not available");
                console.log("- settings module:", typeof window.aimx.settings !== 'undefined' ? "Available" : "Not available");
                console.log("- core module:", typeof window.aimx.core !== 'undefined' ? "Available" : "Not available");
            });
        </script>
</body>

</html>