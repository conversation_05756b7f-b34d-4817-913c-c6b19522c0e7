/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    ConversationManager.h

Abstract:

    Header file for the Conversation Session Manager that manages conversation sessions
    using existing AIMX context IDs. 

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/13/2025

--*/

#pragma once

#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include <mutex>
#include <functional>
#include <atomic>
#include "AimxCommon.h"
#include "nlohmann/json.hpp"

// Message types for conversation flow
enum AIMX_CONVERSATION_MESSAGE_TYPE
{
    AIMX_MSG_USER_INPUT = 1,
    AIMX_MSG_ASSISTANT_RESPONSE = 2,
    AI<PERSON>_MSG_PROGRESS_UPDATE = 3,
    <PERSON><PERSON>_MSG_TASK_BREAKDOWN = 4,
    AIMX_MSG_APPROVAL_REQUEST = 5,
    AIMX_MSG_TOOL_EXECUTION = 6,
    AIMX_MSG_ERROR_MESSAGE = 7,
    <PERSON><PERSON>_MSG_LLM_INFERENCE = 8,
    <PERSON><PERSON>_MSG_LLM_RESPONSE = 9,
    AIMX_MSG_TOOL_ANALYSIS = 10,
    AIMX_MSG_EXECUTION_PLAN = 11,
    AIMX_MSG_TOOL_EXECUTION_START = 12,
    AIMX_MSG_TOOL_EXECUTION_RESULT = 13,
    AIMX_MSG_STATUS_UPDATE = 14,
    AIMX_MSG_COMPLETION = 15,
    AIMX_MSG_FINAL_RESULT = 16
};

// Session states
enum AIMX_CONVERSATION_SESSION_STATE
{
    AIMX_SESSION_ACTIVE = 1,
    AIMX_SESSION_PROCESSING = 2,
    AIMX_SESSION_AWAITING_APPROVAL = 3,
    AIMX_SESSION_COMPLETED = 4,
    AIMX_SESSION_ERROR = 5
};

// Individual message in conversation (internal C++ structure)
struct AIMX_CONVERSATION_MESSAGE
{
    GUID MessageId;
    AIMX_CONVERSATION_MESSAGE_TYPE Type;
    std::wstring Content;
    nlohmann::json Metadata;
    FILETIME Timestamp;
};

// Message queue for polling
struct AIMX_QUEUED_MESSAGE
{
    AIMX_CONVERSATION_MESSAGE Message;
    bool IsDelivered;
};

// Conversation Session - uses existing AIMX context ID
class ConversationSession
{
public:
    ConversationSession(const GUID& contextId);
    ~ConversationSession();

    // Core session management
    HRESULT Initialize();
    HRESULT Shutdown();
    
    // Message handling
    HRESULT AddMessage(
        _In_ const AIMX_CONVERSATION_MESSAGE& message
        );
        
    HRESULT SendMessage(
        _In_ AIMX_CONVERSATION_MESSAGE_TYPE type,
        _In_ const std::wstring& content,
        _In_ const nlohmann::json& metadata = {}
        );
        
    // State management
    AIMX_CONVERSATION_SESSION_STATE GetState() const { return m_State; }
    
    HRESULT SetState(
        _In_ AIMX_CONVERSATION_SESSION_STATE newState
        );
    
    // Message polling
    HRESULT PollMessages(
        _Out_ std::vector<AIMX_CONVERSATION_MESSAGE>& newMessages
        );

    // Status probing for detailed service state
    HRESULT GetCurrentStatus(
        _Out_ std::wstring& statusDescription,
        _Out_ std::wstring& currentStage,
        _Out_ bool& isActive
        );

    // Update current status (for internal use)
    void UpdateStatus(
        _In_ const std::wstring& stage,
        _In_ const std::wstring& description
        );
        
    HRESULT SendProgressUpdate(
        _In_ const std::wstring& message
        );

        
    HRESULT SetCurrentOperation(
        _In_ const GUID& operationId
        );
    

    bool IsActive() const { return m_IsActive.load(); }

private:
    // Delete copy constructor and assignment operator
    ConversationSession(const ConversationSession&) = delete;
    ConversationSession& operator=(const ConversationSession&) = delete;

    // Internal data members
    GUID m_ContextId;  // Use existing AIMX context ID as session ID
    std::vector<AIMX_CONVERSATION_MESSAGE> m_Messages;
    std::vector<AIMX_QUEUED_MESSAGE> m_QueuedMessages;  // For polling mechanism
    AIMX_CONVERSATION_SESSION_STATE m_State;
    std::mutex m_SessionMutex;
    std::atomic<bool> m_IsActive;
    GUID m_CurrentOperationId;

    // Status tracking for detailed feedback
    std::wstring m_CurrentStage;
    std::wstring m_StatusDescription;
    FILETIME m_LastActivityTime;
};

// Main Conversation Session Manager component class
class ConversationSessionManager
{
public:
    // Initialize the Conversation Session Manager component
    static HRESULT Initialize();

    // Uninitialize and cleanup resources
    static void Uninitialize();

    // Session lifecycle - use existing context ID
    static HRESULT GetOrCreateSession(
        _In_ const GUID& contextId,
        _Out_ std::shared_ptr<ConversationSession>& session
        );
        
    static HRESULT GetSession(
        _In_ const GUID& contextId,
        _Out_ std::shared_ptr<ConversationSession>& session
        );

    static HRESULT GetSessionByOperationId(
        _In_ const GUID& operationId,
        _Out_ std::shared_ptr<ConversationSession>& session
        );
        
    static HRESULT CloseSession(
        _In_ const GUID& contextId
        );
    
    // Operation tracking - called by Planner/Orchestrator
    static HRESULT NotifyOperationStarted(
        _In_ const GUID& contextId,
        _In_ const GUID& operationId
        );
        
    static HRESULT NotifyOperationProgress(
        _In_ const GUID& operationId,
        _In_ const std::wstring& message
        );

    static HRESULT NotifyOperationCompleted(
        _In_ const GUID& operationId,
        _In_ const std::wstring& result
        );
        
    static HRESULT NotifyOperationFailed(
        _In_ const GUID& operationId,
        _In_ const std::wstring& error
        );
    
    // Get the singleton instance
    static ConversationSessionManager* GetInstance();

private:
    // Private constructor for singleton pattern
    ConversationSessionManager();
    
    // Private destructor
    ~ConversationSessionManager();

    // Delete copy constructor and assignment operator
    ConversationSessionManager(const ConversationSessionManager&) = delete;
    ConversationSessionManager& operator=(const ConversationSessionManager&) = delete;

    // Internal initialization
    HRESULT InitializeInternal();

    // Internal data structures
    static ConversationSessionManager* s_instance;
    static std::mutex s_instanceMutex;

    // Use existing context ID as session key
    std::unordered_map<GUID, std::shared_ptr<ConversationSession>, GuidHash, GuidEqual> m_sessions;
    std::unordered_map<GUID, GUID, GuidHash, GuidEqual> m_operationToContextMap; // OperationId -> ContextId
    std::mutex m_sessionsMutex;
    bool m_initialized;
};

// Helper functions
HRESULT CreateConversationMessage(
    _In_ AIMX_CONVERSATION_MESSAGE_TYPE type,
    _In_ const std::wstring& content,
    _In_ const nlohmann::json& metadata,
    _Out_ AIMX_CONVERSATION_MESSAGE& message
    );

// Message string allocation for PowerShell polling
HRESULT FormatMessagesForPolling(
    _In_ const std::vector<AIMX_CONVERSATION_MESSAGE>& messages,
    _Out_ LPWSTR* messagesString
    );

std::wstring ConversationMessageTypeToString(
    _In_ AIMX_CONVERSATION_MESSAGE_TYPE type
    );

// Helper function to extract stage from formatted message
void ExtractStageFromMessage(
    _In_ const std::wstring& message,
    _Out_ std::wstring& stage,
    _Out_ std::wstring& description
    );
