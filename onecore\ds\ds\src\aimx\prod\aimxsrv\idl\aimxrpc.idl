/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    aimxrpc.idl

Abstract:
    Defines a local AIMXSRV RPC interface. Used for managing context handles and RPC operations for the AIMXSRV service.

Author:
    <PERSON> (SNAKE FIGHTER) (linda<PERSON><PERSON>) 06/09/2025

--*/
[
    uuid(B2A1C7E2-4F3B-4A6B-9C2D-1E8F7A5D3C4B),
    version(1.0),
    helpstring("AIMX LRPC Interface"),
    pointer_default(unique)
]
interface aimxrpc
{
    import "wtypes.idl";

    // LPC endpoint for the AIMXSRV service
    cpp_quote("#define AIMXSRV_LRPC_ENDPOINT L\"aimxrpc_LRPC_b2a1c7e2-4f3b-4a6b-9c2d-1e8f7a5d3c4b\"")

    // Context handle structure
    typedef [context_handle] PVOID AIMXR_HANDLE;

    // Connect to the AIMXSRV service and obtain a context handle
    HRESULT AimxrConnect(
        [out] AIMXR_HANDLE* contextHandle
    );

    // Close the context handle
    HRESULT AimxrClose(
        [in, out] AIMXR_HANDLE* contextHandle
    );

    // Process a prompt and return a response
    HRESULT AimxrProcessPrompt(
        [in] AIMXR_HANDLE contextHandle,
        [in, string] LPCWSTR InputPrompt,
        [out, string] LPWSTR* Response
    );

    // Poll for conversation messages (replaces callback mechanism)
    HRESULT AimxrPollConversationMessages(
        [in] AIMXR_HANDLE contextHandle,
        [out, string] LPWSTR* messages
    );

    // Get current conversation status
    HRESULT AimxrGetConversationStatus(
        [in] AIMXR_HANDLE contextHandle,
        [out, string] LPWSTR* statusJson
    );

    // Start interactive conversation with real-time updates
    HRESULT AimxrStartConversation(
        [in] AIMXR_HANDLE contextHandle,
        [in, string] LPCWSTR query,
        [in] LONG executionMode
    );

    // Get LLM service status and connectivity
    HRESULT AimxrGetLlmStatus(
        [in] AIMXR_HANDLE contextHandle,
        [out, string] LPWSTR* statusJson
    );

    // Get MCP server information and available tools
    HRESULT AimxrGetMcpServerInfo(
        [in] AIMXR_HANDLE contextHandle,
        [out, string] LPWSTR* serverInfoJson
    );

    //Additional RPC methods can be defined here as needed
}