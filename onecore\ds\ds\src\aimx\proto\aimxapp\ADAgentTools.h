/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    ADAgentTools.h

Abstract:

    This header file defines utility functions for the ADAI Agent in the AIMX application.
    These functions assist in processing prompts, gathering data, and managing the agent's state.

Author:

    <PERSON> (SNAKE FIGHTER) (lindakup) 03-28-2025

--*/
#pragma once
#include "pch.h"

class ADAgentTools
{
private:
    
    bool
    ADAgentTools::GetNameFromPrompt(
        _In_ const std::wstring& wstrPrompt,
        _Out_ std::wstring& wstrName
    );

public:
    ADAgentTools(/* args */);
    ~ADAgentTools();

    bool
    RunCommand(
        _In_ const std::wstring& wstrCommand,
        _Out_ std::wstring& wstrOutput
    );

    bool
    CheckDomainControllerHealth(
        _In_ const std::wstring& wstrDcName,
        _Out_ std::wstring& wstrHealthStatus
    );

    bool
    ListDomainControllersInDomain(
        _In_ const std::wstring& wstrDomainName,
        _Out_ std::wstring& dcListString
    );

    bool
    GetDomainControllerInformation(
        _In_ const std::wstring& wstrDcName,
        _Out_ std::wstring& wstrDcInfo
    );
};

// SNAKE_TODO: move this to common header like StringUtils.h
bool
FindStringCaseInsensitive(
    _In_ const std::wstring& inputString,
    _In_ const std::wstring& searchFor
    );
