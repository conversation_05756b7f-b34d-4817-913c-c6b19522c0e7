/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    GetADForestTool.cpp

Abstract:

    Implementation of Get-ADForest tool for Active Directory MCP server.
    Provides comprehensive AD forest query functionality using native Win32 LDAP APIs
    while maintaining PowerShell cmdlet parameter compatibility.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 2025-7-16

--*/

#include "AdMcpSvr.h"
#include "StringUtils.h"
#include "AimxConstants.h"
#include "../aimxsrv/inc/wpp.h"

// WPP tracing
#include "GetADForestTool.cpp.tmh"

HRESULT AdMcpSvr::GetADForestTool(
    _In_ const nlohmann::json& parameters,
    _Out_ nlohmann::json& result
    )
/*++

Routine Description:
    Implementation of Get-ADForest tool.

Arguments:
    parameters - Input parameters matching PowerShell Get-ADForest cmdlet
    result - Receives forest information as JSON

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    TraceInfo(AdMcpSvr, L"AdMcpSvr::GetADForestTool called");

    try
    {
        result = nlohmann::json::object();

        // Initialize LDAP connection if needed
        std::wstring serverName;
        if (parameters.contains("Server") && parameters["Server"].is_string())
        {
            serverName = Utf8ToWide(parameters["Server"].get<std::string>());
        }

        HRESULT hr = InitializeLdapConnection(serverName);
        if (FAILED(hr))
        {
            return CreateLdapErrorResponse(hr, L"Failed to initialize LDAP connection", result);
        }

        // Get forest attributes - query the domain object for forest information
        std::vector<std::wstring> attributes = GetDefaultForestAttributes();

        // Execute LDAP search for domain object to get forest information
        std::vector<nlohmann::json> forests;
        hr = ExecuteLdapSearch(m_defaultNamingContext, L"(objectClass=domainDNS)", attributes, LDAP_SCOPE_BASE, forests);
        if (FAILED(hr))
        {
            return CreateLdapErrorResponse(hr, L"LDAP search for forest failed", result);
        }

        if (!forests.empty())
        {
            result["forest"] = forests[0];
        }
        else
        {
            result["forest"] = nlohmann::json::object();
        }

        result["server"] = WideToUtf8(m_domainController);

        TraceInfo(AdMcpSvr, L"AdMcpSvr::GetADForestTool completed successfully");
        return S_OK;
    }
    catch (const std::exception& ex)
    {
        TraceErr(AdMcpSvr, L"Exception in GetADForestTool: %s", ex.what());
        result = CreateErrorResponse(L"Failed to execute Get-ADForest: " + Utf8ToWide(ex.what()), L"execution_error");
        return E_FAIL;
    }
}

std::vector<std::wstring> AdMcpSvr::GetDefaultForestAttributes()
/*++

Routine Description:
    Get default attributes for forest objects.

Return Value:
    Vector of default forest attribute names

--*/
{
    return {
        L"distinguishedName", L"objectGUID", L"name", L"dnsRoot", L"canonicalName",
        L"forestFunctionality", L"msDS-Behavior-Version", L"objectClass",
        L"whenCreated", L"whenChanged", L"fSMORoleOwner", L"masteredBy",
        L"domainFunctionality", L"netBIOSName"
    };
}
