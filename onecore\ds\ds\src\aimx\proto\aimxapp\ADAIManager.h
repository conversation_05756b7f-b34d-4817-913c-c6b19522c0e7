/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    ADAIManager.h

Abstract:
    - This module defines the ADAIManager class, which is responsible for
    - initializing and managing the various components of the AIMX application.
    - Objectives of the ADAIManager
        - Centralized Orchestration: Manage interactions between ADAgent, ADAgentTools, and LlmService.
        - Abstraction: Provide a unified interface for higher-level operations, abstracting the complexity of individual components.
        - Error Handling: Centralize error handling and logging for better observability.
        - Extensibility: Allow for future enhancements, such as adding new actions or integrating additional services.

Author:

    <PERSON><PERSON><PERSON><PERSON> (pumathur) 04/15/2025

--*/

#pragma once

#include <Windows.h>
#include <wrl.h>
#include <wil/com.h>
#include <WebView2.h>
#include <WebView2EnvironmentOptions.h>
#include "LlmService.h"
#include "ADAgent.h"
#include "WebViewMessageHandler.h"
#include "AppConfig.h"

class ADAIManager
{
public:
    // Static method to get the singleton instance
    static ADAIManager* GetInstance();
    
    bool Initialize();
    void Shutdown();

    LlmService* GetLlmService() const;
    ADAgent* GetADAgent() const;
    

private:
    static ADAIManager* adaiManagerInstance;

    ADAIManager();
    ~ADAIManager();

    // Delete copy constructor and assignment operator
    ADAIManager(const ADAIManager&) = delete;
    ADAIManager& operator=(const ADAIManager&) = delete;

    // Initialization functions
    bool InitializeRAG();
    bool InitializeComponents();

    // Cleanup functions
    void CleanupComponents();

    // Components
    std::unique_ptr<LlmService> llmService;
    std::unique_ptr<ADAgent> adAgent;  
};