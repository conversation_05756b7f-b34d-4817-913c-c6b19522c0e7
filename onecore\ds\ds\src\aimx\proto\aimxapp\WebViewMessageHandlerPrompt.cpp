/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    WebViewMessageHandlerPrompt.cpp

Abstract:

    This module implements prompt handling functionality for the WebView message handler.
    Handles processing and streaming of LLM prompts.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 03/28/2025

--*/

#include "pch.h"
#include "WebViewMessageHandler.h"
#include "LlmService.h"
#include <sstream>
#include <string>
#include <algorithm>

/*++

Routine Description:

    Processes a prompt from the WebView in streaming mode.

Arguments:

    prompt - The prompt text to process.
    useRag - Whether to use RAG for this prompt.

Return Value:

    None.

--*/
void
WebViewMessageHandler::ProcessPromptStreaming(
    _In_ const std::wstring& prompt,
    _In_ bool useRag
    )
{
    LOGINFO("Processing prompt in streaming mode: " + WideToUtf8(prompt.substr(0, 50)) + "..." + 
              (useRag ? " with RAG" : " without RAG"));
    
    if (!_llmService)
    {
        LOGERROR("Cannot process prompt: LLM service is null");
        
        // Send error message back to the WebView
        std::wstring errorMessage = L"{\"type\":\"error\",\"content\":\"LLM service unavailable\"}";
        SendMessageToWebView(errorMessage);
        return;
    }
    
    // Generate a unique session ID for this streaming response
    std::wstring sessionId = GenerateSessionId();
    
    // Send message indicating streaming is starting
    std::wostringstream startJson;
    startJson << L"{\"type\":\"streamStart\",\"sessionId\":\"" << sessionId << "\"}";
    SendMessageToWebView(startJson.str());
    
    // Keep track of chunks for debugging
    int chunkCount = 0;
    
    // Create a callback for streaming updates
    auto streamCallback = [this, sessionId, &chunkCount](const std::wstring& chunk, bool isComplete) 
    {
        try 
        {
            if (isComplete) 
            {
                // Send end of stream message
                std::wostringstream endJson;
                endJson << L"{\"type\":\"streamEnd\",\"sessionId\":\"" << sessionId << "\"}";
                SendMessageToWebView(endJson.str());
                LOGVERBOSE("Streaming session " + WideToUtf8(sessionId) + " completed (" + 
                           std::to_string(chunkCount) + " chunks)");
            } 
            else 
            {
                // Increment chunk counter
                chunkCount++;
                
                // Skip empty chunks
                if (chunk.empty()) 
                {
                    LOGVERBOSE("Skipping empty chunk for session " + WideToUtf8(sessionId));
                    return;
                }
                
                // Check if chunk contains JSON data that shouldn't be displayed
                std::wstring cleanedChunk = chunk;
                if (cleanedChunk.find(L"{") != std::wstring::npos && cleanedChunk.find(L"index") != std::wstring::npos) 
                {
                    try 
                    {
                        auto jsonData = nlohmann::json::parse(WideToUtf8(cleanedChunk));
                        // If this is valid JSON with content field, extract just the content
                        if (jsonData.contains("content") && jsonData["content"].is_string()) 
                        {
                            cleanedChunk = Utf8ToWide(jsonData["content"].get<std::string>());
                            LOGINFO("Extracted content from JSON chunk: " + WideToUtf8(cleanedChunk));
                        }
                    } 
                    catch (...) 
                    {
                        // Not valid JSON or other issue, keep as is
                        LOGVERBOSE("Could not parse potential JSON in chunk");
                    }
                }

                // Remove any control characters that aren't handled by the escaping
                cleanedChunk.erase(
                    std::remove_if(cleanedChunk.begin(), cleanedChunk.end(), 
                                   [](wchar_t c){ return c < 32 && c != L'\r' && c != L'\n' && c != L'\t'; }),
                    cleanedChunk.end());
                
                // Send streaming chunk
                std::wostringstream updateJson;
                updateJson << L"{\"type\":\"streamUpdate\",\"sessionId\":\"" << sessionId 
                          << L"\",\"content\":\"" << EscapeJsonStringW(cleanedChunk) << "\"}";
                SendMessageToWebView(updateJson.str());
                
                if (chunkCount % 5 == 0 || chunkCount < 5) 
                {
                    LOGVERBOSE("Sent streaming chunk #" + std::to_string(chunkCount) + 
                              " for session " + WideToUtf8(sessionId) + 
                              ", length: " + std::to_string(cleanedChunk.length()));
                }
            }
        } 
        catch (const std::exception& e) 
        {
            LOGERROR("Error in stream callback: " + std::string(e.what()));
        }
    };

    // Entry point for the AD AI Agent. Call the ADAI Agent to process the prompt.
    // this is where the agent will take action if needed.
    // The agent will return the result as a new prompt to the LLM service to summarize.
    // If no action is needed, the agent will return an unchanged prompt to the LLM service.
    // In the future, when function calling is available, the order of operations will need to change
    // And the agent will need to check the LLM response to see if it contains a function call and execute it.
    // For now, we just call the agent and let it decide if action is needed.
    bool success = false;
    std::wstring wstrActionResult;
    success = _adAgent->ProcessPrompt(prompt, wstrActionResult);

    // Start streaming the LLM response with or without RAG
    if (useRag && _llmService->IsRagInitialized())
    {
        success = _llmService->StreamPromptWithRag(wstrActionResult, streamCallback);
    }
    else
    {
        success = _llmService->StreamPrompt(wstrActionResult, streamCallback);
    }
    
    if (!success)
    {
        LOGERROR("Failed to start streaming prompt");
        
        // Send error message back to the WebView
        std::wostringstream errorJson;
        errorJson << L"{\"type\":\"error\",\"sessionId\":\"" << sessionId 
                 << L"\",\"content\":\"Failed to process prompt\"}";
        SendMessageToWebView(errorJson.str());
    }
}

/*++

Routine Description:

    Processes a prompt from the WebView in non-streaming mode (legacy).

Arguments:

    prompt - The prompt text to process.

Return Value:

    None.

--*/
void
WebViewMessageHandler::ProcessPrompt(
    _In_ const std::wstring& prompt
    )
{
    LOGVERBOSE("Processing prompt (non-streaming): " + WideToUtf8(prompt.substr(0, 50)) + "...");
    
    if (!_llmService) 
    {
        LOGERROR("Cannot process prompt: LLM service is null");
        return;
    }
    
    std::wstring response;
    bool success = _llmService->SendPrompt(prompt, response);
    
    if (success) 
    {
        // Construct JSON response
        std::wostringstream json;
        json << L"{\"type\":\"assistantMessage\",\"content\":\"" << EscapeJsonStringW(response) << "\"}";
        SendMessageToWebView(json.str());
    } 
    else 
    {
        std::wstring errorMessage = L"{\"type\":\"error\",\"content\":\"Failed to process prompt\"}";
        SendMessageToWebView(errorMessage);
    }
}
