

# ============================================================================
# Copyright (c) Microsoft Corporation. All rights reserved.
#
# File: aimxchat.ps1
# Description: AIMX Chat Script for testing AIMX server interaction and response handling.
# Author: <PERSON> (SNAKE FIGHTER) (lindakup)
# Date: 07/04/2025
# ============================================================================
# Import the AIMX PowerShell module
Import-Module AIMX -ErrorAction Stop

# AIMX Chat Script
# This script prompts the user for a question, connects to the AIMX server, sends the question, show progress and receives the response.
# intended for testing AIMX server interaction and response handling and as an example of how to use the AIMX PowerShell module and protocol.
# Prompt user for input
$promptText = Read-Host "Enter your AIMX question or prompt"

# Connect to AIMX server
$context = $null
try {
    $context = Connect-AimxServer
    Write-Host "Connected to AIMX server."

    # Build JSON prompt for initial request
    $initialPrompt = "{""requestType"":1, ""query"":""$promptText"" , ""executionMode"":2}"
    Write-Host "sending initial prompt: $initialPrompt"

    $response = Get-AimxQuestionResponse -PromptText $initialPrompt -Context $context
    Write-Host "Response from AIMX server: $response"

    # Parse the response as JSON and extract operationId
    $operationId = $null
    try {
        $jsonResponse = $response | ConvertFrom-Json
        if ($jsonResponse.operationId) {
            $operationId = $jsonResponse.operationId
            Write-Host "OperationId: $operationId"
        } else {
            Write-Host "operationId not found in JSON response."
        }
    } catch {
        Write-Host "Failed to parse response as JSON."
    }

    # Check the planning status and loop until it's ready or timeout after 30 attempts
    $planStatus = "{""requestType"":3, ""operationId"":""$operationId""}"
    Write-Host "Requesting planStatus: $planStatus"
    $planReady = $false
    $planStart = Get-Date
    $planLoopCount = 0
    $planResponse = Get-AimxQuestionResponse -PromptText $planStatus -Context $context
    Write-Host "Plan status response: $planResponse"
    try {
        $planJson = $planResponse | ConvertFrom-Json
        if ($planJson.status -eq "PLAN_READY") {
            $planReady = $true
        } else {
            Start-Sleep -Seconds 1
        }
    } catch {
        Write-Host "Failed to parse plan status response as JSON."
        Start-Sleep -Seconds 1
    }
    while (-not $planReady -and $planLoopCount -lt 30) {
        $planResponse = Get-AimxQuestionResponse -PromptText $planStatus -Context $context
        $planLoopCount++
        Write-Host -NoNewline "."
        try {
            $planJson = $planResponse | ConvertFrom-Json
            if ($planJson.status -eq "PLAN_READY") {
                $planReady = $true
            } else {
                Start-Sleep -Seconds 2
            }
        } catch {
            Write-Host "Failed to parse plan status response as JSON."
            Start-Sleep -Seconds 2
        }
    }
    if (-not $planReady) {
        Write-Host "PLANNING phase did not complete after 30 polling attempts. Exiting."
    }
    Write-Host "PLANNING phase completed successfully."
    Write-Host "========================================================="

    #dump some statistics
    Write-Host ("PLANNING phase required {0} polling calls." -f $planLoopCount)
    $planEnd = Get-Date
    $planDuration = $planEnd - $planStart
    Write-Host ("PLANNING phase took: {0} seconds" -f [math]::Round($planDuration.TotalSeconds,2))

    Write-Host "=========================================================="
    # Proceed with execution once the plan is ready
    $execution = "{""requestType"":4, ""operationId"":""$operationId""}"
    Write-Host "Requesting execution: $execution"
    $execStart = Get-Date
    $executionResponse = Get-AimxQuestionResponse -PromptText $execution -Context $context
    Write-Host "Execution response: $executionResponse"

    Write-Host "=========================================================="
    # Final result request (requestType 3 again), poll until status is not EXECUTING
    $finalResult = "{""requestType"":3, ""operationId"":""$operationId""}"
    Write-Host "Requesting finalResult....."
    $finalDone = $false
    $finalLoopCount = 0
    while (-not $finalDone -and $finalLoopCount -lt 30) {
        $finalResponse = Get-AimxQuestionResponse -PromptText $finalResult -Context $context
        $finalLoopCount++
        Write-Host -NoNewline "."
        if (-not $finalResponse) {
            Write-Host "No response or error from Get-AimxQuestionResponse during final result polling. Exiting loop."
            break
        }
        try {
            $finalJson = $finalResponse | ConvertFrom-Json
            if ($finalJson.status -ne "EXECUTING") {
                $finalDone = $true
            } else {
                Start-Sleep -Seconds 2
            }
        } catch {
            Write-Host "Failed to parse final response as JSON. Exiting loop."
            break
        }
    }
    if (-not $finalDone) {
        Write-Host "FINAL RESULT phase did not complete after 30 polling attempts. Exiting."
    }
    Write-Host ""
    Write-Host ("FINAL RESULT phase required {0} polling calls." -f $finalLoopCount)

    $execEnd = Get-Date
    $execDuration = $execEnd - $execStart
    Write-Host ("EXECUTION phase took: {0} seconds" -f [math]::Round($execDuration.TotalSeconds,2))
    Write-Host "Final response status: $($finalJson.status)"
    Write-Host "=========================================================="

    # Display nicely formatted command output if present, handling nested JSON and escape sequences
    try {
        if ($finalJson.result) {
            $innerResult = $finalJson.result | ConvertFrom-Json
            if ($innerResult.resultData.combinedResults) {
                # Replace escaped newlines with real newlines for better readability
                $cleaned = $innerResult.resultData.combinedResults -replace "\\n", "`n" -replace "\\r", ""
                $lines = $cleaned -split "`n"
                $objects = @()
                foreach ($line in $lines) {
                    if ($line -match "^(.*?):\s*(.*)$") {
                        $objects += [PSCustomObject]@{
                            Property = $matches[1].Trim()
                            Value    = $matches[2].Trim()
                        }
                    }
                }
                if ($objects.Count -gt 0) {
                    $objects | Format-Table -AutoSize
                } else {
                    Write-Host "Combined Results:`n$cleaned"
                }
            } else {
                Write-Host "No combinedResults field found in resultData."
            }
            if ($innerResult.message) {
                Write-Host "Message: $($innerResult.message)"
            }
        } elseif ($finalJson.output) {
            $cleaned = $finalJson.output -replace "\\n", "`n" -replace "\\r", ""
            Write-Host "Command Output:`n$cleaned"
        } else {
            Write-Host "No output or result field found in final response."
        }
    } catch {
        Write-Host "Failed to parse/display final response as JSON."
    }

}
finally {
    if ($context) {
        Close-AimxServer -Context $context
        Write-Host "AIMX server context closed."
    }
}
