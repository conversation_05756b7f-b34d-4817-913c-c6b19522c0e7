<#
.SYNOPSIS
    Active Directory Service Account Migration Tools for MCP Server
    
.DESCRIPTION
    This module registers MCP tools for Active Directory service account migration operations.
    Each tool is a direct wrapper around the corresponding AD PowerShell cmdlet with
    exact parameter passthrough and no output formatting.

.AUTHOR
    Rupo Zhang (rizhang)
#>

# Import required modules
Import-Module ActiveDirectory -ErrorAction SilentlyContinue

function Register-ServiceAccountMigrationTools {
    [CmdletBinding()]
    param()

    # Start-ADServiceAccountMigration - Starts the migration process by linking a normal user account to a delegated managed service account
    Register-McpTool -Name "Start-ADServiceAccountMigration" -Description "Starts the migration process by linking a normal user account to a delegated managed service account." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.ManagedServiceAccount) { $params.ManagedServiceAccount = $Arguments.ManagedServiceAccount }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Start-ADServiceAccountMigration @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "User account identity to migrate (DN, GUID, SID, or SAM account name)" }
            ManagedServiceAccount = @{ type = "string"; description = "Target managed service account identity" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the user account object" }
        }
        required = @("Identity", "ManagedServiceAccount")
    }

    # Complete-ADServiceAccountMigration - Completes the migration process and supersedes a normal user account to a delegated managed service account
    Register-McpTool -Name "Complete-ADServiceAccountMigration" -Description "Completes the migration process and supersedes a normal user account to a delegated managed service account." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Complete-ADServiceAccountMigration @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "User account identity being migrated (DN, GUID, SID, or SAM account name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the user account object" }
        }
        required = @("Identity")
    }

    # Reset-ADServiceAccountMigration - Resets the state of a migration to a delegated managed service account and unlinks the delegated managed service account from the user account
    Register-McpTool -Name "Reset-ADServiceAccountMigration" -Description "Resets the state of a migration to a delegated managed service account and unlinks the delegated managed service account from the user account." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Reset-ADServiceAccountMigration @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "User account identity being migrated (DN, GUID, SID, or SAM account name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the user account object" }
        }
        required = @("Identity")
    }

    # Undo-ADServiceAccountMigration - Reverts the previous migration phase of a migration to a delegated managed service account
    Register-McpTool -Name "Undo-ADServiceAccountMigration" -Description "Reverts the previous migration phase of a migration to a delegated managed service account." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Undo-ADServiceAccountMigration @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "User account identity being migrated (DN, GUID, SID, or SAM account name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the user account object" }
        }
        required = @("Identity")
    }

    # Reset-ADServiceAccountPassword - Resets the password for a standalone managed service account
    Register-McpTool -Name "Reset-ADServiceAccountPassword" -Description "Resets the password for a standalone managed service account." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        
        Reset-ADServiceAccountPassword @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Service account identity (DN, GUID, SID, or SAM account name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
        required = @("Identity")
    }

    # Set-ADClaimType - Modify a claim type in Active Directory
    Register-McpTool -Name "Set-ADClaimType" -Description "Modify a claim type in Active Directory." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Add) { $params.Add = $Arguments.Add }
        if ($Arguments.Clear) { $params.Clear = $Arguments.Clear }
        if ($Arguments.Remove) { $params.Remove = $Arguments.Remove }
        if ($Arguments.Replace) { $params.Replace = $Arguments.Replace }
        if ($Arguments.DisplayName) { $params.DisplayName = $Arguments.DisplayName }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.Enabled) { $params.Enabled = $Arguments.Enabled }
        if ($Arguments.RestrictValues) { $params.RestrictValues = $Arguments.RestrictValues }
        if ($Arguments.SuggestedValues) { $params.SuggestedValues = $Arguments.SuggestedValues }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Set-ADClaimType @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Claim type identity (DN, GUID, or name)" }
            Add = @{ type = "object"; description = "Attributes to add as hashtable" }
            Clear = @{ type = "array"; items = @{ type = "string" }; description = "Attributes to clear" }
            Remove = @{ type = "object"; description = "Attributes to remove as hashtable" }
            Replace = @{ type = "object"; description = "Attributes to replace as hashtable" }
            DisplayName = @{ type = "string"; description = "Display name for the claim type" }
            Description = @{ type = "string"; description = "Description of the claim type" }
            Enabled = @{ type = "boolean"; description = "Whether the claim type is enabled" }
            RestrictValues = @{ type = "boolean"; description = "Whether to restrict values to suggested values" }
            SuggestedValues = @{ type = "array"; items = @{ type = "string" }; description = "Suggested values for the claim type" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified claim type object" }
        }
        required = @("Identity")
    }

    # Remove-ADClaimType - Removes a claim type from Active Directory
    Register-McpTool -Name "Remove-ADClaimType" -Description "Removes a claim type from Active Directory." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Confirm) { $params.Confirm = $Arguments.Confirm }
        if ($Arguments.WhatIf) { $params.WhatIf = $Arguments.WhatIf }
        
        Remove-ADClaimType @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Claim type identity (DN, GUID, or name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Confirm = @{ type = "boolean"; description = "Prompt for confirmation before removing" }
            WhatIf = @{ type = "boolean"; description = "Show what would happen without executing" }
        }
        required = @("Identity")
    }

    # Show-ADAuthenticationPolicyExpression - Displays the Edit Access Control Conditions window update or create security descriptor definition language (SDDL) security descriptors
    Register-McpTool -Name "Show-ADAuthenticationPolicyExpression" -Description "Displays the Edit Access Control Conditions window update or create security descriptor definition language (SDDL) security descriptors." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.AllowedToAuthenticateFrom) { $params.AllowedToAuthenticateFrom = $Arguments.AllowedToAuthenticateFrom }
        if ($Arguments.AllowedToAuthenticateTo) { $params.AllowedToAuthenticateTo = $Arguments.AllowedToAuthenticateTo }
        if ($Arguments.Title) { $params.Title = $Arguments.Title }
        
        Show-ADAuthenticationPolicyExpression @params
    } -InputSchema @{
        type = "object"
        properties = @{
            AllowedToAuthenticateFrom = @{ type = "string"; description = "SDDL string for allowed to authenticate from condition" }
            AllowedToAuthenticateTo = @{ type = "string"; description = "SDDL string for allowed to authenticate to condition" }
            Title = @{ type = "string"; description = "Title for the dialog window" }
        }
    }
}

# Function is available after dot-sourcing
