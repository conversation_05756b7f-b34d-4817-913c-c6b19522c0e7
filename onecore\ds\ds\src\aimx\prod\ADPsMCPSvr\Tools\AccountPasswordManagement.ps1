<#
.SYNOPSIS
    Active Directory Account and Password Management Tools for MCP Server
    
.DESCRIPTION
    This module registers MCP tools for Active Directory account and password management operations.
    Each tool is a direct wrapper around the corresponding AD PowerShell cmdlet with
    exact parameter passthrough and no output formatting.

.AUTHOR
    Rupo Zhang (rizhang)
#>

# Import required modules
Import-Module ActiveDirectory -ErrorAction SilentlyContinue

function Register-AccountPasswordManagementTools {
    [CmdletBinding()]
    param()

    # Set-ADAccountControl - Modifies user account control (UAC) values for an Active Directory account
    Register-McpTool -Name "Set-ADAccountControl" -Description "Modifies user account control (UAC) values for an Active Directory account." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.AccountNotDelegated) { $params.AccountNotDelegated = $Arguments.AccountNotDelegated }
        if ($Arguments.AllowReversiblePasswordEncryption) { $params.AllowReversiblePasswordEncryption = $Arguments.AllowReversiblePasswordEncryption }
        if ($Arguments.CannotChangePassword) { $params.CannotChangePassword = $Arguments.CannotChangePassword }
        if ($Arguments.DoesNotRequirePreAuth) { $params.DoesNotRequirePreAuth = $Arguments.DoesNotRequirePreAuth }
        if ($Arguments.Enabled) { $params.Enabled = $Arguments.Enabled }
        if ($Arguments.MNSLogonAccount) { $params.MNSLogonAccount = $Arguments.MNSLogonAccount }
        if ($Arguments.PasswordNeverExpires) { $params.PasswordNeverExpires = $Arguments.PasswordNeverExpires }
        if ($Arguments.PasswordNotRequired) { $params.PasswordNotRequired = $Arguments.PasswordNotRequired }
        if ($Arguments.SmartcardLogonRequired) { $params.SmartcardLogonRequired = $Arguments.SmartcardLogonRequired }
        if ($Arguments.TrustedForDelegation) { $params.TrustedForDelegation = $Arguments.TrustedForDelegation }
        if ($Arguments.TrustedToAuthForDelegation) { $params.TrustedToAuthForDelegation = $Arguments.TrustedToAuthForDelegation }
        if ($Arguments.UseDESKeyOnly) { $params.UseDESKeyOnly = $Arguments.UseDESKeyOnly }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Set-ADAccountControl @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Account identity (DN, GUID, SID, or SAM account name)" }
            AccountNotDelegated = @{ type = "boolean"; description = "Account is sensitive and cannot be delegated" }
            AllowReversiblePasswordEncryption = @{ type = "boolean"; description = "Allow reversible password encryption" }
            CannotChangePassword = @{ type = "boolean"; description = "User cannot change password" }
            DoesNotRequirePreAuth = @{ type = "boolean"; description = "Account does not require Kerberos pre-authentication" }
            Enabled = @{ type = "boolean"; description = "Account is enabled" }
            MNSLogonAccount = @{ type = "boolean"; description = "Account is an MNS logon account" }
            PasswordNeverExpires = @{ type = "boolean"; description = "Password never expires" }
            PasswordNotRequired = @{ type = "boolean"; description = "Password is not required" }
            SmartcardLogonRequired = @{ type = "boolean"; description = "Smart card is required for logon" }
            TrustedForDelegation = @{ type = "boolean"; description = "Account is trusted for delegation" }
            TrustedToAuthForDelegation = @{ type = "boolean"; description = "Account is trusted to authenticate for delegation" }
            UseDESKeyOnly = @{ type = "boolean"; description = "Use DES encryption types only" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            PassThru = @{ type = "boolean"; description = "Return the account object" }
        }
        required = @("Identity")
    }

    # Search-ADAccount - Gets Active Directory user, computer, or service accounts
    Register-McpTool -Name "Search-ADAccount" -Description "Gets Active Directory user, computer, or service accounts based on account properties and states." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.AccountDisabled) { $params.AccountDisabled = $Arguments.AccountDisabled }
        if ($Arguments.AccountExpired) { $params.AccountExpired = $Arguments.AccountExpired }
        if ($Arguments.AccountExpiring) { $params.AccountExpiring = $Arguments.AccountExpiring }
        if ($Arguments.AccountInactive) { $params.AccountInactive = $Arguments.AccountInactive }
        if ($Arguments.LockedOut) { $params.LockedOut = $Arguments.LockedOut }
        if ($Arguments.PasswordExpired) { $params.PasswordExpired = $Arguments.PasswordExpired }
        if ($Arguments.PasswordNeverExpires) { $params.PasswordNeverExpires = $Arguments.PasswordNeverExpires }
        if ($Arguments.UsersOnly) { $params.UsersOnly = $Arguments.UsersOnly }
        if ($Arguments.ComputersOnly) { $params.ComputersOnly = $Arguments.ComputersOnly }
        if ($Arguments.DateTime) { $params.DateTime = $Arguments.DateTime }
        if ($Arguments.TimeSpan) { $params.TimeSpan = $Arguments.TimeSpan }
        if ($Arguments.ResultPageSize) { $params.ResultPageSize = $Arguments.ResultPageSize }
        if ($Arguments.ResultSetSize) { $params.ResultSetSize = $Arguments.ResultSetSize }
        if ($Arguments.SearchBase) { $params.SearchBase = $Arguments.SearchBase }
        if ($Arguments.SearchScope) { $params.SearchScope = $Arguments.SearchScope }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        
        Search-ADAccount @params
    } -InputSchema @{
        type = "object"
        properties = @{
            AccountDisabled = @{ type = "boolean"; description = "Search for disabled accounts" }
            AccountExpired = @{ type = "boolean"; description = "Search for expired accounts" }
            AccountExpiring = @{ type = "boolean"; description = "Search for accounts expiring soon" }
            AccountInactive = @{ type = "boolean"; description = "Search for inactive accounts" }
            LockedOut = @{ type = "boolean"; description = "Search for locked out accounts" }
            PasswordExpired = @{ type = "boolean"; description = "Search for accounts with expired passwords" }
            PasswordNeverExpires = @{ type = "boolean"; description = "Search for accounts with non-expiring passwords" }
            UsersOnly = @{ type = "boolean"; description = "Search user accounts only" }
            ComputersOnly = @{ type = "boolean"; description = "Search computer accounts only" }
            DateTime = @{ type = "string"; description = "Reference date/time for searches" }
            TimeSpan = @{ type = "string"; description = "Time span for relative searches" }
            ResultPageSize = @{ type = "integer"; description = "Number of objects per page" }
            ResultSetSize = @{ type = "integer"; description = "Maximum number of objects to return" }
            SearchBase = @{ type = "string"; description = "Active Directory path to search under" }
            SearchScope = @{ type = "string"; enum = @("Base", "OneLevel", "Subtree"); description = "Scope of the search" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
    }

    # Add-ADPrincipalGroupMembership - Adds a member to one or more Active Directory groups
    Register-McpTool -Name "Add-ADPrincipalGroupMembership" -Description "Adds a member to one or more Active Directory groups." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.MemberOf) { $params.MemberOf = $Arguments.MemberOf }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Add-ADPrincipalGroupMembership @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Principal identity (DN, GUID, SID, or SAM account name)" }
            MemberOf = @{ type = "array"; items = @{ type = "string" }; description = "Array of group identities to add the principal to" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            PassThru = @{ type = "boolean"; description = "Return the principal object" }
        }
        required = @("Identity", "MemberOf")
    }

    # Remove-ADPrincipalGroupMembership - Removes a member from one or more Active Directory groups
    Register-McpTool -Name "Remove-ADPrincipalGroupMembership" -Description "Removes a member from one or more Active Directory groups." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.MemberOf) { $params.MemberOf = $Arguments.MemberOf }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        if ($Arguments.Confirm) { $params.Confirm = $Arguments.Confirm }
        
        Remove-ADPrincipalGroupMembership @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Principal identity (DN, GUID, SID, or SAM account name)" }
            MemberOf = @{ type = "array"; items = @{ type = "string" }; description = "Array of group identities to remove the principal from" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            PassThru = @{ type = "boolean"; description = "Return the principal object" }
            Confirm = @{ type = "boolean"; description = "Prompt for confirmation before removing" }
        }
        required = @("Identity", "MemberOf")
    }

    # Get-ADAccountAuthorizationGroup - Gets the accounts token group information
    Register-McpTool -Name "Get-ADAccountAuthorizationGroup" -Description "Gets the token groups for an Active Directory account, which represent the security groups used for authorization." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        
        Get-ADAccountAuthorizationGroup @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Account identity (DN, GUID, SID, or SAM account name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
        }
        required = @("Identity")
    }

    # Get-ADUserResultantPasswordPolicy - Gets the resultant password policy for a user
    Register-McpTool -Name "Get-ADUserResultantPasswordPolicy" -Description "Gets the resultant password policy that applies to a specific Active Directory user." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        
        Get-ADUserResultantPasswordPolicy @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "User identity (DN, GUID, SID, or SAM account name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
        }
        required = @("Identity")
    }

    # Get-ADFineGrainedPasswordPolicy - Gets one or more Active Directory fine-grained password policies
    Register-McpTool -Name "Get-ADFineGrainedPasswordPolicy" -Description "Gets one or more Active Directory fine-grained password policies." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Filter) { $params.Filter = $Arguments.Filter }
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.LDAPFilter) { $params.LDAPFilter = $Arguments.LDAPFilter }
        if ($Arguments.Properties) { $params.Properties = $Arguments.Properties }
        if ($Arguments.ResultPageSize) { $params.ResultPageSize = $Arguments.ResultPageSize }
        if ($Arguments.ResultSetSize) { $params.ResultSetSize = $Arguments.ResultSetSize }
        if ($Arguments.SearchBase) { $params.SearchBase = $Arguments.SearchBase }
        if ($Arguments.SearchScope) { $params.SearchScope = $Arguments.SearchScope }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        
        Get-ADFineGrainedPasswordPolicy @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Filter = @{ type = "string"; description = "PowerShell Expression Language filter string" }
            Identity = @{ type = "string"; description = "Policy identity (DN, GUID, or name)" }
            LDAPFilter = @{ type = "string"; description = "LDAP query string for filtering" }
            Properties = @{ type = "array"; items = @{ type = "string" }; description = "Additional properties to retrieve" }
            ResultPageSize = @{ type = "integer"; description = "Number of objects per page" }
            ResultSetSize = @{ type = "integer"; description = "Maximum number of objects to return" }
            SearchBase = @{ type = "string"; description = "Active Directory path to search under" }
            SearchScope = @{ type = "string"; enum = @("Base", "OneLevel", "Subtree"); description = "Scope of the search" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
    }

    # New-ADFineGrainedPasswordPolicy - Creates a new Active Directory fine-grained password policy
    Register-McpTool -Name "New-ADFineGrainedPasswordPolicy" -Description "Creates a new Active Directory fine-grained password policy." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Name) { $params.Name = $Arguments.Name }
        if ($Arguments.Precedence) { $params.Precedence = $Arguments.Precedence }
        if ($Arguments.ComplexityEnabled) { $params.ComplexityEnabled = $Arguments.ComplexityEnabled }
        if ($Arguments.LockoutDuration) { $params.LockoutDuration = $Arguments.LockoutDuration }
        if ($Arguments.LockoutObservationWindow) { $params.LockoutObservationWindow = $Arguments.LockoutObservationWindow }
        if ($Arguments.LockoutThreshold) { $params.LockoutThreshold = $Arguments.LockoutThreshold }
        if ($Arguments.MaxPasswordAge) { $params.MaxPasswordAge = $Arguments.MaxPasswordAge }
        if ($Arguments.MinPasswordAge) { $params.MinPasswordAge = $Arguments.MinPasswordAge }
        if ($Arguments.MinPasswordLength) { $params.MinPasswordLength = $Arguments.MinPasswordLength }
        if ($Arguments.PasswordHistoryCount) { $params.PasswordHistoryCount = $Arguments.PasswordHistoryCount }
        if ($Arguments.ReversibleEncryptionEnabled) { $params.ReversibleEncryptionEnabled = $Arguments.ReversibleEncryptionEnabled }
        if ($Arguments.DisplayName) { $params.DisplayName = $Arguments.DisplayName }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.ProtectedFromAccidentalDeletion) { $params.ProtectedFromAccidentalDeletion = $Arguments.ProtectedFromAccidentalDeletion }
        if ($Arguments.OtherAttributes) { $params.OtherAttributes = $Arguments.OtherAttributes }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        New-ADFineGrainedPasswordPolicy @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Name = @{ type = "string"; description = "Name of the password policy (required)" }
            Precedence = @{ type = "integer"; description = "Precedence value (required)" }
            ComplexityEnabled = @{ type = "boolean"; description = "Enable password complexity requirements" }
            LockoutDuration = @{ type = "string"; description = "Account lockout duration (timespan)" }
            LockoutObservationWindow = @{ type = "string"; description = "Lockout observation window (timespan)" }
            LockoutThreshold = @{ type = "integer"; description = "Account lockout threshold" }
            MaxPasswordAge = @{ type = "string"; description = "Maximum password age (timespan)" }
            MinPasswordAge = @{ type = "string"; description = "Minimum password age (timespan)" }
            MinPasswordLength = @{ type = "integer"; description = "Minimum password length" }
            PasswordHistoryCount = @{ type = "integer"; description = "Password history count" }
            ReversibleEncryptionEnabled = @{ type = "boolean"; description = "Enable reversible encryption" }
            DisplayName = @{ type = "string"; description = "Display name for the policy" }
            Description = @{ type = "string"; description = "Description of the policy" }
            ProtectedFromAccidentalDeletion = @{ type = "boolean"; description = "Protect from accidental deletion" }
            OtherAttributes = @{ type = "object"; description = "Additional attributes as hashtable" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the created policy object" }
        }
        required = @("Name", "Precedence")
    }

    # Set-ADFineGrainedPasswordPolicy - Modifies an Active Directory fine-grained password policy
    Register-McpTool -Name "Set-ADFineGrainedPasswordPolicy" -Description "Modifies an Active Directory fine-grained password policy." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Add) { $params.Add = $Arguments.Add }
        if ($Arguments.Clear) { $params.Clear = $Arguments.Clear }
        if ($Arguments.Remove) { $params.Remove = $Arguments.Remove }
        if ($Arguments.Replace) { $params.Replace = $Arguments.Replace }
        if ($Arguments.Precedence) { $params.Precedence = $Arguments.Precedence }
        if ($Arguments.ComplexityEnabled) { $params.ComplexityEnabled = $Arguments.ComplexityEnabled }
        if ($Arguments.LockoutDuration) { $params.LockoutDuration = $Arguments.LockoutDuration }
        if ($Arguments.LockoutObservationWindow) { $params.LockoutObservationWindow = $Arguments.LockoutObservationWindow }
        if ($Arguments.LockoutThreshold) { $params.LockoutThreshold = $Arguments.LockoutThreshold }
        if ($Arguments.MaxPasswordAge) { $params.MaxPasswordAge = $Arguments.MaxPasswordAge }
        if ($Arguments.MinPasswordAge) { $params.MinPasswordAge = $Arguments.MinPasswordAge }
        if ($Arguments.MinPasswordLength) { $params.MinPasswordLength = $Arguments.MinPasswordLength }
        if ($Arguments.PasswordHistoryCount) { $params.PasswordHistoryCount = $Arguments.PasswordHistoryCount }
        if ($Arguments.ReversibleEncryptionEnabled) { $params.ReversibleEncryptionEnabled = $Arguments.ReversibleEncryptionEnabled }
        if ($Arguments.DisplayName) { $params.DisplayName = $Arguments.DisplayName }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.ProtectedFromAccidentalDeletion) { $params.ProtectedFromAccidentalDeletion = $Arguments.ProtectedFromAccidentalDeletion }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        Set-ADFineGrainedPasswordPolicy @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Policy identity (DN, GUID, or name)" }
            Add = @{ type = "object"; description = "Attributes to add as hashtable" }
            Clear = @{ type = "array"; items = @{ type = "string" }; description = "Attributes to clear" }
            Remove = @{ type = "object"; description = "Attributes to remove as hashtable" }
            Replace = @{ type = "object"; description = "Attributes to replace as hashtable" }
            Precedence = @{ type = "integer"; description = "Precedence value" }
            ComplexityEnabled = @{ type = "boolean"; description = "Enable password complexity requirements" }
            LockoutDuration = @{ type = "string"; description = "Account lockout duration (timespan)" }
            LockoutObservationWindow = @{ type = "string"; description = "Lockout observation window (timespan)" }
            LockoutThreshold = @{ type = "integer"; description = "Account lockout threshold" }
            MaxPasswordAge = @{ type = "string"; description = "Maximum password age (timespan)" }
            MinPasswordAge = @{ type = "string"; description = "Minimum password age (timespan)" }
            MinPasswordLength = @{ type = "integer"; description = "Minimum password length" }
            PasswordHistoryCount = @{ type = "integer"; description = "Password history count" }
            ReversibleEncryptionEnabled = @{ type = "boolean"; description = "Enable reversible encryption" }
            DisplayName = @{ type = "string"; description = "Display name for the policy" }
            Description = @{ type = "string"; description = "Description of the policy" }
            ProtectedFromAccidentalDeletion = @{ type = "boolean"; description = "Protect from accidental deletion" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified policy object" }
        }
        required = @("Identity")
    }

    # Remove-ADFineGrainedPasswordPolicy - Removes an Active Directory fine-grained password policy
    Register-McpTool -Name "Remove-ADFineGrainedPasswordPolicy" -Description "Removes an Active Directory fine-grained password policy." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Confirm) { $params.Confirm = $Arguments.Confirm }
        if ($Arguments.WhatIf) { $params.WhatIf = $Arguments.WhatIf }

        Remove-ADFineGrainedPasswordPolicy @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Policy identity (DN, GUID, or name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Confirm = @{ type = "boolean"; description = "Prompt for confirmation before removing" }
            WhatIf = @{ type = "boolean"; description = "Show what would happen without executing" }
        }
        required = @("Identity")
    }

    # Add-ADFineGrainedPasswordPolicySubject - Applies a fine-grained password policy to one more users and groups
    Register-McpTool -Name "Add-ADFineGrainedPasswordPolicySubject" -Description "Applies a fine-grained password policy to one or more users and groups." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Subjects) { $params.Subjects = $Arguments.Subjects }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        Add-ADFineGrainedPasswordPolicySubject @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Policy identity (DN, GUID, or name)" }
            Subjects = @{ type = "array"; items = @{ type = "string" }; description = "Array of user/group identities to apply policy to" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the policy object" }
        }
        required = @("Identity", "Subjects")
    }

    # Remove-ADFineGrainedPasswordPolicySubject - Removes one or more users from a fine-grained password policy
    Register-McpTool -Name "Remove-ADFineGrainedPasswordPolicySubject" -Description "Removes one or more users or groups from a fine-grained password policy." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Subjects) { $params.Subjects = $Arguments.Subjects }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        if ($Arguments.Confirm) { $params.Confirm = $Arguments.Confirm }

        Remove-ADFineGrainedPasswordPolicySubject @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Policy identity (DN, GUID, or name)" }
            Subjects = @{ type = "array"; items = @{ type = "string" }; description = "Array of user/group identities to remove from policy" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the policy object" }
            Confirm = @{ type = "boolean"; description = "Prompt for confirmation before removing" }
        }
        required = @("Identity", "Subjects")
    }

    # Get-ADFineGrainedPasswordPolicySubject - Gets the users and groups to which a fine-grained password policy is applied
    Register-McpTool -Name "Get-ADFineGrainedPasswordPolicySubject" -Description "Gets the users and groups to which a fine-grained password policy is applied." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }

        Get-ADFineGrainedPasswordPolicySubject @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Policy identity (DN, GUID, or name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
        required = @("Identity")
    }
}

# Function is available after dot-sourcing
