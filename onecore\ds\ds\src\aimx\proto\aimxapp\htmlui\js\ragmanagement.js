// Sample data
window.aimx = window.aimx || {};
const logOutput = document.getElementById('logOutput');
// Add a log message
function log(message) {
    const timestamp = new Date().toLocaleTimeString();
    if (logOutput) {
        logOutput.innerHTML += `[${timestamp}] ${message}\n`;
        logOutput.scrollTop = logOutput.scrollHeight;
    }
    console.log(message);
}


function handleToggle(selectedIndex) {
    const checkboxes = document.querySelectorAll('.custom-control-input');
    checkboxes.forEach((checkbox, index) => {
        checkbox.checked = index === selectedIndex;
    });

    //Send a message to native to persist new rag db in configuration files
    const selectedRag = checkboxes[selectedIndex].name;
    // Prepare the message payload
    const message = {
        type: 'ragSelection',
        action: 'toggle',
        name: selectedRag,
    };
    console.log("Message to be sent: ", message);

    // Send to host application
    if (window.chrome && window.chrome.webview) {
        window.chrome.webview.postMessage(JSON.stringify(message));
        console.log("Message sent to host");
    } else {
        console.error("WebView API not available!");
    }
}

// Handle messages from native code
function handleNativeMessage(message) {
    if (!message || !message.type) {
        log("ERROR: Received invalid message from native code");
        return;
    }

    // Only process RAG builder related messages
    if (message.type !== "ragSelection") {
        return;
    }

    switch (message.action) {
        case "progress":
            updateProgress(message.percent, message.status);
            log(message.status);

            // When progress reaches 100%, mark as completed
            if (message.percent >= 100) {
                markProgressAsCompleted();
            }
            break;

        case "selectionResponse":
            if (message.success) {
                log("SUCCESS: " + message.message);
            } else {
                log("ERROR: " + message.message);
            }
            break;
 
        default:
            log("Received unknown action: " + message.action);
    }
}

// Register listener for messages from the native code
if (window.chrome && window.chrome.webview) {
    window.chrome.webview.addEventListener('message', function (event) {
        const message = event.data;

        if (typeof message === 'string') {
            try {
                const data = JSON.parse(message);
                handleNativeMessage(data);
            } catch (e) {
                log("ERROR: Failed to parse message from native code: " + e.message);
            }
        } else {
            handleNativeMessage(message);
        }
    });
    debugLog("WebView message listener registered");
} else {
    debugLog("ERROR: WebView API not available!");
}