/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    mainframe.js

Abstract:

    This module serves as the core coordinator for the AIMX application.
    Handles page navigation, content loading, and message routing between
    the WebView host and specialized modules including chat, settings,
    RAG builder, and AD dashboard functionality.

Communication Protocol:
    
    Frontend to Backend:
        1. pageNavigation: Notify the host about UI navigation
           Format: {type: "pageNavigation", page: "string"}
        
        2. getEndpointInfo: Request LLM endpoint information
           Format: {type: "getEndpointInfo", content: ""}
        
        3. getSettings: Request application settings
           Format: {type: "getSettings", content: ""}
        
        4. ragToggleChanged: Update RAG usage status
           Format: {type: "ragToggleChanged", enabled: boolean}
    
    Backend to Frontend:
        1. endpointInfo: Provide LLM endpoint details
           Format: {type: "endpointInfo", endpoint: "string"}
        
        2. ragStatus: Report RAG initialization status
           Format: {type: "ragStatus", initialized: boolean}

Author:

    <PERSON><PERSON><PERSON> (rizhang) 03/29/2025

--*/

// Ensure our namespace exists
window.aimx = window.aimx || {};

// Core application controller 
window.aimx.core = (function () {
    // Private variables
    let initialized = false;
    let knowledgeBaseList = [];

    // Initialize the application
    function initialize() {
        if (initialized) return;

        console.log("Initializing AIMX core application");


        // Set up navigation
        setupNavigation();

        // Set up WebView message handler
        setupMessageHandler();

        // Load the initial page (chat)
        loadChatContent();

        // Request essential information from the host
        requestHostInfo();

        initialized = true;
        console.log("Core initialization complete");
    }

    // Set up page navigation
    function setupNavigation() {
        const navItems = document.querySelectorAll('.nav-items .nav-item');

        navItems.forEach(item => {
            item.addEventListener('click', function () {
                // Get the target page id
                const targetPageId = this.getAttribute('data-page');

                // Remove active class from all nav items
                navItems.forEach(navItem => {
                    navItem.classList.remove('active');
                });

                // Add active class to clicked nav item
                this.classList.add('active');

                // Hide all pages
                const pages = document.querySelectorAll('.page');
                pages.forEach(page => {
                    page.classList.remove('active');
                });

                // Show target page
                const targetPage = document.getElementById(targetPageId);
                if (targetPage) {
                    targetPage.classList.add('active');
                }

                // Load content for specific pages
                if (targetPageId === 'chat-page') {
                    loadChatContent();
                }
                else if (targetPageId === 'rag-builder-page') {
                    loadRagBuilderContent();
                }
                else if (targetPageId === 'ad-dashboard-page') {
                    loadAdDashboardContent();
                } else if (targetPageId === 'rag-management-page') {
                    loadRagManagementContent();
                } else if (targetPageId === 'entraid-migration-page') {
                    loadEntraidMigrationContent();
                }

                // Notify host application about page change
                try {
                    if (window.chrome && window.chrome.webview) {
                        window.chrome.webview.postMessage({
                            type: 'pageNavigation',
                            page: targetPageId
                        });
                    }
                } catch (e) {
                    console.error("Could not notify host of page navigation:", e);
                }
            });
        });

        // Setup settings button click handler
        const settingsButton = document.getElementById('settingsButton');
        if (settingsButton) {
            settingsButton.addEventListener('click', function () {
                loadSettingsContent();
            });
        }

        console.log("Navigation setup complete");
    }

    // Set up WebView message handler
    function setupMessageHandler() {
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.addEventListener('message', receiveWebViewMessage);
            console.log("WebView message listener registered");
        } else {
            console.log("WebView API not available!");
        }
    }

    // Request essential information from the host
    function requestHostInfo() {
        if (window.chrome && window.chrome.webview) {
            // Request endpoint information
            window.chrome.webview.postMessage({
                type: 'getEndpointInfo',
                content: ''
            });

            // Request settings
            window.chrome.webview.postMessage({
                type: 'getSettings',
                content: ''
            });
        }
    }

    // Load chat content dynamically
    function loadChatContent() {
        const chatContainer = document.getElementById('chat-content-container');
        if (!chatContainer) {
            console.error('Chat container not found');
            return;
        }

        // Check if already loaded
        if (chatContainer.querySelector('.container')) {
            console.log('Chat content already loaded');
            chatContainer.style.display = 'block';
            return;
        }

        console.log('Loading chat content...');

        // Show loading indicator
        chatContainer.innerHTML = '<div class="loading-indicator">Loading Chat Interface...</div>';

        // Use fetch to load the content - update path to HTML
        fetch('../html/chat.html')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Failed to load chat interface: ${response.status} ${response.statusText}`);
                }
                return response.text();
            })
            .then(html => {
                console.log('Chat content loaded successfully');

                // Set the HTML content
                chatContainer.innerHTML = html;

                // Force display
                chatContainer.style.display = 'block';

                // Initialize chat.js functionality
                if (window.aimx.chat && typeof window.aimx.chat.initialize === 'function') {
                    window.aimx.chat.initialize();
                } else {
                    console.log('Chat module not yet available, will be initialized by its own DOMContentLoaded handler');
                }

                // Setup RAG toggle
                setupRagToggle();
            })
            .catch(error => {
                console.error('Error loading chat content:', error);
                chatContainer.innerHTML = `<div class="error-message">
                    Failed to load Chat Interface: ${error.message}<br><br>
                    <button onclick="window.aimx.core.loadChatContent()">Retry</button>
                </div>`;
            });
    }

    // Load settings content dynamically
    function loadSettingsContent() {
        const settingsContainer = document.getElementById('settings-container');
        const settingsModal = document.getElementById('settingsModal');

        // Create or ensure the settings container exists
        if (!settingsContainer) {
            const container = document.createElement('div');
            container.id = 'settings-container';
            container.className = 'modal';
            document.body.appendChild(container);

            console.log('Created settings container');
        }

        // If modal already exists, just show it
        if (settingsModal) {
            console.log('Settings modal already exists, showing it');
            settingsModal.classList.add('active');
            return;
        }

        const containerToUse = settingsContainer || document.getElementById('settings-container');

        console.log('Loading settings content...');

        // Show loading indicator
        containerToUse.innerHTML = '<div class="loading-indicator">Loading Settings...</div>';
        containerToUse.classList.add('active');

        // Use fetch to load the content - update path to HTML
        fetch('../html/settings.html')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Failed to load settings: ${response.status} ${response.statusText}`);
                }
                return response.text();
            })
            .then(html => {
                console.log('Settings content loaded successfully');

                // Set the HTML content
                containerToUse.innerHTML = html;

                // Add the modal class and make it active
                containerToUse.classList.add('modal');
                containerToUse.classList.add('active');
                containerToUse.id = 'settingsModal';

                // Now explicitly initialize the settings module
                // This is AFTER the DOM elements have been added
                if (window.aimx.settings && typeof window.aimx.settings.initialize === 'function') {
                    setTimeout(() => {
                        // Small delay to ensure DOM is ready
                        console.log("Explicitly initializing settings module");
                        window.aimx.settings.getElements(); // Force element refresh
                        window.aimx.settings.initialize();

                        // Check if settings need to be loaded
                        if (window.chrome && window.chrome.webview) {
                            window.chrome.webview.postMessage({
                                type: 'getSettings',
                                content: ''
                            });
                        }
                    }, 50);
                } else {
                    console.error('Settings module not available!');
                }
            })
            .catch(error => {
                console.error('Error loading settings content:', error);
                containerToUse.innerHTML = `<div class="error-message">
                    Failed to load Settings: ${error.message}<br><br>
                    <button onclick="window.aimx.core.loadSettingsContent()">Retry</button>
                </div>`;
                containerToUse.classList.add('active');
            });
    }

    // Load RAG builder content
    function loadRagBuilderContent() {
        const ragContainer = document.getElementById('rag-builder-container');
        if (!ragContainer) {
            console.error('RAG builder container not found');
            return;
        }

        // Check if already loaded
        if (ragContainer.querySelector('h1')) {
            console.log('RAG builder already loaded');
            ragContainer.style.display = 'block';
            return;
        }

        console.log('Loading RAG builder content...');

        // Show loading indicator
        ragContainer.innerHTML = '<div class="loading-indicator">Loading RAG Builder...</div>';

        // Use fetch to load the content - update path to HTML
        fetch('../html/ragbuilder.html')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Failed to load RAG builder: ${response.status} ${response.statusText}`);
                }
                return response.text();
            })
            .then(html => {
                console.log('RAG builder content loaded successfully');

                // Set the HTML content
                ragContainer.innerHTML = html;

                // Force display
                ragContainer.style.display = 'block';

                // Execute any scripts in the loaded content
                const scripts = ragContainer.querySelectorAll('script');
                scripts.forEach(script => {
                    try {
                        const newScript = document.createElement('script');

                        // Copy all attributes from the original script
                        Array.from(script.attributes).forEach(attr => {
                            newScript.setAttribute(attr.name, attr.value);
                        });

                        newScript.textContent = script.textContent;
                        script.parentNode.replaceChild(newScript, script);

                        console.log('Executed script in RAG builder content');
                    } catch (scriptError) {
                        console.error('Error executing script in RAG builder:', scriptError);
                    }
                });
            })
            .catch(error => {
                console.error('Error loading RAG builder:', error);
                ragContainer.innerHTML = `<div class="error-message">
                    Failed to load RAG Builder: ${error.message}<br><br>
                    <button onclick="window.aimx.core.loadRagBuilderContent()">Retry</button>
                </div>`;
            });
    }

    // Load AD Dashboard content
    function loadAdDashboardContent() {
        const adDashboardContainer = document.getElementById('ad-dashboard-container');
        if (!adDashboardContainer) {
            console.error('AD Dashboard container not found');
            return;
        }

        // Check if already loaded
        if (adDashboardContainer.querySelector('.dashboard-header')) {
            console.log('AD Dashboard already loaded');
            adDashboardContainer.style.display = 'block';
            return;
        }

        console.log('Loading AD Dashboard content...');

        // Show loading indicator
        adDashboardContainer.innerHTML = '<div class="loading-indicator">Loading AD Dashboard...</div>';

        // First, check if the script is already in the document - update path to JS
        const existingScript = document.querySelector('script[src="addashboard.js"]');
        if (existingScript) {
            console.log('addashboard.js is already loaded, removing it to prevent conflicts');
            existingScript.parentNode.removeChild(existingScript);
        }

        // Use fetch to load the content - update path to HTML
        fetch('../html/addashboard.html')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Failed to load AD Dashboard: ${response.status} ${response.statusText}`);
                }
                return response.text();
            })
            .then(html => {
                console.log('AD Dashboard content loaded successfully');

                // Set the HTML content
                adDashboardContainer.innerHTML = html;

                // Force display
                adDashboardContainer.style.display = 'block';

                // After HTML is loaded, explicitly load the addashboard.js script - update path to JS
                return new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = '../js/addashboard.js';
                    script.onload = () => {
                        console.log('addashboard.js loaded successfully');
                        resolve();
                    };
                    script.onerror = (error) => {
                        console.error('Failed to load addashboard.js:', error);
                        reject(error);
                    };
                    document.body.appendChild(script);
                });
            })
            .then(() => {
                // Script is loaded, now initialize the dashboard
                if (window.ADDashboard && typeof window.ADDashboard.initialize === 'function') {
                    console.log('ADDashboard object is available, initializing...');
                    window.ADDashboard.initialize();
                } else {
                    console.error('ADDashboard object or initialize method not available');
                }
            })
            .catch(error => {
                console.error('Error loading AD Dashboard:', error);
                adDashboardContainer.innerHTML = `<div class="error-message">
                    Failed to load AD Dashboard: ${error.message}<br><br>
                    <button onclick="window.aimx.core.loadAdDashboardContent()">Retry</button>
                </div>`;
            });
    }

    function loadRagManagementContent() {
        console.log('Loading RAG Management content...');
        const ragManagementContainer = document.getElementById('rag-management-container');
        if (!ragManagementContainer) {
            console.error('RAG Management container not found');
            return;
        }
        fetch('../html/ragmanagement.html')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Failed to load RAG Management: ${response.status} ${response.statusText}`);
                }
                return response.text();
            })
            .then(html => {
                console.log('RAG Management content loaded successfully');
                ragManagementContainer.innerHTML = html;
                ragManagementContainer.style.display = 'block';

                var list = window.aimx.core.getKnowledgeBaseList();
                console.log('Knowledge Base List:', list);
                const listContainer = document.getElementById('knowledgeBaseList');
                if (!listContainer) {
                    console.error("Element with ID 'knowledgeBaseList' not found in the document.");
                }

                list.forEach((item, index) => {
                    const isChecked = item === window.aimx.core.defaultKnowledgeBase ? 'checked' : ''; // Enable default rag database by default
                    const listItem = `
            <li class="list-group-item d-flex justify-content-between align-items-center">
            ${item}
            <div class="custom-control custom-switch">
            <input type="checkbox" class="custom-control-input" name="${item}" id="toggle${index}" ${isChecked} onclick="handleToggle(${index})">
            <label class="custom-control-label" for="toggle${index}">Set as default</label>
            </div>
            </li>
            `;
                    listContainer.innerHTML += listItem;
                    const toggleInput = document.getElementById(`toggle${index}`);
                    toggleInput.addEventListener('click', () => handleToggle(index));

                });
            })
            .catch(error => {
                console.error('Error loading RAG Management:', error);
                ragManagementContainer.innerHTML = `<div class="error-message">
                    Failed to load RAG Management: ${error.message}<br><br>
                    <button onclick="window.aimx.core.loadRagManagementContent()">Retry</button>
                </div>`;
            });
    }

    // Load Entra ID Migration content
    function loadEntraidMigrationContent() {
        const migrationContainer = document.getElementById('entraid-migration-container');
        if (!migrationContainer) {
            console.error('Entra ID Migration container not found');
            return;
        }

        // Check if already loaded
        if (migrationContainer.querySelector('.migration-container')) {
            console.log('Entra ID Migration already loaded');
            migrationContainer.style.display = 'block';
            return;
        }

        console.log('Loading Entra ID Migration content...');

        // Show loading indicator
        migrationContainer.innerHTML = '<div class="loading-indicator">Loading Entra ID Migration Assessment...</div>';

        // Use fetch to load the content
        fetch('../html/entraid-migration.html')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Failed to load Entra ID Migration: ${response.status} ${response.statusText}`);
                }
                return response.text();
            })
            .then(html => {
                console.log('Entra ID Migration content loaded successfully');

                // Set the HTML content
                migrationContainer.innerHTML = html;

                // Force display
                migrationContainer.style.display = 'block';

                // Initialize the migration assessment module
                if (window.EntraidMigration && typeof window.EntraidMigration.initialize === 'function') {
                    console.log('EntraidMigration object is available, initializing...');
                    window.EntraidMigration.initialize();
                } else {
                    console.log('EntraidMigration will be initialized by its script when loaded');
                    
                    // Load the JavaScript file
                    const script = document.createElement('script');
                    script.src = '../js/entraid-migration.js';
                    script.onload = () => {
                        console.log('entraid-migration.js loaded successfully');
                        if (window.EntraidMigration && typeof window.EntraidMigration.initialize === 'function') {
                            window.EntraidMigration.initialize();
                        }
                    };
                    script.onerror = (error) => {
                        console.error('Failed to load entraid-migration.js:', error);
                    };
                    document.body.appendChild(script);
                }
            })
            .catch(error => {
                console.error('Error loading Entra ID Migration:', error);
                migrationContainer.innerHTML = `<div class="error-message">
                    Failed to load Entra ID Migration: ${error.message}<br><br>
                    <button onclick="window.aimx.core.loadEntraidMigrationContent()">Retry</button>
                </div>`;
            });
    }

    // Set up RAG toggle functionality
    function setupRagToggle() {
        const ragToggle = document.getElementById('ragToggle');
        if (ragToggle) {
            ragToggle.addEventListener('change', function () {
                console.log('RAG toggle state changed:', this.checked);

                // Notify the host application about the change
                if (window.chrome && window.chrome.webview) {
                    window.chrome.webview.postMessage(JSON.stringify({
                        type: 'ragToggleChanged',
                        enabled: this.checked,
                        useRag: this.checked
                    }));
                }
            });
        }
    }

    // Handle messages from WebView
    function receiveWebViewMessage(event) {
        try {
            let data = event.data;
            if (!data) return;

            // Log the raw data for debugging (truncated for large messages)
            if (typeof data === 'string') {
                console.log("Raw message data:", data.length > 50 ?
                    data.substring(0, 50) + "..." : data);
            } else {
                console.log("Raw message data:", data);
            }

            // Parse string messages as JSON
            let parsedData = data;
            if (typeof data === 'string') {
                try {
                    parsedData = JSON.parse(data);
                } catch (parseError) {
                    console.error("Failed to parse message as JSON:", parseError);

                    // Try cleaning the string if it contains escaped quotes or backslashes
                    if (data.includes('\\\"') || data.includes('\\\\')) {
                        try {
                            const cleanedData = data
                                .replace(/\\"/g, '"')  // Replace escaped quotes
                                .replace(/\\\\/g, '\\'); // Replace escaped backslashes

                            parsedData = JSON.parse(cleanedData);
                            console.log("Parsed JSON after cleaning string");
                        } catch (cleanError) {
                            console.error("Failed to parse cleaned JSON:", cleanError);
                            parsedData = {
                                type: "assistantMessage",
                                content: "Error parsing message: " + data.substring(0, 100) + (data.length > 100 ? "..." : "")
                            };
                        }
                    } else {
                        // Use as regular message
                        parsedData = {
                            type: "assistantMessage",
                            content: data
                        };
                    }
                }
            }

            // Clean up JSON content if needed
            parsedData = cleanMessageContent(parsedData);

            // Route message to appropriate handler based on type
            routeMessage(parsedData);
        } catch (error) {
            console.error("Error processing WebView message:", error);
        }
    }

    // Clean up potentially nested JSON content in streaming messages
    function cleanMessageContent(data) {
        // Check for nested JSON content in streamUpdate messages
        if (data &&
            data.type === "streamUpdate" &&
            data.content &&
            typeof data.content === 'string') {

            // Look for JSON objects or fragments
            if ((data.content.startsWith('{') && data.content.endsWith('}')) ||
                data.content.includes('{"index":') ||
                data.content.includes('"token') ||
                data.content.includes('"content":')) {

                console.log("Detected potential JSON artifacts in content, cleaning up");

                try {
                    // Try to parse complete JSON objects
                    if (data.content.startsWith('{') && data.content.endsWith('}')) {
                        const contentObj = JSON.parse(data.content);
                        if (contentObj.content) {
                            console.log("Extracted nested content from complete JSON");
                            data.content = contentObj.content;
                        }
                    }
                    // Handle cases where JSON is truncated or malformed
                    else if (data.content.includes('{"index":')) {
                        // Find the start of a JSON object
                        const jsonStart = data.content.indexOf('{"index":');
                        // Extract content up to that point
                        if (jsonStart > 0) {
                            console.log("Truncating content at JSON artifact");
                            data.content = data.content.substring(0, jsonStart);
                        }
                        // If it starts with JSON and we have accumulated content, replace completely
                        else if (jsonStart === 0 &&
                            window.aimx.chat.messageTracker.hasMessage(data.sessionId)) {
                            console.log("Skipping JSON artifact chunk");
                            // Get previous content and keep using that
                            data.content = window.aimx.chat.messageTracker.getContent(data.sessionId);
                        }
                    }
                } catch (e) {
                    console.log("JSON cleanup attempt failed:", e);
                }
            }
        }

        return data;
    }

    // Route message to appropriate handler based on message type
    function routeMessage(message) {
        if (!message || !message.type) {
            console.error("Invalid message format (missing type):", message);
            return;
        }

        console.log(`Routing message of type: ${message.type}`);

        switch (message.type) {
            // Chat-related messages
            case "userMessage":
            case "assistantMessage":
            case "error":
            case "streamStart":
            case "streamUpdate":
            case "streamEnd":
                // Forward to chat module
                handleChatMessage(message);
                break;

            // Settings-related message
            case "settings":
                // Forward to settings module
                if (window.aimx.settings && typeof window.aimx.settings.loadSettings === 'function') {
                    window.aimx.settings.loadSettings(message.settings);
                }
                break;

            // Endpoint information message
            case "endpointInfo":
                // Update endpoint display
                const disclaimerElement = document.getElementById('endpointDisclaimer');
                if (disclaimerElement && message.endpoint) {
                    disclaimerElement.textContent = `aimx interacts with a locally hosted LLM at ${message.endpoint}`;
                }
                break;

            // RAG status update
            case "ragStatus":
                handleRagStatus(message.initialized);
                break;

            // AD Dashboard messages
            case "adDashboard":
                // These should be handled by the AD Dashboard module directly
                console.log("AD Dashboard message received:", message.action);
                break;

            case "toastNotification":
                handleToastNotification(message);
                break;

            default:
                console.log("Unhandled message type:", message.type);
                break;
        }
    }

    //Displays a toast notification with the message content
    function handleToastNotification(message) {
        console.log("Received toast notification " + message.content);
        Toastify({
            text: message.content,
            duration: 5000,
            //newWindow: true,
            //close: true,
            gravity: "top", 
            position: "left",
            stopOnFocus: true, 
            style: {
                background: "linear-gradient(to right, #0078D7, #00BCF2)",
            },
            onClick: function () { } // Callback after click
        }).showToast();
    }

    // Handle chat-related messages by forwarding to chat module
    function handleChatMessage(message) {
        // Ensure chat module is available
        if (!window.aimx.chat) {
            console.error("Chat module not available to handle message:", message.type);
            return;
        }

        switch (message.type) {
            case "userMessage":
                window.aimx.chat.addMessage(message.content, 'user');
                break;

            case "assistantMessage":
                window.aimx.chat.removeTypingIndicator();
                window.aimx.chat.addMessage(message.content, 'bot');
                break;

            case "error":
                window.aimx.chat.removeTypingIndicator();
                window.aimx.chat.addMessage(message.content, 'bot');
                break;

            case "streamStart":
                window.aimx.chat.createStreamingMessage(message.sessionId);
                break;

            case "streamUpdate":
                const chunk = message.content || "";
                const fullContent = window.aimx.chat.messageTracker.updateContent(message.sessionId, chunk);
                window.aimx.chat.updateStreamingMessage(message.sessionId, chunk, fullContent);
                break;

            case "streamEnd":
                window.aimx.chat.finalizeStreamingMessage(message.sessionId);
                break;
        }
    }

    // Handle RAG status update
    function handleRagStatus(initialized) {
        if (initialized) {
            console.log('RAG successfully initialized');
        } else {
            console.log('RAG initialization failed');
            // Disable RAG toggle
            const ragToggle = document.getElementById('ragToggle');
            if (ragToggle) {
                ragToggle.checked = false;
            }

            // Show error message in chat
            const errorMessage = 'RAG database could not be initialized. Please check if it exists or build it in the RAG Builder.';
            if (window.aimx.chat) {
                window.aimx.chat.addMessage(errorMessage, 'bot');
            } else {
                // Fallback if chat module isn't available
                alert(errorMessage);
            }
        }
    }

    // Public API
    return {
        initialize,
        loadChatContent,
        loadSettingsContent,
        loadRagBuilderContent,
        loadAdDashboardContent,
        loadRagManagementContent,
        loadEntraidMigrationContent,
        getKnowledgeBaseList: () => knowledgeBaseList, // Expose the variable via a getter
        setKnowledgeBaseList: (value) => { knowledgeBaseList = value; } // Optional setter
    };
})();

// Initialize core application when DOM is ready
document.addEventListener('DOMContentLoaded', function () {
    window.aimx.core.initialize();
});