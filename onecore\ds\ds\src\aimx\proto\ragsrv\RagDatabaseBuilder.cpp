/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    RagDatabaseBuilder.cpp

Abstract:

    This module implements the RAG database builder class.


Author:

    <PERSON><PERSON><PERSON> (rizhang) 04/14/2025

    Note: this is currently just a stub and does not implement the key functionality.
    <PERSON><PERSON><PERSON> will be implementing the actual functionality in the next phase.

--*/

#include "RagDatabaseBuilder.h"
#include "RagJobManager.h"
#include "../common/debug.h"
#include "../common/nlohmann/json.hpp"
#include "hnswlib/hnswlib.h" // Ensure this path points to the correct header file for hnswlib
#include "dtt.h"

#include <filesystem>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <chrono>
#include <thread>
#include <windows.h>
#include <regex>

constexpr size_t LARGE_FILE_THRESHOLD = 1024 * 1024 * 1024; // 1GB
constexpr size_t DEFAULT_CHUNK_SIZE = 128; // Default chunk size for text processing

namespace fs = std::filesystem;
using json = nlohmann::json;
using namespace Microsoft::WRL;

// Function pointer types for the llm-infer.dll functions
typedef bool (*EMBED_INITIALIZE_FUNC)(model_params&);
typedef bool (*EMBED_ENCODE_BATCH_FUNC)(const model_params&, std::vector<chunk>&);
typedef bool (*EMBED_ENCODE_SINGLE_FUNC)(const model_params&, const std::string&, std::vector<float>&);
typedef void (*EMBED_TERMINATE_FUNC)();

// Function pointers for dynamic loading
static HMODULE g_hLlmInferDll = NULL;
static EMBED_INITIALIZE_FUNC g_pfnEmbedInitialize = NULL;
static EMBED_ENCODE_BATCH_FUNC g_pfnEmbedEncodeBatch = NULL;
static EMBED_ENCODE_SINGLE_FUNC g_pfnEmbedEncodeSingle = NULL;
static EMBED_TERMINATE_FUNC g_pfnEmbedTerminate = NULL;

// Global state for RAG database
static bool g_bRagDbInitialized = false;
static std::vector<rag_entry> g_RagEntries;
static hnswlib::HierarchicalNSW<float>* g_pRagDB = nullptr;

/*++

Routine Description:

    Constructor for the RagDatabaseBuilder class.

Arguments:

    None.

Return Value:

    None.

--*/
RagDatabaseBuilder::RagDatabaseBuilder()
    : m_buildInProgress(false)
    , m_gpuAvailable(false)
{
    LOGINFO("RagDatabaseBuilder: Initializing");

    memset(&m_metadata, 0, sizeof(RagMetadata));

    // Initialize GPU detection
    InitializeGpuDetection();
}

//
// Routine Description:
//
//     Initializes the RagDatabaseBuilder class.
//
// Arguments:
//
//     chunkSize - Size of text chunks.
//     similarityThreshold - Threshold for semantic chunking.
//     modelName - Name of the embedding model.
//     useGpu - Whether to use GPU for embeddings.
//
// Return Value:
//
//     bool - True if the class was initialized, false otherwise.
//

bool
RagDatabaseBuilder::Initialize(
    _In_ int chunkSize,
    _In_z_ const std::string& modelName,
    _In_ bool useGpu,
    _In_z_ const std::string& outputDir
    )
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_buildInProgress)
    {
        LOGERROR("RagDatabaseBuilder: A build is already in progress");
        return false;
    }

    m_chunkSize = chunkSize;
    m_outputDirectory = outputDir;


    m_params.chunk_size = chunkSize;
    m_params.force_cpu_mode = useGpu ? 1 : 0;
    m_params.model_name = modelName;
    m_params.n_ctx = 512;


    // Set output file paths
    m_indexPath = m_outputDirectory + "\\rag_index.bin";
    m_chunksPath = m_outputDirectory + "\\chunks.json";
    m_metadataPath = m_outputDirectory + "\\metadata.json";

     // Initialize embedding model
     if (!embed_initialize(m_params))
     {
         LOGERROR("Failed to initialize embedding model: " + m_params.model_name);
         return false;
     }

    // Clear any existing data
    m_documents.clear();
    m_documentStats.clear();

    // Initialize metadata
    m_metadata.modelName = m_params.model_name;
    m_metadata.dimension = m_params.n_dim;
    m_metadata.chunkSize = m_chunkSize;

    m_initialized = true;
    return true;
}
/*++

Routine Description:

    Destructor for the RagDatabaseBuilder class.

Arguments:

    None.

Return Value:

    None.

--*/
RagDatabaseBuilder::~RagDatabaseBuilder()
{
    LOGINFO("RagDatabaseBuilder: Destroying");

    // Wait for any ongoing build to finish
    if (m_buildThread.joinable())
    {
        m_buildThread.join();
    }
}

/*++

Routine Description:

    Starts an asynchronous RAG database build.

Arguments:

    jobId - ID of the job.
    folderPath - Path to the folder containing documents.
    outputDir - Path to the output directory.
    chunkSize - Size of text chunks.
    similarityThreshold - Threshold for semantic chunking.
    modelName - Name of the embedding model.
    useGpu - Whether to use GPU for embeddings.
    jobManager - Pointer to the job manager.

Return Value:

    bool - True if the build was started, false otherwise.

--*/
bool
RagDatabaseBuilder::StartBuildAsync(
    _In_z_ const std::string& jobId,
    _In_z_ const std::string& folderPath,
    _In_z_ const std::string& outputDir,
    _In_ int chunkSize,
    _In_ float similarityThreshold,
    _In_z_ const std::string& modelName,
    _In_ bool useGpu,
    _In_ std::unique_ptr<RagJobManager>& jobManager
)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_buildInProgress)
    {
        LOGERROR("RagDatabaseBuilder: A build is already in progress");
        return false;
    }

    // Validate parameters
    if (!std::filesystem::exists(folderPath))
    {
        LOGERROR("RagDatabaseBuilder: Folder path does not exist: ", folderPath.c_str());
        return false;
    }

    // Create output directory if it doesn't exist
    if (!std::filesystem::exists(outputDir))
    {
        try
        {
            std::filesystem::create_directories(outputDir);
        }
        catch (const std::exception& e)
        {
            LOGERROR("RagDatabaseBuilder: Failed to create output directory: ", e.what());
            return false;
        }
    }

    // Start the build thread
    m_buildInProgress = true;

    // Get raw pointer from unique_ptr for the thread function
    RagJobManager* jobManagerPtr = jobManager.get();

    m_buildThread = std::thread(&RagDatabaseBuilder::BuildDatabaseThread, this,
                               jobId, folderPath, outputDir, chunkSize, similarityThreshold,
                               modelName, useGpu, jobManagerPtr);

    LOGINFO("RagDatabaseBuilder: Started async build for job ", jobId);
    return true;
}

/*++

Routine Description:

    Tests an existing index.

Arguments:

    indexPath - Path to the index file.
    query - Query to test.
    k - Number of results to return.

Return Value:

    json - Results of the query.

--*/
json
RagDatabaseBuilder::TestIndex(
    _In_z_ const std::string& indexPath,
    _In_z_ const std::string& query,
    _In_ int k
)
{
    LOGINFO("RagDatabaseBuilder: Testing index %s with query '%s'", indexPath.c_str(), query.c_str());

    // to be implmented.

    // Return results in structured format
    json responseJson;
    return responseJson;
}

/*++

Routine Description:

    Checks if GPU is available.

Arguments:

    None.

Return Value:

    bool - True if GPU is available, false otherwise.

--*/
bool
RagDatabaseBuilder::IsGpuAvailable() const
{
    return m_gpuAvailable;
}

/*++

Routine Description:

    Gets the name of the GPU.

Arguments:

    None.

Return Value:

    std::string - Name of the GPU.

--*/
std::string
RagDatabaseBuilder::GetGpuName() const
{
    return m_gpuName;
}

/*++

Routine Description:

    Checks if a build is in progress.

Arguments:

    None.

Return Value:

    bool - True if a build is in progress, false otherwise.

--*/
bool
RagDatabaseBuilder::IsBuildInProgress() const
{
    return m_buildInProgress;
}

/*++

Routine Description:

    Creates a temporary directory for storing selected files.

Arguments:

    None.

Return Value:

    std::string containing the path to the temporary directory.

--*/
std::string
RagDatabaseBuilder::CreateTemporaryDirectory()
{
    // Create a unique temporary directory path
    wchar_t tempPath[MAX_PATH];
    DWORD result = GetTempPathW(MAX_PATH, tempPath);
    if (result == 0 || result > MAX_PATH) {
        LOGERROR("Failed to get temporary path");
        return "";
    }

    // Create a unique folder name with timestamp
    std::wstring uniqueName = L"aimx_rag_" + std::to_wstring(std::chrono::system_clock::now().time_since_epoch().count());
    std::wstring tempDir = std::wstring(tempPath) + uniqueName;

    // Create the directory
    if (!CreateDirectoryW(tempDir.c_str(), NULL) && GetLastError() != ERROR_ALREADY_EXISTS) {
        LOGERROR("Failed to create temporary directory");
        return "";
    }

    LOGINFO("Created temporary directory: " + WideToUtf8(tempDir));
    return WideToUtf8(tempDir);
}

/*++

Routine Description:

    Extracts text content from a document file eg. txt file, log file

Arguments:

    filePath - Path to the document file.

Return Value:

    std::string containing the extracted text based file.

--*/
std::string
RagDatabaseBuilder::ExtractTextBasedFile(
    _In_ const std::string& filePath
)
{
    LOGINFO("Extracting text from file: " + filePath);

    std::ifstream file(filePath, std::ios::binary | std::ios::ate);
    if (!file.is_open())
    {
        LOGERROR("Failed to open file: " + filePath);
        return "";
    }

    // Get file size and reset position to beginning
    std::streamsize fileSize = file.tellg();
    file.seekg(0, std::ios::beg);

    // Read entire file into buffer
    std::vector<char> buffer(fileSize);
    if (!file.read(buffer.data(), fileSize))
    {
        LOGERROR("Error reading file: " + filePath);
        return "";
    }

    // Check for UTF-8 BOM (EF BB BF)
    if (fileSize >= 3 &&
        static_cast<unsigned char>(buffer[0]) == 0xEF &&
        static_cast<unsigned char>(buffer[1]) == 0xBB &&
        static_cast<unsigned char>(buffer[2]) == 0xBF)
    {
        LOGINFO("Detected UTF-8 encoding with BOM");
        return std::string(buffer.begin() + 3, buffer.end());
    }

    // Check for UTF-16LE BOM (FF FE)
    if (fileSize >= 2 &&
        static_cast<unsigned char>(buffer[0]) == 0xFF
        && static_cast<unsigned char>(buffer[1]) == 0xFE)
    {
        LOGINFO("Detected UTF-16 Little-Endian encoding with BOM");

        // Create a wide string from the buffer (skipping BOM)
        const wchar_t* wideData = reinterpret_cast<const wchar_t*>(buffer.data() + 2);
        size_t wideLength = (fileSize - 2) / 2;
        std::wstring wideStr(wideData, wideLength);

        // Convert to UTF-8 using StringUtils helper
        return WideToUtf8(wideStr);
    }

    // Check for UTF-16BE BOM (FE FF)
    if (fileSize >= 2 &&
        static_cast<unsigned char>(buffer[0]) == 0xFE &&
        static_cast<unsigned char>(buffer[1]) == 0xFF)
    {
        LOGINFO("Detected UTF-16 Big-Endian encoding with BOM");

        // Need to swap bytes for BE to create proper wide string
        std::wstring wideStr;
        wideStr.reserve((fileSize - 2) / 2);

        for (long long i = 2; i < fileSize; i += 2)
        {
            if (i + 1 < fileSize)
            {
                wchar_t wc = (static_cast<unsigned char>(buffer[i]) << 8) | static_cast<unsigned char>(buffer[i + 1]);
                wideStr.push_back(wc);
            }
        }

        // Convert to UTF-8 using StringUtils helper
        return WideToUtf8(wideStr);
    }

    // No BOM detected, assume UTF-8
    LOGINFO("No BOM detected, assuming UTF-8 encoding");
    return std::string(buffer.begin(), buffer.end());
}

/*++

Routine Description:

    Extract content of pdf file into string output

Arguments:

    pdfInputStream: pdf file input stream
    fileContent: out param to read pdf file

Return Value:

    HRESULT: succees, failure

--*/
HRESULT
RagDatabaseBuilder::ExtractPdfText(
    _In_ const IStream_t& pdfInputStream,
    _Out_ std::string &fileContent
    )
{
    //
    // Get an IFilter instance for the PDF filter.
    // Use this filter to extract any text from the PDF
    //
    HRESULT hr = S_OK;
    ComPtr<IFilter> pPdfFilter;
    ComPtr<ILoadFilter> pLoadFilter;

    const GUID CLSID_FilterRegistration = { 0x9E175B8D, 0xF52A, 0x11D8, { 0xB9, 0xA5, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30 } };

    if (SUCCEEDED(hr))
    {
        hr = CoCreateInstance(CLSID_FilterRegistration, NULL, CLSCTX_INPROC_SERVER, IID_PPV_ARGS(&pLoadFilter));
    }

    if (SUCCEEDED(hr))
    {
        FILTERED_DATA_SOURCES filteredSources = {};
        filteredSources.pwcsExtension = L".pdf";

        CLSID filterClsid = {};

        hr = pLoadFilter->LoadIFilter(
            NULL,
            &filteredSources,
            NULL,
            FALSE,
            &filterClsid,
            NULL,
            NULL,
            &pPdfFilter
            );
    }

    //
    // Load the PDF stream into the filter
    //
    if (SUCCEEDED(hr))
    {
        ComPtr<IInitializeWithStream> pIInitWithStream;
        pPdfFilter.As(&pIInitWithStream);

        if (pIInitWithStream)
        {
            // Load from stream
            hr = pIInitWithStream->Initialize(pdfInputStream.Get(), STGM_READ);
            hr = (hr == S_FALSE) ? E_FAIL : hr;
        }
    }

    if (SUCCEEDED(hr))
    {
        ULONG initFlags = 0;
        ULONG propFlags = 0;
        hr = pPdfFilter->Init(
            initFlags,
            0,
            NULL,
            &propFlags);
    }

    if (SUCCEEDED(hr))
    {
        STAT_CHUNK statChunk = {};
        while (pPdfFilter->GetChunk(&statChunk) == S_OK)
        {
            if (statChunk.flags & CHUNK_TEXT)
            {
                // This chunk has text in it, read it out.
                const ULONG bufferSize = 0x1000;
                ULONG readSize = bufferSize;
                WCHAR textBuffer[bufferSize];
                SCODE ret = S_OK;
                do
                {
                    ret = pPdfFilter->GetText(&readSize, textBuffer);

                    if (ret == S_OK || ret == FILTER_S_LAST_TEXT)
                    {
                        // Output the text to the output stream
                        if (readSize > 0)
                        {
                            fileContent += WideToUtf8(textBuffer);
                        }
                    }
                    readSize = bufferSize;
                } while (SUCCEEDED(hr) && (ret != FILTER_E_NO_MORE_TEXT));
            }
        }
    }

    return hr;
}

/*++

Routine Description:

    Extracts text content from PDF file

Arguments:

    filePath - Path to the document file.

Return Value:

    std::string containing the extracted PDF file.

--*/
std::string
RagDatabaseBuilder::ExtractPDFFile(
    _In_ const std::string& filePath
)
{
    HRESULT hr = CoInitializeEx(NULL, COINIT_MULTITHREADED);
    if (FAILED(hr))
    {
        LOGERROR("Failed to initialize COM.");
        return "";
    }

    IStream_t pdfInput;
    hr = SHCreateStreamOnFileEx(Utf8ToWide(filePath).c_str(), STGM_READ, 0, FALSE, NULL, &pdfInput);
    if (FAILED(hr))
    {
        LOGERROR("Could not open input file: " + filePath);
        CoUninitialize();
        return "";
    }

    //Extract content from pdf file
    std::string fileContent;
    hr = ExtractPdfText(pdfInput, fileContent);
    if (SUCCEEDED(hr))
    {
        LOGINFO("Text extracted from PDF file: " + filePath);
    }
    else
    {
        LOGERROR("Failed to extract text from PDF file with error", std::to_string(hr));
    }

    CoUninitialize();

    return fileContent;
}

/*++

Routine Description:

    Extracts text content from Docx file

Arguments:

    filePath - Path to the document file.

Return Value:

    std::string containing the extracted Docx file.

--*/
std::string
RagDatabaseBuilder::ExtractDocxFile(
    _In_ const std::string& filePath
)
{
    LOGINFO("Extracting contents for document: " + filePath);
    return dtt(filePath);
}

/*++

Routine Description:

    Extracts text content from a document file.
    Currently supports basic text extraction, can be extended for various formats.

Arguments:

    filePath - Path to the document file.

Return Value:

    std::string containing the extracted text content.

--*/
std::string
RagDatabaseBuilder::ExtractTextFromFile(
    _In_ const std::string& filePath
)
{
    std::string fileExtension = fs::path(filePath).extension().string();
    std::transform(fileExtension.begin(), fileExtension.end(), fileExtension.begin(),
                  [](unsigned char c) { return std::tolower(c); });

    // Error out if file size is larger (> 1GB)
    size_t fileSize = fs::file_size(filePath);
    if (fileSize >= LARGE_FILE_THRESHOLD)
    {
        LOGERROR("File size exceeds the maximum limit (1GB) for in-memory loading" + std::to_string(fileSize));
        return "";
    }

    // Extract text based on file extension
    if (fileExtension == ".txt" ||
        fileExtension == ".log")
    {
        return ExtractTextBasedFile(filePath);
    }

    if (fileExtension == ".docx")
    {
        return ExtractDocxFile(filePath);
    }

    if (fileExtension == ".pdf")
    {
        return ExtractPDFFile(filePath);
    }

    // Unsupported file type
    LOGERROR("Unsupported file type for text extraction: " + fileExtension);
    return "";
}

/*++

Routine Description:

    Processes a single document file for RAG database creation.

Arguments:

    filePath - Path to the document file.
    progressCallback - Optional callback for reporting progress.

Return Value:

    bool indicating success or failure of document processing.

--*/
bool
RagDatabaseBuilder::ProcessDocument(
    _In_ const std::string& filePath
)
{
    LOGINFO("Processing document: " + filePath);

    try
    {
        // Check if the file exists
        if (!fs::exists(filePath) || !fs::is_regular_file(filePath))
        {
            LOGERROR("File does not exist: " + filePath);
            return false;
        }

        // Get file info
        std::string fileBaseName = fs::path(filePath).filename().string();
        std::string fileExtension = fs::path(filePath).extension().string();
        std::transform(fileExtension.begin(), fileExtension.end(), fileExtension.begin(),
                      [](unsigned char c) { return std::tolower(c); });

        // Track timing
        auto startTime = std::chrono::high_resolution_clock::now();

        // Extract text from file
        std::string textContent = ExtractTextFromFile(filePath);

        auto endTime = std::chrono::high_resolution_clock::now();
        std::chrono::duration<double> extractionTime = endTime - startTime;

        if (textContent.empty())
        {
            LOGERROR("Failed to extract text from: " + filePath);
            m_metadata.failedCount++;
            return false;
        }


        // Create document entry
        rag_entry document;
        document.id = (int)m_documents.size();
        document.filename = fileBaseName;
        document.textdata = textContent;


        // Add to document collection
        m_documents.push_back(document);

        // Create document stats
        DocumentStats stats;
        stats.filename = fileBaseName;
        stats.path = filePath;
        stats.fileType = fileExtension.substr(1); // Remove the dot
        stats.sizeBytes = fs::file_size(filePath);
        stats.charCount = textContent.size();
        stats.extractionTime = extractionTime.count();

        // Add to stats collection
        m_documentStats.push_back(stats);

        // Update metadata
        m_metadata.documentCount++;

        if (fileExtension == ".docx")
        {
            m_metadata.docxCount++;
        }
        else if (fileExtension == ".txt")
        {
            m_metadata.txtCount++;
        }
        else if (fileExtension == ".log")
        {
            m_metadata.logCount++;
        }
        else if (fileExtension == ".pdf")
        {
            m_metadata.pdfCount++;
        }
        else
        {
            m_metadata.unsupportedCount++;
        }

        return true;
    }
    catch (const std::exception& e)
    {
        LOGERROR("Exception processing document: " + std::string(e.what()));
        m_metadata.failedCount++;
        return false;
    }
}
/*++

Routine Description:

    Processes a directory of documents for RAG database creation.

Arguments:

    directoryPath - Path to the directory containing documents, or file paths separated by ';'.
    progressCallback - Optional callback for reporting progress.

Return Value:

    bool indicating success or failure of document processing.

--*/
bool
RagDatabaseBuilder::ExtractDocumentContent(
    _In_ const std::string& directoryPath
)
{
    LOGINFO("Processing document input: " + directoryPath);

    // Check if we are in multiple file mode
    bool isMultipleFiles = directoryPath.find(';') != std::string::npos;
    std::string pathToProcess = directoryPath;
    std::string tempDirectory = "";

    // Handle multiple file mode
    if (isMultipleFiles) {
        LOGINFO("Multiple file mode detected");

        // Create a temporary directory to store the files
        tempDirectory = CreateTemporaryDirectory();
        if (tempDirectory.empty()) {
            LOGERROR("Failed to create temporary directory for processing files");
            return false;
        }

        // Parse the concatenated paths
        std::vector<std::string> filePaths;
        std::string currentPath;
        std::istringstream pathStream(directoryPath);

        while (std::getline(pathStream, currentPath, ';')) {
            if (!currentPath.empty()) {
                filePaths.push_back(currentPath);
            }
        }

        LOGINFO("Processing " + std::to_string(filePaths.size()) + " individual files");

        // Copy each file to the temporary directory
        size_t filesCopied = 0;
        for (const auto& filePath : filePaths) {
            try {
                std::string fileName = fs::path(filePath).filename().string();
                std::string destPath = tempDirectory + "/" + fileName;

                // Copy the file
                fs::copy_file(filePath, destPath, fs::copy_options::overwrite_existing);
                LOGINFO("Copied file: " + filePath + " to " + destPath);
                filesCopied++;
            }
            catch (const std::exception& e) {
                LOGERROR("Failed to copy file: " + filePath + " - " + e.what());
                // Continue with other files
            }
        }

        if (filesCopied == 0) {
            LOGERROR("Failed to copy any files to temporary directory");

            // Clean up the temporary directory
            try {
                fs::remove_all(tempDirectory);
            } catch (...) {
                // Ignore cleanup errors
            }

            return false;
        }

        // Set the path to process to the temporary directory
        pathToProcess = tempDirectory;
    }

    // Check if the directory exists
    if (!fs::exists(pathToProcess) || !fs::is_directory(pathToProcess))
    {
        LOGERROR("Directory does not exist: " + pathToProcess);
        return false;
    }

    // Count files for progress reporting
    size_t totalFiles = 0;
    for (const auto& entry : fs::directory_iterator(pathToProcess))
    {
        if (entry.is_regular_file())
        {
            totalFiles++;
        }
    }

    if (totalFiles == 0)
    {
        LOGERROR("No files found in directory: " + pathToProcess);
        return false;
    }

    size_t processedFiles = 0;
    bool success = true;

    // Process each file in the directory
    for (const auto& entry : fs::directory_iterator(pathToProcess))
    {
        if (entry.is_regular_file())
        {
            std::string filePath = entry.path().string();

            // Process the file
            try
            {
                std::string fileExtension = entry.path().extension().string();
                std::transform(fileExtension.begin(), fileExtension.end(), fileExtension.begin(),
                              [](unsigned char c) { return std::tolower(c); });

                if (fileExtension == ".docx" ||
                    fileExtension == ".txt" ||
                    fileExtension == ".pdf" ||
                    fileExtension == ".log" )
                {
                    if (!ProcessDocument(filePath))
                    {
                        LOGERROR("Failed to process file: " + filePath);
                        success = false;
                    }
                }
                else
                {
                    LOGINFO("Skipping unsupported file type: " + filePath);
                    m_metadata.unsupportedCount++;
                }
            }
            catch (const std::exception& e)
            {
                LOGERROR("Exception processing file: " + filePath + " - " + e.what());
                m_metadata.failedCount++;
                success = false;
            }

            processedFiles++;
        }
    }

    // Save document directory in metadata
    m_metadata.documentsPath = isMultipleFiles ? "Multiple files" : pathToProcess;

    // Clean up temporary directory if we created one
    if (!tempDirectory.empty()) {
        LOGINFO("Cleaning up temporary directory: " + tempDirectory);
        try {
            fs::remove_all(tempDirectory);
        }
        catch (const std::exception& e) {
            LOGERROR("Failed to clean up temporary directory: " + std::string(e.what()));
            // Continue anyway, this is just cleanup
        }
    }

    return success;
}

std::string removeSpecialChars(const std::string& text) {
    // This regex keeps alphanumeric characters, spaces, and basic newlines.
    std::regex pattern("[^a-zA-Z0-9\\s\\n]");
    return std::regex_replace(text, pattern, "");
}

/*++

Routine Description:

    Creates chunks from a document for processing and embedding.
    Implements both basic chunking and semantic chunking approaches.

Arguments:

    document - The document to chunk.
    useSemanticChunking - Whether to use semantic chunking (default: false)
    similarityThreshold - Threshold for semantic similarity (default: 0.7)

Return Value:

    std::vector<chunk> containing the document chunks.

Note:

    Rupo Zhang 3/21/2025:
    The chunking process involves splitting the document into smaller segments
    based on either semantic similarity or basic size constraints. The semantic
    chunking uses sentence embeddings to determine the similarity, which calls
    embed_encode_single() to generate the embeddings for each sentence. that requires
    major computation, it is best to have a GPU accelerated model for that.

    Future plans:
      Split the RAG DB construciotn to the AI Server role so that the heavy lifting
      can be done in the server side along side the LLM inference.


--*/
std::vector<chunk>
RagDatabaseBuilder::ChunkDocument(
    _In_ const rag_entry& document,
    _In_ bool useSemanticChunking,
    _In_ float similarityThreshold
)
{
    std::vector<chunk> chunks;

    // Skip empty documents
    if (document.textdata.empty())
    {
        LOGINFO("Skipping empty document: " + std::string(document.filename));
        return chunks;
    }

    const std::string& texts = document.textdata;

    // Extract sentences regardless of chunking method
    std::vector<std::string> sentences;
    const std::string separators = ".!?,";
    size_t start = 0;
    size_t end = 0;

    // Split input text into sentences
    while ((end = texts.find_first_of(separators, start)) != std::string::npos)
    {
        // Extract the sentence including the terminating punctuation
        std::string sentenceText = texts.substr(start, end - start + 1);
        if (!sentenceText.empty())
        {
            sentences.push_back(sentenceText);
        }
        start = end + 1;
    }

    // Add any remaining text as the final sentence
    if (start < texts.size())
    {
        std::string remainingText = texts.substr(start);
        if (!remainingText.empty())
        {
            sentences.push_back(remainingText);
        }
    }

    if (sentences.empty())
    {
        LOGINFO("No sentences extracted from document: " + std::string(document.filename));
        return chunks;
    }

    // Choose between semantic chunking and basic chunking
    if (useSemanticChunking)
    {
        LOGINFO("Using semantic chunking for document: " + std::string(document.filename));

        try
        {
            // Calculate embeddings for each sentence
            std::vector<std::vector<float>> sentenceEmbeddings;
            sentenceEmbeddings.reserve(sentences.size());

            for (const auto& sentence : sentences)
            {
                std::vector<float> embedding;
                if (!embed_encode_single(m_params, sentence, embedding))
                {
                    LOGERROR("Failed to generate embedding for sentence");
                    continue;
                }
                sentenceEmbeddings.push_back(std::move(embedding));
            }

            // Helper function to calculate cosine similarity between two embeddings
            auto calculateCosineSimilarity = [](const std::vector<float>& emb1, const std::vector<float>& emb2) -> float {
                float dotProduct = 0.0f;
                float norm1 = 0.0f;
                float norm2 = 0.0f;

                for (size_t i = 0; i < emb1.size(); i++) {
                    dotProduct += emb1[i] * emb2[i];
                    norm1 += emb1[i] * emb1[i];
                    norm2 += emb2[i] * emb2[i];
                }

                // Avoid division by zero
                if (norm1 == 0.0f || norm2 == 0.0f) return 0.0f;

                return dotProduct / (std::sqrt(norm1) * std::sqrt(norm2));
            };

            // Create chunks based on semantic similarity and size
            std::vector<std::string> currentChunk;
            size_t currentLength = 0;

            for (size_t i = 0; i < sentences.size(); i++)
            {
                const std::string& sentence = sentences[i];
                size_t sentenceLength = sentence.length();

                if (currentLength + sentenceLength > (size_t)m_chunkSize && !currentChunk.empty())
                {
                    // Current chunk would exceed size limit, finalize it
                    std::string chunkText;
                    for (const auto& s : currentChunk) {
                        chunkText += s;
                    }
                    chunk newChunk = {document.filename, chunkText, {}, {}};
                    chunks.push_back(newChunk);

                    currentChunk.clear();
                    currentChunk.push_back(sentence);
                    currentLength = sentenceLength;
                }
                else
                {
                    if (i > 0 && !currentChunk.empty())
                    {
                        // Check semantic similarity with previous sentence
                        float similarity = calculateCosineSimilarity(
                            sentenceEmbeddings[i],
                            sentenceEmbeddings[i-1]
                        );

                        LOGINFO("Sentence similarity: " + std::to_string(similarity) +
                                " (threshold: " + std::to_string(similarityThreshold) + ")");

                        if (similarity < similarityThreshold)
                        {
                            // Semantic break detected, finalize current chunk
                            std::string chunkText;
                            for (const auto& s : currentChunk) {
                                chunkText += s;
                            }
                            chunk newChunk = {document.filename, chunkText, {}, {}};
                            chunks.push_back(newChunk);

                            currentChunk.clear();
                            currentChunk.push_back(sentence);
                            currentLength = sentenceLength;
                        }
                        else
                        {
                            // Add to current chunk
                            currentChunk.push_back(sentence);
                            currentLength += sentenceLength;
                        }
                    }
                    else
                    {
                        // First sentence or empty chunk
                        currentChunk.push_back(sentence);
                        currentLength += sentenceLength;
                    }
                }
            }

            // Add the final chunk if not empty
            if (!currentChunk.empty())
            {
                std::string chunkText;
                for (const auto& s : currentChunk) {
                    chunkText += s;
                }

                chunk newChunk = {document.filename, chunkText, {}, {}};
                chunks.push_back(newChunk);
            }

            LOGINFO("Created " + std::to_string(chunks.size()) + " semantic chunks from document: " + std::string(document.filename));
        }
        catch (const std::exception& e)
        {
            LOGERROR("Error during semantic chunking: " + std::string(e.what()) +
                          ", falling back to basic chunking");
            useSemanticChunking = false;
        }
    }

    // Use basic chunking as fallback or if semantic chunking was not requested
    if (!useSemanticChunking)
    {
        LOGINFO("Using basic chunking for document: " + std::string(document.filename));

        // Basic chunking approach based only on size
        std::string currentChunk;

        for (const auto& sentence : sentences)
        {
            // If adding this sentence would exceed the chunk size and we already have content,
            // create a new chunk
            if ((currentChunk.length() + sentence.length() > (size_t)m_chunkSize) && !currentChunk.empty())
            {
                chunk newChunk = {document.filename, currentChunk, {}, {}};
                chunks.push_back(newChunk);
                currentChunk = "";
            }

            // Add the sentence to the current chunk
            currentChunk += sentence;
        }

        // Add the final chunk if not empty
        if (!currentChunk.empty())
        {
            chunk newChunk = {document.filename, currentChunk, {}, {}};
            chunks.push_back(newChunk);
        }

        LOGINFO("Created " + std::to_string(chunks.size()) + " basic chunks from document: " + std::string(document.filename));
    }

    return chunks;
}

/*++

Routine Description:

    Generates embeddings for document chunks using the embedding model.

Arguments:

    chunks - Vector of chunks to generate embeddings for.
    progressCallback - Optional callback for reporting progress.

Return Value:

    bool indicating success or failure of embedding generation.

--*/
bool
RagDatabaseBuilder::GenerateEmbeddings(
    _Inout_ std::vector<chunk>& chunks
)
{
    if (chunks.empty())
    {
        LOGERROR("No chunks to generate embeddings for");
        return false;
    }

    LOGINFO("Generating embeddings for " + std::to_string(chunks.size()) + " chunks");

    // Track timing
    auto startTime = std::chrono::high_resolution_clock::now();

    // Use the embedding model to generate embeddings
    bool success = embed_encode_batch(m_params, chunks);

    auto endTime = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double> embeddingTime = endTime - startTime;

    // Store embedding time in metadata
    m_metadata.embeddingTime = embeddingTime.count();

    if (!success)
    {
        LOGERROR("Failed to generate embeddings for chunks");
        return false;
    }

    LOGINFO("Embeddings generated in " + std::to_string(embeddingTime.count()) + " seconds");
    return true;
}

bool
RagDatabaseBuilder::BuildVectorDatabase(
    _In_ const std::vector<chunk>& allChunks
)
{
  //  LOGINFO("Saving RAG database to: " + m_indexPath);
     // Create HNSW index

    // HNSW parameters
    int maxElements = std::max(10000, (int)(allChunks.size() * 1.5)); // Some buffer room
    int M = 16;               // Links per element
    int efConstruction = 200; // Build time vs accuracy tradeoff
    int efSearch = 100;       // Search accuracy parameter

    // Update metadata
    m_metadata.indexM = M;
    m_metadata.indexEfConstruction = efConstruction;
    m_metadata.indexEfSearch = efSearch;

    LOGINFO("Creating HNSW index with M=" + std::to_string(M) +
              ", efConstruction=" + std::to_string(efConstruction));

    // Initialize index
    hnswlib::L2Space space(m_params.n_dim);
    hnswlib::HierarchicalNSW<float>* hnsw =
        new hnswlib::HierarchicalNSW<float>(&space, maxElements, M, efConstruction);
    hnsw->setEf(efSearch);

    // Add all vectors to the index
    for (size_t i = 0; i < allChunks.size(); i++)
    {
        // Add point with ID equal to its position
        hnsw->addPoint(allChunks[i].embeddings.data(), (hnswlib::labeltype)i);
    }

    LOGINFO("Saving database files...");

    // Save the database
    bool success = SaveDatabase(allChunks, hnsw);

    // Clean up
    delete hnsw;

    if (success)
    {
        LOGINFO("RAG database built successfully with " +
                  std::to_string(allChunks.size()) + " chunks");
        return true;
    }
    else
    {
        LOGERROR("Failed to save RAG database");
        return false;
    }
}
    /*++

Routine Description:

    Saves the RAG database files (vector index and chunk data).

Arguments:

    chunks - The document chunks with embeddings.
    vectorDb - The HNSW vector database.

Return Value:

    bool indicating success or failure of database saving.

--*/
bool
RagDatabaseBuilder::SaveDatabase(
    _In_ const std::vector<chunk>& chunks,
    _In_ hnswlib::HierarchicalNSW<float>* vectorDb
)
{
    LOGINFO("Saving RAG database to: " + m_indexPath);

    try
    {
        // Create output directory if it doesn't exist
        fs::create_directories(m_outputDirectory);

        // Save the HNSW index
        vectorDb->saveIndex(m_indexPath);

        LOGINFO("Vector database saved: max_elements=" +
                  std::to_string(vectorDb->getMaxElements()) +
                  ", current_elements=" +
                  std::to_string(vectorDb->getCurrentElementCount()));

        // Create chunks JSON
        nlohmann::json chunksJson = nlohmann::json::array();

        for (size_t i = 0; i < chunks.size(); i++)
        {
            nlohmann::json chunkObj;
            chunkObj["id"] = i;
            chunkObj["source"] = chunks[i].filename;
            chunkObj["text"] = chunks[i].textdata;
            chunkObj["tokens"] = chunks[i].tokens.size();

            chunksJson.push_back(chunkObj);
        }

        // Save chunks JSON
        std::ofstream chunksFile(m_chunksPath);
        if (!chunksFile.is_open())
        {
            LOGERROR("Failed to open chunks file for writing: " + m_chunksPath);
            return false;
        }

        chunksFile << chunksJson.dump(2); // Pretty print with 2 spaces
        chunksFile.close();

        LOGINFO("Chunks data saved: " + std::to_string(chunks.size()) + " chunks");

        // Save metadata
        if (!SaveMetadata(m_documentStats))
        {
            LOGERROR("Failed to save metadata");
            return false;
        }

        return true;
    }
    catch (const std::exception& e)
    {
        LOGERROR("Exception saving database: " + std::string(e.what()));
        return false;
    }
}

/*++

Routine Description:

    Saves metadata about the RAG database.

Arguments:

    documentStats - Statistics about the processed documents.

Return Value:

    bool indicating success or failure of metadata saving.

--*/
bool
RagDatabaseBuilder::SaveMetadata(
    _In_ const std::vector<DocumentStats>& documentStats
)
{
    LOGINFO("Saving RAG metadata to: " + m_metadataPath);

    try
    {
        // Get current time
        auto currentTime = std::chrono::system_clock::now();
        std::time_t currentTimeT = std::chrono::system_clock::to_time_t(currentTime);
        char timeBuffer[100];
        std::strftime(timeBuffer, sizeof(timeBuffer), "%Y-%m-%d %H:%M:%S", std::localtime(&currentTimeT));

        // Create metadata JSON
        nlohmann::json metadata;

        // Basic info
        metadata["created_at"] = timeBuffer;
        metadata["model"] = m_metadata.modelName;
        metadata["documents_path"] = m_metadata.documentsPath;
        metadata["dimension"] = m_metadata.dimension;
        metadata["document_count"] = m_metadata.documentCount;

        // Document types
        nlohmann::json documentTypes;
        documentTypes["docx"] = m_metadata.docxCount;
        documentTypes["txt"] = m_metadata.txtCount;
        documentTypes["log"] = m_metadata.logCount;
        documentTypes["pdf"] = m_metadata.pdfCount;
        documentTypes["unsupported"] = m_metadata.unsupportedCount;
        documentTypes["failed"] = m_metadata.failedCount;
        metadata["document_types"] = documentTypes;

        // Chunk info
        metadata["chunk_count"] = m_metadata.chunkCount;
        metadata["chunk_size"] = m_metadata.chunkSize;
        metadata["total_tokens"] = m_metadata.totalTokens;

        // Index params
        nlohmann::json indexParams;
        indexParams["ef_construction"] = m_metadata.indexEfConstruction;
        indexParams["M"] = m_metadata.indexM;
        indexParams["ef_search"] = m_metadata.indexEfSearch;
        metadata["index_params"] = indexParams;

        // Performance
        metadata["embedding_time"] = m_metadata.embeddingTime;
        metadata["verbose"] = m_params.verbose;

        // Document details
        nlohmann::json documents = nlohmann::json::array();

        for (const auto& docStat : documentStats)
        {
            nlohmann::json doc;
            doc["filename"] = docStat.filename;
            doc["path"] = docStat.path;
            doc["size_bytes"] = docStat.sizeBytes;
            doc["extraction_time"] = docStat.extractionTime;
            doc["char_count"] = docStat.charCount;
            doc["type"] = docStat.fileType;

            documents.push_back(doc);
        }

        metadata["documents"] = documents;

        // Write to file
        std::ofstream metadataFile(m_metadataPath);
        if (!metadataFile.is_open())
        {
            LOGERROR("Failed to open metadata file for writing: " + m_metadataPath);
            return false;
        }

        metadataFile << metadata.dump(2); // Pretty print with 2 spaces
        metadataFile.close();

        LOGINFO("Metadata saved successfully");
        return true;
    }
    catch (const std::exception& e)
    {
        LOGERROR("Exception saving metadata: " + std::string(e.what()));
        return false;
    }
}

/*++

Routine Description:

    Main build thread function that performs the RAG database build.

Arguments:

    jobId - ID of the job.
    folderPath - Path to the folder containing documents.
    outputDir - Path to the output directory.
    chunkSize - Size of text chunks.
    similarityThreshold - Threshold for semantic chunking.
    modelName - Name of the embedding model.
    useGpu - Whether to use GPU for embeddings.
    jobManager - Pointer to the job manager.

Return Value:

    None.

--*/
void
RagDatabaseBuilder::BuildDatabaseThread(
    _In_z_ const std::string& jobId,
    _In_z_ const std::string& folderPath,
    _In_z_ const std::string& outputDir,
    _In_ int chunkSize,
    _In_ float similarityThreshold,
    _In_z_ const std::string& modelName,
    _In_ bool useGpu,
    _In_ RagJobManager* jobManager
)
{
    LOGINFO("RagDatabaseBuilder: Build thread started for job ", jobId.c_str());

    // Set build in progress state
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_buildInProgress = true;
    }

    // Create a status object to track progress
    RagJobStatus status;
    status.status = "starting";
    status.progress = 0.0;
    status.message = "Initializing build process";
    status.completed = false;
    status.usingGpu = useGpu;

    // Update job status
    if (jobManager)
    {
        jobManager->UpdateJobStatus(jobId, status);
    }

    try
    {

        LOGINFO("Extracting documents...");

        // 1. Extract text from documents
        status.status = "extracting";
        status.progress = 0.1;
        status.message = "Extracting documents";
        jobManager->UpdateJobStatus(jobId, status);

        bool success = ExtractDocumentContent(folderPath);
        if (!success)
        {
            LOGERROR("Failed to extract text from documents");
            status.status = "Extraction Failed";
            status.progress = 1.0;
            status.message = "Failed to process documents";
            jobManager->UpdateJobStatus(jobId, status);
            return;
        }

        LOGINFO("Chunking documents...");

        // 2. Load the embedding model
        status.status = "chunking";
        status.progress = 0.2;
        status.message = "Chunking documents";
        jobManager->UpdateJobStatus(jobId, status);

         // Collection of all chunks across all documents
        std::vector<chunk> allChunks;

        // Process each document into chunks - using basic chunking by default
        for (size_t i = 0; i < m_documents.size(); i++)
        {
            std::vector<chunk> documentChunks = ChunkDocument(m_documents[i], false, similarityThreshold);
           LOGINFO("Document " + std::string(m_documents[i].filename) + ": " +
                   std::to_string(documentChunks.size()) + " chunks");

            // Add to collection
            allChunks.insert(allChunks.end(), documentChunks.begin(), documentChunks.end());
        }

        // Update metadata
        m_metadata.chunkCount = (int)allChunks.size();
        if (allChunks.empty())
        {
            LOGERROR("No chunks generated from documents");
            status.status = "Chunking Failed";
            status.progress = 1.0;
            status.message = "Failed to chunk documents";
            jobManager->UpdateJobStatus(jobId, status);
            return;
        }

        LOGINFO("Embedding documents...");

        // Update status for embedding
        status.status = "embedding";
        status.progress = 0.5;
        jobManager->UpdateJobStatus(jobId, status);

        // Generate embeddings
        success = GenerateEmbeddings(allChunks);
        if (!success)
        {
            LOGERROR("Failed to generate embeddings");
            status.status = "Embedding Failed";
            status.progress = 1.0;
            status.message = "Failed to embed documents";
            jobManager->UpdateJobStatus(jobId, status);

            // Mark build as no longer in progress
            {
                std::lock_guard<std::mutex> lock(m_mutex);
                m_buildInProgress = false;
            }
            return;
        }

        // Count tokens
        for (const auto& chunk : allChunks)
        {
            m_metadata.totalTokens += (int)chunk.tokens.size();
        }


        // Update status for indexing
        status.status = "indexing";
        status.progress = 0.8;
        status.message = "Building HNSWLIB index";
        jobManager->UpdateJobStatus(jobId, status);

        LOGINFO("Building vector database...");

        success = BuildVectorDatabase(allChunks);
        if (!success)
        {
            LOGERROR("Failed to build vector database");
            status.status = "Indexing Failed";
            status.progress = 1.0;
            status.message = "Failed to build vector database";
            jobManager->UpdateJobStatus(jobId, status);
            // Mark build as no longer in progress
            {
                std::lock_guard<std::mutex> lock(m_mutex);
                m_buildInProgress = false;
            }
            return;
        }

        // Build completed successfully
        status.status = "completed";
        status.progress = 1.0;
        status.message = "Database built successfully";
        status.completed = true;

        // Final job status update
        jobManager->UpdateJobStatus(jobId, status);

        LOGINFO("RagDatabaseBuilder: Build completed successfully for job " +  jobId);
    }
    catch (const std::exception& e)
    {
        LOGERROR("RagDatabaseBuilder: Error during build for job " + jobId +":"+ e.what());

        status.status = "failed";
        status.message = "Error: " + std::string(e.what());
        status.completed = true;
        jobManager->UpdateJobStatus(jobId, status);
    }

    // Mark build as no longer in progress
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_buildInProgress = false;
    }
}


/*++

Routine Description:

    Initializes GPU detection.

Arguments:

    None.

Return Value:

    None.

--*/
void RagDatabaseBuilder::InitializeGpuDetection()
{
    LOGINFO("RagDatabaseBuilder: Detecting GPU availability");

    try
    {
        // Try NVIDIA GPU detection
        HMODULE hNvml = LoadLibraryA("nvml.dll");
        if (hNvml)
        {
            typedef int (*nvmlInitFunc)();
            typedef int (*nvmlDeviceGetCountFunc)(unsigned int*);
            typedef int (*nvmlDeviceGetNameFunc)(void*, char*, unsigned int);
            typedef int (*nvmlDeviceGetHandleFunc)(unsigned int, void**);

            auto nvmlInit = (nvmlInitFunc)GetProcAddress(hNvml, "nvmlInit_v2");
            auto nvmlDeviceGetCount = (nvmlDeviceGetCountFunc)GetProcAddress(hNvml, "nvmlDeviceGetCount_v2");
            auto nvmlDeviceGetName = (nvmlDeviceGetNameFunc)GetProcAddress(hNvml, "nvmlDeviceGetName");
            auto nvmlDeviceGetHandle = (nvmlDeviceGetHandleFunc)GetProcAddress(hNvml, "nvmlDeviceGetHandleByIndex_v2");

            bool proceed = (nvmlInit && nvmlDeviceGetCount && nvmlDeviceGetName && nvmlDeviceGetHandle);
            proceed = proceed && (nvmlInit() == 0);

            unsigned int deviceCount = 0;
            proceed = proceed && (nvmlDeviceGetCount(&deviceCount) == 0 && deviceCount > 0);

            void* device = nullptr;
            proceed = proceed && (nvmlDeviceGetHandle(0, &device) == 0);

            char name[256] = {0};
            proceed = proceed && (nvmlDeviceGetName(device, name, sizeof(name)) == 0);

            FreeLibrary(hNvml);

            if (proceed)
            {
                m_gpuAvailable = true;
                m_gpuName = name;
                LOGINFO("RagDatabaseBuilder: GPU available: true, Name: ", m_gpuName);
                return;
            }
        }

        // Try DirectX 12 detection
        HMODULE hD3D12 = LoadLibraryA("d3d12.dll");
        if (hD3D12)
        {
            m_gpuAvailable = true;
            m_gpuName = "Generic GPU (DirectX 12 capable)";
            FreeLibrary(hD3D12);
            LOGINFO("RagDatabaseBuilder: GPU available: true, Name: ", m_gpuName);
            return;
        }

        // No GPU detected
        m_gpuAvailable = false;
        m_gpuName = "None";
        LOGINFO("RagDatabaseBuilder: GPU available: false, Name: ", m_gpuName);
    }
    catch (const std::exception& e)
    {
        LOGERROR("RagDatabaseBuilder: Error detecting GPU: ", e.what());
        m_gpuAvailable = false;
        m_gpuName = "None";
    }
}
