/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    ragbuilder.css

Abstract:

    This module provides styling for the RAG Builder UI.
    Contains specific styles for the RAG database builder interface.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 03/24/2025

--*/

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.section {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #292929;
    border-radius: 8px;
}

.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

input[type="text"], 
input[type="number"],
input[type="file"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #444;
    border-radius: 4px;
    background-color: #333;
    color: #fff;
}

button {
    padding: 10px 15px;
    background-color: #0078d4;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

button:hover {
    background-color: #0063ad;
}

button:disabled {
    background-color: #666;
    cursor: not-allowed;
}

.progress-container {
    margin-top: 20px;
    width: 100%;
    background-color: #333;
    border-radius: 4px;
    overflow: hidden;
    /* Add border to make the container more visible */
    border: 1px solid #444;
    /* Set a fixed height to ensure it's visible */
    height: 24px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

.progress-bar {
    height: 100%;
    background-color: #0078d4;
    width: 0%;
    /* Add a gradient to make progress more visible */
    background-image: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.15) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.15) 50%,
        rgba(255, 255, 255, 0.15) 75%,
        transparent 75%,
        transparent
    );
    background-size: 40px 40px;
    transition: width 0.3s ease;
    min-width: 1px; /* Ensure the bar is at least minimally visible */
    /* Add subtle animation to make progress obvious */
    animation: progress-bar-stripes 2s linear infinite;
}

/* Add a class for completed progress bar with no animation */
.progress-bar.completed {
    animation: none;
    background-image: none;
    background-color: #2ecc71; /* Different color for completed state */
}

@keyframes progress-bar-stripes {
    from { background-position: 40px 0; }
    to { background-position: 0 0; }
}

.status {
    margin-top: 10px;
    font-style: italic;
    /* Make status more visible */
    padding: 5px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    font-weight: 500;
}

.log-container {
    margin-top: 20px;
    max-height: 200px;
    overflow-y: auto;
    background-color: #1e1e1e;
    padding: 10px;
    border-radius: 4px;
    font-family: monospace;
    white-space: pre-wrap;
}

/* Path display for selected folder */
.path-display {
    margin-top: 8px;
    font-size: 14px;
    padding: 8px;
    background-color: #222;
    border-radius: 4px;
    word-break: break-all;
    display: none; /* Initially hidden */
}

/* Browse button specific style */
#browseButton {
    position: relative;
    overflow: hidden;
}

/* Debug/test style to use during troubleshooting */
.file-input-debug {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

/* Collapsible section styles */
.collapsible {
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 10px;
    background-color: #333;
    border-radius: 4px;
    margin-bottom: 10px;
}

.collapsible:hover {
    background-color: #444;
}

.collapsible-content {
    display: none;
    padding: 10px 0;
}

.collapsible.active .arrow {
    transform: rotate(180deg);
}

.arrow {
    border: solid white;
    border-width: 0 3px 3px 0;
    display: inline-block;
    padding: 3px;
    transform: rotate(45deg);
    transition: transform 0.3s;
}

/* Document list styles */
.document-list {
    margin-top: 15px;
    background-color: #222;
    border-radius: 4px;
    padding: 10px;
    max-height: 200px;
    overflow-y: auto;
}

.document-item {
    display: flex;
    justify-content: space-between;
    padding: 5px;
    border-bottom: 1px solid #333;
}

.document-item:last-child {
    border-bottom: none;
}

.document-name {
    flex-grow: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.document-type {
    margin-left: 10px;
    color: #999;
}

.document-size {
    margin-left: 10px;
    color: #999;
}

/* Add styling for the remove button */
.document-remove {
    margin-left: 10px;
    color: #ff6b6b;
    cursor: pointer;
    opacity: 0.7;
    font-weight: bold;
}

.document-remove:hover {
    opacity: 1;
}

/* File selection toggle */
.file-selection-toggle {
    display: flex;
    margin-bottom: 10px;
}

.file-selection-toggle button {
    flex: 1;
    background-color: #333;
    border: 1px solid #444;
}

.file-selection-toggle button.active {
    background-color: #0078d4;
    border: 1px solid #0078d4;
}
