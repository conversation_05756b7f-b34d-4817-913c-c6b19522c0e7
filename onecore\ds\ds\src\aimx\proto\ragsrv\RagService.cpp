/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    RagService.cpp

Abstract:

    This module implements the RAG service class for hosting HTTP endpoints
    and providing RAG functionality through a REST API.

Author:

    <PERSON><PERSON><PERSON> (r<PERSON><PERSON>) 04/12/2025

--*/

#include "RagService.h"
#include "RagHttpServer.h"
#include "RAGDbHelper.h"
#include "../common/debug.h"
#include <windows.h>

// Initialize static members
RagService* RagService::s_service = nullptr;
SERVICE_STATUS_HANDLE RagService::s_statusHandle = NULL;
SERVICE_STATUS RagService::s_status = {};

// Service name
const wchar_t* SERVICE_NAME = L"RagService";

/*++

Routine Description:

    Constructor for the RagService class.

Arguments:

    None.

Return Value:

    None.

--*/
RagService::RagService() 
    : m_running(false)
    , m_httpServer(nullptr)
{
    s_service = this;
}

/*++

Routine Description:

    Destructor for the RagService class.

Arguments:

    None.

Return Value:

    None.

--*/
RagService::~RagService() 
{
    CleanupResources();
    s_service = nullptr;
}

/*++

Routine Description:

    Main entry point for the service when started by the Service Control Manager.

Arguments:

    argc - Number of arguments.
    argv - Arguments passed to the service.

Return Value:

    None.

--*/
void WINAPI 
RagService::ServiceMain(
    DWORD /* argc */, 
    LPWSTR* /* argv */
    )
{
    LOGINFO("RAG Service: ServiceMain started");
    
    // Create the service instance if it doesn't exist
    if (s_service == nullptr) 
    {
        s_service = new RagService();
        if (s_service == nullptr)
        {
            LOGERROR("RAG Service: Failed to create service instance");
            return;
        }
        LOGINFO("RAG Service: Service instance created");
    }

    // Register the service control handler
    s_statusHandle = RegisterServiceCtrlHandlerW(SERVICE_NAME, ServiceCtrlHandler);

    if (s_statusHandle == NULL) 
    {
        LOGERROR("RAG Service: RegisterServiceCtrlHandler failed: %d", GetLastError());
        delete s_service;
        s_service = nullptr;
        return;
    }

    // Initialize the service status
    s_service->SetServiceStatus(SERVICE_START_PENDING, NO_ERROR, 3000);

    // Initialize the service
    if (!s_service->Initialize()) 
    {
        LOGERROR("RAG Service: Service initialization failed");
        s_service->SetServiceStatus(SERVICE_STOPPED, ERROR_SERVICE_SPECIFIC_ERROR);
        delete s_service;
        s_service = nullptr;
        return;
    }

    // Service is now running
    s_service->SetServiceStatus(SERVICE_RUNNING);
    
    LOGINFO("RAG Service: Service is running");

    // Run the service
    s_service->Run();

    // Service is stopping
    s_service->SetServiceStatus(SERVICE_STOP_PENDING, NO_ERROR, 3000);
    
    // Cleanup
    s_service->CleanupResources();
    
    // Service has stopped
    s_service->SetServiceStatus(SERVICE_STOPPED);
    
    LOGINFO("RAG Service: Service has stopped");
    
    // Clean up the service instance
    delete s_service;
    s_service = nullptr;
}

/*++

Routine Description:

    Service control handler for the service control manager.

Arguments:

    control - The control code.

Return Value:

    None.

--*/
void WINAPI 
RagService::ServiceCtrlHandler(
    DWORD control
    )
{
    switch (control) 
    {
    case SERVICE_CONTROL_STOP:
        LOGINFO("RAG Service: Received stop command");
        if (s_service != nullptr)
        {
            s_service->Stop();
        }
        break;

    case SERVICE_CONTROL_INTERROGATE:
        // Just update current status
        if (s_service != nullptr)
        {
            s_service->SetServiceStatus(s_status.dwCurrentState);
        }
        break;

    default:
        // Unhandled control code
        break;
    }
}

/*++

Routine Description:

    Initializes the service.

Arguments:

    None.

Return Value:

    bool - True if initialization succeeded, false otherwise.

--*/
bool 
RagService::Initialize()
{
    LOGINFO("RAG Service: Initializing service");
    
    // Initialize service resources
    if (!InitializeResources())
    {
        LOGERROR("RAG Service: Failed to initialize resources");
        return false;
    }
    
    // Initialize RAG components
    if (!InitializeRag())
    {
        LOGERROR("RAG Service: Failed to initialize RAG components");
        return false;
    }
    
    // Start HTTP server
    if (!StartHttpServer())
    {
        LOGERROR("RAG Service: Failed to start HTTP server");
        return false;
    }
    
    LOGINFO("RAG Service: Service initialized successfully");
    return true;
}

/*++

Routine Description:

    Runs the service main loop.

Arguments:

    None.

Return Value:

    None.

--*/
void 
RagService::Run()
{
    m_running = true;
    
    LOGINFO("RAG Service: Entering main service loop");
    
    while (m_running)
    {
        // Wait for events or perform periodic tasks
        Sleep(1000);
    }
    
    LOGINFO("RAG Service: Exiting main service loop");
}

/*++

Routine Description:

    Stops the service.

Arguments:

    None.

Return Value:

    None.

--*/
void 
RagService::Stop()
{
    LOGINFO("RAG Service: Stopping service");
    m_running = false;
}

/*++

Routine Description:

    Updates the service status with the Service Control Manager.

Arguments:

    currentState - Current state of the service.
    exitCode - Exit code for the service.
    waitHint - Estimated time for pending operations.

Return Value:

    None.

--*/
void 
RagService::SetServiceStatus(
    DWORD currentState, 
    DWORD exitCode, 
    DWORD waitHint
    )
{
    s_status.dwServiceType = SERVICE_WIN32_OWN_PROCESS;
    s_status.dwCurrentState = currentState;
    
    if (currentState == SERVICE_START_PENDING)
    {
        s_status.dwControlsAccepted = 0;
    }
    else
    {
        s_status.dwControlsAccepted = SERVICE_ACCEPT_STOP;
    }

    if (exitCode == 0)
    {
        s_status.dwWin32ExitCode = NO_ERROR;
        s_status.dwServiceSpecificExitCode = 0;
    }
    else
    {
        s_status.dwWin32ExitCode = ERROR_SERVICE_SPECIFIC_ERROR;
        s_status.dwServiceSpecificExitCode = exitCode;
    }

    s_status.dwCheckPoint = 0;
    s_status.dwWaitHint = waitHint;

    if (!::SetServiceStatus(s_statusHandle, &s_status))
    {
        LOGERROR("RAG Service: SetServiceStatus failed: %d", GetLastError());
    }
}

/*++

Routine Description:

    Initializes service resources.

Arguments:

    None.

Return Value:

    bool - True if resources were initialized, false otherwise.

--*/
bool 
RagService::InitializeResources()
{
    LOGINFO("RAG Service: Initializing resources");
    
    // Create HTTP server instance
    m_httpServer = std::make_unique<RagHttpServer>();
    
    return true;
}

/*++

Routine Description:

    Cleans up service resources.

Arguments:

    None.

Return Value:

    None.

--*/
void 
RagService::CleanupResources()
{
    LOGINFO("RAG Service: Cleaning up resources");
    
    // Cleanup HTTP server if it exists
    if (m_httpServer)
    {
        m_httpServer->Stop();
        m_httpServer.reset();
    }
}

/*++

Routine Description:

    Initializes RAG components.

Arguments:

    None.

Return Value:

    bool - True if the RAG components were initialized, false otherwise.

--*/
bool 
RagService::InitializeRag()
{
    LOGINFO("RAG Service: Initializing RAG components");

    // Initialize RAG
    RAG_initialize();
    
    return true;
}

/*++

Routine Description:

    Starts the HTTP server.

Arguments:

    None.

Return Value:

    bool - True if the HTTP server was started, false otherwise.

--*/
bool 
RagService::StartHttpServer()
{
    LOGINFO("RAG Service: Starting HTTP server");
    
    if (!m_httpServer)
    {
        LOGERROR("RAG Service: HTTP server instance not created");
        return false;
    }
    
    return m_httpServer->Start(GetRunningFlag());
}