/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    AimxRpcTests.h

Abstract:
    TAEF test class declaration for AIMXSRV RPC client methods.

Author:
    <PERSON> (SNAKE FIGHTER) (lindakup) 06/10/2025

--*/
#pragma once

#include "WexTestClass.h"

using namespace WEX::Logging;
using namespace WEX::Common;
using namespace WEX::TestExecution;

class AimxRpcTests : public WEX::TestClass<AimxRpcTests>
{
public:
    //static std::wstring s_domainFqdn;
    //static std::wstring s_domainAdminPassword;

    BEGIN_TEST_CLASS(AimxRpcTests)
        TEST_CLASS_PROPERTY(L"TestClass", L"AIMXSRV RPC")
        //TEST_CLASS_PROPERTY(L"DomainFQDN", L"<domain fqdn>")
        //TEST_CLASS_PROPERTY(L"DomainAdminPassword", L"<domain admin password>")
        //TEST_CLASS_SETUP(Setup)
    END_TEST_CLASS()

   // static bool Setup()
    //{
    //    WEX::TestExecution::TestData::TryGetValue(L"DomainFQDN", s_domainFqdn);
    //    WEX::TestExecution::TestData::TryGetValue(L"DomainAdminPassword", s_domainAdminPassword);
    //    return true;
    //}

    BEGIN_TEST_METHOD(TestAimxConnectAndClose)
        END_TEST_METHOD()

    BEGIN_TEST_METHOD(TestAimxProcessPrompt)
        END_TEST_METHOD()

    BEGIN_TEST_METHOD(TestOperationIdHijackProtection)
        TEST_METHOD_PROPERTY(L"Description", L"Verifies that an operationId cannot be used from a different context handle (hijack protection)")
        END_TEST_METHOD()

    BEGIN_TEST_METHOD(TestContextHandleSidProtection)
        TEST_METHOD_PROPERTY(L"Description", L"Verifies that a user with a different SID cannot use another user's context handle")
        END_TEST_METHOD()
};