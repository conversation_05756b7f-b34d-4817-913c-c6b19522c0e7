<#
.SYNOPSIS
    Active Directory Central Access Policy and Claims Management Tools for MCP Server
    
.DESCRIPTION
    This module registers MCP tools for Active Directory central access policy and claims management operations.
    Each tool is a direct wrapper around the corresponding AD PowerShell cmdlet with
    exact parameter passthrough and no output formatting.

.AUTHOR
    Rup<PERSON> Zhang (rizhang)
#>

# Import required modules
Import-Module ActiveDirectory -ErrorAction SilentlyContinue

function Register-CentralAccessPolicyManagementTools {
    [CmdletBinding()]
    param()

    # Add-ADCentralAccessPolicyMember - Adds central access rules to a central access policy in Active Directory
    Register-McpTool -Name "Add-ADCentralAccessPolicyMember" -Description "Adds central access rules to a central access policy in Active Directory." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Members) { $params.Members = $Arguments.Members }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Add-ADCentralAccessPolicyMember @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Central access policy identity (DN, GUID, or name)" }
            Members = @{ type = "array"; items = @{ type = "string" }; description = "Central access rules to add" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified central access policy object" }
        }
        required = @("Identity", "Members")
    }

    # Remove-ADCentralAccessPolicyMember - Removes central access rules from a central access policy in Active Directory
    Register-McpTool -Name "Remove-ADCentralAccessPolicyMember" -Description "Removes central access rules from a central access policy in Active Directory." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Members) { $params.Members = $Arguments.Members }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Remove-ADCentralAccessPolicyMember @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Central access policy identity (DN, GUID, or name)" }
            Members = @{ type = "array"; items = @{ type = "string" }; description = "Central access rules to remove" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified central access policy object" }
        }
        required = @("Identity", "Members")
    }

    # Get-ADCentralAccessRule - Retrieves central access rules from Active Directory
    Register-McpTool -Name "Get-ADCentralAccessRule" -Description "Retrieves central access rules from Active Directory." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Filter) { $params.Filter = $Arguments.Filter }
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.LDAPFilter) { $params.LDAPFilter = $Arguments.LDAPFilter }
        if ($Arguments.Properties) { $params.Properties = $Arguments.Properties }
        if ($Arguments.ResultPageSize) { $params.ResultPageSize = $Arguments.ResultPageSize }
        if ($Arguments.ResultSetSize) { $params.ResultSetSize = $Arguments.ResultSetSize }
        if ($Arguments.SearchBase) { $params.SearchBase = $Arguments.SearchBase }
        if ($Arguments.SearchScope) { $params.SearchScope = $Arguments.SearchScope }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        
        Get-ADCentralAccessRule @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Filter = @{ type = "string"; description = "PowerShell Expression Language filter string" }
            Identity = @{ type = "string"; description = "Central access rule identity (DN, GUID, or name)" }
            LDAPFilter = @{ type = "string"; description = "LDAP query string for filtering" }
            Properties = @{ type = "array"; items = @{ type = "string" }; description = "Additional properties to retrieve" }
            ResultPageSize = @{ type = "integer"; description = "Number of objects per page" }
            ResultSetSize = @{ type = "integer"; description = "Maximum number of objects to return" }
            SearchBase = @{ type = "string"; description = "Active Directory path to search under" }
            SearchScope = @{ type = "string"; enum = @("Base", "OneLevel", "Subtree"); description = "Scope of the search" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
    }

    # New-ADCentralAccessRule - Creates a central access rule in Active Directory
    Register-McpTool -Name "New-ADCentralAccessRule" -Description "Creates a central access rule in Active Directory." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Name) { $params.Name = $Arguments.Name }
        if ($Arguments.DisplayName) { $params.DisplayName = $Arguments.DisplayName }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.CurrentAcl) { $params.CurrentAcl = $Arguments.CurrentAcl }
        if ($Arguments.ProposedAcl) { $params.ProposedAcl = $Arguments.ProposedAcl }
        if ($Arguments.ResourceCondition) { $params.ResourceCondition = $Arguments.ResourceCondition }
        if ($Arguments.Path) { $params.Path = $Arguments.Path }
        if ($Arguments.OtherAttributes) { $params.OtherAttributes = $Arguments.OtherAttributes }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        New-ADCentralAccessRule @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Name = @{ type = "string"; description = "Name of the central access rule (required)" }
            DisplayName = @{ type = "string"; description = "Display name for the central access rule" }
            Description = @{ type = "string"; description = "Description of the central access rule" }
            CurrentAcl = @{ type = "string"; description = "Current access control list (SDDL format)" }
            ProposedAcl = @{ type = "string"; description = "Proposed access control list (SDDL format)" }
            ResourceCondition = @{ type = "string"; description = "Resource condition expression" }
            Path = @{ type = "string"; description = "Distinguished name of the container for the rule" }
            OtherAttributes = @{ type = "object"; description = "Additional attributes as hashtable" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the created central access rule object" }
        }
        required = @("Name")
    }

    # Set-ADCentralAccessRule - Modifies a central access rule in Active Directory
    Register-McpTool -Name "Set-ADCentralAccessRule" -Description "Modifies a central access rule in Active Directory." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Add) { $params.Add = $Arguments.Add }
        if ($Arguments.Clear) { $params.Clear = $Arguments.Clear }
        if ($Arguments.Remove) { $params.Remove = $Arguments.Remove }
        if ($Arguments.Replace) { $params.Replace = $Arguments.Replace }
        if ($Arguments.DisplayName) { $params.DisplayName = $Arguments.DisplayName }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.CurrentAcl) { $params.CurrentAcl = $Arguments.CurrentAcl }
        if ($Arguments.ProposedAcl) { $params.ProposedAcl = $Arguments.ProposedAcl }
        if ($Arguments.ResourceCondition) { $params.ResourceCondition = $Arguments.ResourceCondition }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Set-ADCentralAccessRule @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Central access rule identity (DN, GUID, or name)" }
            Add = @{ type = "object"; description = "Attributes to add as hashtable" }
            Clear = @{ type = "array"; items = @{ type = "string" }; description = "Attributes to clear" }
            Remove = @{ type = "object"; description = "Attributes to remove as hashtable" }
            Replace = @{ type = "object"; description = "Attributes to replace as hashtable" }
            DisplayName = @{ type = "string"; description = "Display name for the central access rule" }
            Description = @{ type = "string"; description = "Description of the central access rule" }
            CurrentAcl = @{ type = "string"; description = "Current access control list (SDDL format)" }
            ProposedAcl = @{ type = "string"; description = "Proposed access control list (SDDL format)" }
            ResourceCondition = @{ type = "string"; description = "Resource condition expression" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified central access rule object" }
        }
        required = @("Identity")
    }

    # Remove-ADCentralAccessRule - Removes a central access rule from Active Directory
    Register-McpTool -Name "Remove-ADCentralAccessRule" -Description "Removes a central access rule from Active Directory." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Confirm) { $params.Confirm = $Arguments.Confirm }
        if ($Arguments.WhatIf) { $params.WhatIf = $Arguments.WhatIf }
        
        Remove-ADCentralAccessRule @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Central access rule identity (DN, GUID, or name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Confirm = @{ type = "boolean"; description = "Prompt for confirmation before removing" }
            WhatIf = @{ type = "boolean"; description = "Show what would happen without executing" }
        }
        required = @("Identity")
    }

    # Get-ADClaimTransformPolicy - Returns one or more Active Directory claim transform objects based on a specified filter
    Register-McpTool -Name "Get-ADClaimTransformPolicy" -Description "Returns one or more Active Directory claim transform objects based on a specified filter." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Filter) { $params.Filter = $Arguments.Filter }
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.LDAPFilter) { $params.LDAPFilter = $Arguments.LDAPFilter }
        if ($Arguments.Properties) { $params.Properties = $Arguments.Properties }
        if ($Arguments.ResultPageSize) { $params.ResultPageSize = $Arguments.ResultPageSize }
        if ($Arguments.ResultSetSize) { $params.ResultSetSize = $Arguments.ResultSetSize }
        if ($Arguments.SearchBase) { $params.SearchBase = $Arguments.SearchBase }
        if ($Arguments.SearchScope) { $params.SearchScope = $Arguments.SearchScope }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        
        Get-ADClaimTransformPolicy @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Filter = @{ type = "string"; description = "PowerShell Expression Language filter string" }
            Identity = @{ type = "string"; description = "Claim transform policy identity (DN, GUID, or name)" }
            LDAPFilter = @{ type = "string"; description = "LDAP query string for filtering" }
            Properties = @{ type = "array"; items = @{ type = "string" }; description = "Additional properties to retrieve" }
            ResultPageSize = @{ type = "integer"; description = "Number of objects per page" }
            ResultSetSize = @{ type = "integer"; description = "Maximum number of objects to return" }
            SearchBase = @{ type = "string"; description = "Active Directory path to search under" }
            SearchScope = @{ type = "string"; enum = @("Base", "OneLevel", "Subtree"); description = "Scope of the search" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
    }

    # New-ADClaimTransformPolicy - Creates a new claim transformation policy object in Active Directory
    Register-McpTool -Name "New-ADClaimTransformPolicy" -Description "Creates a new claim transformation policy object in Active Directory." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Name) { $params.Name = $Arguments.Name }
        if ($Arguments.DisplayName) { $params.DisplayName = $Arguments.DisplayName }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.Rule) { $params.Rule = $Arguments.Rule }
        if ($Arguments.Path) { $params.Path = $Arguments.Path }
        if ($Arguments.OtherAttributes) { $params.OtherAttributes = $Arguments.OtherAttributes }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        New-ADClaimTransformPolicy @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Name = @{ type = "string"; description = "Name of the claim transform policy (required)" }
            DisplayName = @{ type = "string"; description = "Display name for the claim transform policy" }
            Description = @{ type = "string"; description = "Description of the claim transform policy" }
            Rule = @{ type = "string"; description = "Claim transformation rule" }
            Path = @{ type = "string"; description = "Distinguished name of the container for the policy" }
            OtherAttributes = @{ type = "object"; description = "Additional attributes as hashtable" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the created claim transform policy object" }
        }
        required = @("Name")
    }

    # Set-ADClaimTransformPolicy - Sets the properties of a claims transformation policy in Active Directory
    Register-McpTool -Name "Set-ADClaimTransformPolicy" -Description "Sets the properties of a claims transformation policy in Active Directory." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Add) { $params.Add = $Arguments.Add }
        if ($Arguments.Clear) { $params.Clear = $Arguments.Clear }
        if ($Arguments.Remove) { $params.Remove = $Arguments.Remove }
        if ($Arguments.Replace) { $params.Replace = $Arguments.Replace }
        if ($Arguments.DisplayName) { $params.DisplayName = $Arguments.DisplayName }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.Rule) { $params.Rule = $Arguments.Rule }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Set-ADClaimTransformPolicy @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Claim transform policy identity (DN, GUID, or name)" }
            Add = @{ type = "object"; description = "Attributes to add as hashtable" }
            Clear = @{ type = "array"; items = @{ type = "string" }; description = "Attributes to clear" }
            Remove = @{ type = "object"; description = "Attributes to remove as hashtable" }
            Replace = @{ type = "object"; description = "Attributes to replace as hashtable" }
            DisplayName = @{ type = "string"; description = "Display name for the claim transform policy" }
            Description = @{ type = "string"; description = "Description of the claim transform policy" }
            Rule = @{ type = "string"; description = "Claim transformation rule" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified claim transform policy object" }
        }
        required = @("Identity")
    }

    # Remove-ADClaimTransformPolicy - Removes a claim transformation policy object from Active Directory
    Register-McpTool -Name "Remove-ADClaimTransformPolicy" -Description "Removes a claim transformation policy object from Active Directory." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Confirm) { $params.Confirm = $Arguments.Confirm }
        if ($Arguments.WhatIf) { $params.WhatIf = $Arguments.WhatIf }

        Remove-ADClaimTransformPolicy @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Claim transform policy identity (DN, GUID, or name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Confirm = @{ type = "boolean"; description = "Prompt for confirmation before removing" }
            WhatIf = @{ type = "boolean"; description = "Show what would happen without executing" }
        }
        required = @("Identity")
    }

    # Clear-ADClaimTransformLink - Removes a claims transformation from being applied to one or more cross-forest trust relationships in Active Directory
    Register-McpTool -Name "Clear-ADClaimTransformLink" -Description "Removes a claims transformation from being applied to one or more cross-forest trust relationships in Active Directory." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Policy) { $params.Policy = $Arguments.Policy }
        if ($Arguments.TrustRole) { $params.TrustRole = $Arguments.TrustRole }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        Clear-ADClaimTransformLink @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Trust relationship identity (DN, GUID, or name)" }
            Policy = @{ type = "string"; description = "Claim transform policy to remove" }
            TrustRole = @{ type = "string"; enum = @("Trusted", "Trusting"); description = "Trust role" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified trust object" }
        }
        required = @("Identity", "Policy", "TrustRole")
    }

    # Set-ADClaimTransformLink - Applies a claims transformation to one or more cross-forest trust relationships in Active Directory
    Register-McpTool -Name "Set-ADClaimTransformLink" -Description "Applies a claims transformation to one or more cross-forest trust relationships in Active Directory." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Policy) { $params.Policy = $Arguments.Policy }
        if ($Arguments.TrustRole) { $params.TrustRole = $Arguments.TrustRole }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        Set-ADClaimTransformLink @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Trust relationship identity (DN, GUID, or name)" }
            Policy = @{ type = "string"; description = "Claim transform policy to apply" }
            TrustRole = @{ type = "string"; enum = @("Trusted", "Trusting"); description = "Trust role" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified trust object" }
        }
        required = @("Identity", "Policy", "TrustRole")
    }

    # Get-ADClaimType - Returns a claim type from Active Directory
    Register-McpTool -Name "Get-ADClaimType" -Description "Returns a claim type from Active Directory." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Filter) { $params.Filter = $Arguments.Filter }
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.LDAPFilter) { $params.LDAPFilter = $Arguments.LDAPFilter }
        if ($Arguments.Properties) { $params.Properties = $Arguments.Properties }
        if ($Arguments.ResultPageSize) { $params.ResultPageSize = $Arguments.ResultPageSize }
        if ($Arguments.ResultSetSize) { $params.ResultSetSize = $Arguments.ResultSetSize }
        if ($Arguments.SearchBase) { $params.SearchBase = $Arguments.SearchBase }
        if ($Arguments.SearchScope) { $params.SearchScope = $Arguments.SearchScope }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }

        Get-ADClaimType @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Filter = @{ type = "string"; description = "PowerShell Expression Language filter string" }
            Identity = @{ type = "string"; description = "Claim type identity (DN, GUID, or name)" }
            LDAPFilter = @{ type = "string"; description = "LDAP query string for filtering" }
            Properties = @{ type = "array"; items = @{ type = "string" }; description = "Additional properties to retrieve" }
            ResultPageSize = @{ type = "integer"; description = "Number of objects per page" }
            ResultSetSize = @{ type = "integer"; description = "Maximum number of objects to return" }
            SearchBase = @{ type = "string"; description = "Active Directory path to search under" }
            SearchScope = @{ type = "string"; enum = @("Base", "OneLevel", "Subtree"); description = "Scope of the search" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
    }

    # New-ADClaimType - Creates a new claim type in Active Directory
    Register-McpTool -Name "New-ADClaimType" -Description "Creates a new claim type in Active Directory." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.DisplayName) { $params.DisplayName = $Arguments.DisplayName }
        if ($Arguments.ID) { $params.ID = $Arguments.ID }
        if ($Arguments.SourceAttribute) { $params.SourceAttribute = $Arguments.SourceAttribute }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.Enabled) { $params.Enabled = $Arguments.Enabled }
        if ($Arguments.RestrictValues) { $params.RestrictValues = $Arguments.RestrictValues }
        if ($Arguments.SuggestedValues) { $params.SuggestedValues = $Arguments.SuggestedValues }
        if ($Arguments.ValueType) { $params.ValueType = $Arguments.ValueType }
        if ($Arguments.Path) { $params.Path = $Arguments.Path }
        if ($Arguments.OtherAttributes) { $params.OtherAttributes = $Arguments.OtherAttributes }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        New-ADClaimType @params
    } -InputSchema @{
        type = "object"
        properties = @{
            DisplayName = @{ type = "string"; description = "Display name for the claim type (required)" }
            ID = @{ type = "string"; description = "Unique identifier for the claim type" }
            SourceAttribute = @{ type = "string"; description = "Source attribute for the claim type" }
            Description = @{ type = "string"; description = "Description of the claim type" }
            Enabled = @{ type = "boolean"; description = "Whether the claim type is enabled" }
            RestrictValues = @{ type = "boolean"; description = "Whether to restrict values to suggested values" }
            SuggestedValues = @{ type = "array"; items = @{ type = "string" }; description = "Suggested values for the claim type" }
            ValueType = @{ type = "string"; enum = @("String", "UInt64", "Boolean"); description = "Value type for the claim" }
            Path = @{ type = "string"; description = "Distinguished name of the container for the claim type" }
            OtherAttributes = @{ type = "object"; description = "Additional attributes as hashtable" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the created claim type object" }
        }
        required = @("DisplayName")
    }
}

# Function is available after dot-sourcing
