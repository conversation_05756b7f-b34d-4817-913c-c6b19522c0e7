/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    WebViewMessageHandlerEntraMigration.cpp

Abstract:

    This module implements Entra ID Migration assessment functionality 
    for the WebView message handler. Provides migration readiness analysis,
    protocol compatibility assessment, security policy evaluation, and
    actionable remediation guidance for Azure Entra ID migration.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 05/23/2025

--*/

#include "pch.h"
#include "WebViewMessageHandler.h"
#include <sstream>
#include <string>
#include <algorithm>
#include <ctime>
#include <iomanip>
#include <random>
#include <chrono>

// Include debug macros - check if Debug.h exists, otherwise define locally
#ifndef LOGVERBOSE
#define LOGVERBOSE(msg) // Empty macro if not defined
#define LOGERROR(msg)   // Empty macro if not defined  
#define LOGINFO(msg)    // Empty macro if not defined
#endif

// Include string conversion utilities from the existing codebase
extern std::wstring Utf8ToWide(const std::string& utf8);
extern std::string WideToUtf8(const std::wstring& wide);

// Entra ID Migration assessment data generation
namespace {
    // Random number generator
    std::random_device rd;
    std::mt19937 gen(rd());

    // Generate random integer between min and max (inclusive)
    INT
    RandomInt(
        _In_ INT nMin,
        _In_ INT nMax
        )
    {
        std::uniform_int_distribution<> dis(nMin, nMax);
        return dis(gen);
    }

    // Generate random float between min and max
    FLOAT
    RandomFloat(
        _In_ FLOAT fMin,
        _In_ FLOAT fMax
        )
    {
        std::uniform_real_distribution<> dis(fMin, fMax);
        return static_cast<FLOAT>(dis(gen));
    }

    // Generate migration readiness assessment data
    nlohmann::json
    GenerateAssessmentData()
    {
        nlohmann::json data;
        
        // Overall readiness score (70-85 range for realistic assessment)
        INT nOverallScore = RandomInt(70, 85);
        data["overallScore"] = nOverallScore;
        
        // Statistics
        data["stats"] = {
            {"totalUsers", RandomInt(1200, 1300)},
            {"totalGroups", RandomInt(150, 180)},
            {"criticalIssues", RandomInt(5, 12)},
            {"warnings", RandomInt(15, 30)}
        };
        
        // Readiness distribution
        INT nTotal = 161;  // Total items analyzed
        INT nCritical = RandomInt(3, 8);
        INT nMajor = RandomInt(6, 12);
        INT nMinor = RandomInt(15, 25);
        INT nReady = nTotal - nCritical - nMajor - nMinor;
        
        data["readiness"] = {
            {"ready", nReady},
            {"minor", nMinor}, 
            {"major", nMajor},
            {"critical", nCritical}
        };
        
        // Protocol compatibility analysis
        data["protocols"] = {
            {"LDAP", {{"supported", RandomInt(90, 98)}, {"unsupported", RandomInt(2, 10)}}},
            {"Kerberos", {{"supported", RandomInt(85, 95)}, {"unsupported", RandomInt(5, 15)}}},
            {"NTLM", {{"supported", 0}, {"unsupported", 100}}},
            {"SAML", {{"supported", 100}, {"unsupported", 0}}},
            {"OAuth 2.0", {{"supported", 100}, {"unsupported", 0}}},
            {"Basic Auth", {{"supported", RandomInt(10, 25)}, {"unsupported", RandomInt(75, 90)}}}
        };
        
        // Security policy compliance
        data["security"] = {
            {"passwordPolicies", RandomInt(80, 90)},
            {"accountLockout", RandomInt(90, 98)},
            {"groupPolicies", RandomInt(65, 80)},
            {"auditPolicies", RandomInt(75, 85)},
            {"trustRelationships", RandomInt(55, 70)}
        };
        
        // Category breakdown
        nlohmann::json categories = nlohmann::json::array();
        
        categories.push_back({
            {"name", "User Accounts"},
            {"status", "ready"},
            {"score", RandomInt(90, 98)},
            {"issues", RandomInt(2, 5)}
        });
        
        categories.push_back({
            {"name", "Group Policies"},
            {"status", "warning"},
            {"score", RandomInt(65, 75)},
            {"issues", RandomInt(8, 15)}
        });
        
        categories.push_back({
            {"name", "Security Protocols"},
            {"status", "critical"},
            {"score", RandomInt(40, 55)},
            {"issues", RandomInt(6, 12)}
        });
        
        categories.push_back({
            {"name", "Legacy Applications"},
            {"status", "warning"},
            {"score", RandomInt(60, 75)},
            {"issues", RandomInt(10, 20)}
        });
        
        data["categories"] = categories;
        
        // Action items
        nlohmann::json actionItems = nlohmann::json::array();
          actionItems.push_back({
            {"id", "1"},
            {"title", "Replace NTLM v1 Authentication"},
            {"category", "critical"},
            {"description", "NTLM v1 authentication is not supported in Azure Entra ID and poses significant security risks. All applications must be updated to use modern authentication methods."},
            {"impact", std::to_string(RandomInt(20, 30)) + " applications affected"},
            {"effort", "High"},
            {"estimatedTime", "3-4 weeks"},
            {"completed", false}
        });
        
        actionItems.push_back({
            {"id", "2"},
            {"title", "Update Password Complexity Policy"},
            {"category", "high"},
            {"description", "Current password policy does not meet Azure AD requirements for complexity, length, and expiration settings."},
            {"impact", std::to_string(static_cast<int>(data["stats"]["totalUsers"])) + " users affected"},
            {"effort", "Medium"},
            {"estimatedTime", "1-2 weeks"},
            {"completed", false}
        });
        
        actionItems.push_back({
            {"id", "3"},
            {"title", "Configure Hybrid Identity Synchronization"},
            {"category", "high"},
            {"description", "Set up Azure AD Connect to synchronize on-premises identities with Azure Entra ID for seamless hybrid authentication."},
            {"impact", "All user accounts (" + std::to_string(static_cast<int>(data["stats"]["totalUsers"])) + ")"},
            {"effort", "Medium"},
            {"estimatedTime", "2-3 weeks"},
            {"completed", false}
        });
        
        actionItems.push_back({
            {"id", "4"},
            {"title", "Remediate Unconstrained Delegation"},
            {"category", "critical"},
            {"description", "Unconstrained Kerberos delegation presents significant security risks in hybrid environments and must be replaced with constrained delegation."},
            {"impact", std::to_string(RandomInt(40, 60)) + " service accounts affected"},
            {"effort", "High"},
            {"estimatedTime", "2-3 weeks"},
            {"completed", false}
        });
        
        actionItems.push_back({
            {"id", "5"},
            {"title", "Update Legacy Application Authentication"},
            {"category", "medium"},
            {"description", "Legacy applications using basic authentication or outdated protocols need to be updated for modern authentication compatibility."},
            {"impact", std::to_string(RandomInt(12, 18)) + " legacy applications"},
            {"effort", "High"},
            {"estimatedTime", "4-6 weeks"},
            {"completed", false}
        });
        
        actionItems.push_back({
            {"id", "6"},
            {"title", "Implement Multi-Factor Authentication"},
            {"category", "medium"},
            {"description", "Enable MFA for all privileged accounts and configure conditional access policies for enhanced security."},
            {"impact", std::to_string(RandomInt(150, 200)) + " privileged accounts"},
            {"effort", "Medium"},
            {"estimatedTime", "1-2 weeks"},
            {"completed", false}
        });
        
        actionItems.push_back({
            {"id", "7"},
            {"title", "Review and Update Group Policy Objects"},
            {"category", "medium"},
            {"description", "Assess existing GPOs for cloud compatibility and update policies that conflict with Azure Entra ID requirements."},
            {"impact", std::to_string(RandomInt(80, 100)) + " Group Policy Objects"},
            {"effort", "Medium"},
            {"estimatedTime", "2-3 weeks"},
            {"completed", false}
        });
        
        actionItems.push_back({
            {"id", "8"},
            {"title", "Configure Certificate-Based Authentication"},
            {"category", "low"},
            {"description", "Set up certificate-based authentication for enhanced security and prepare certificate infrastructure for cloud integration."},
            {"impact", "Certificate infrastructure"},
            {"effort", "Low"},
            {"estimatedTime", "1 week"},
            {"completed", false}
        });
        
        data["actionItems"] = actionItems;
        
        return data;
    }

    // Generate detailed information for specific categories or protocols
    nlohmann::json
    GenerateDetailData(
        _In_ const std::string& strCategory,
        _In_ const std::string& strItem
        )
    {
        nlohmann::json details;
        
        if (strCategory == "protocol" && strItem == "NTLM")
        {
            details = {
                {"category", "Protocol Analysis"},
                {"item", "NTLM Authentication"},
                {"compatibility", "Not Supported"},
                {"riskLevel", "High"},
                {"affectedSystems", nlohmann::json::array({
                    "Exchange Server 2016",
                    "SharePoint 2019", 
                    "Legacy LOB Application 1",
                    "File Server Authentication"
                })},
                {"recommendations", nlohmann::json::array({
                    "Upgrade to Kerberos authentication where possible",
                    "Implement Azure AD Application Proxy for web applications",
                    "Configure modern authentication for Exchange",
                    "Update application code to support OAuth 2.0"
                })},
                {"migrationSteps", nlohmann::json::array({
                    "Inventory all NTLM-dependent applications",
                    "Test applications with Kerberos authentication",
                    "Configure service principal names (SPNs)",
                    "Update application configurations",
                    "Validate authentication flow",
                    "Disable NTLM after verification"
                })}
            };
        }
        else if (strCategory == "security" && strItem == "Password Policies")
        {
            details = {
                {"category", "Security Policy"},
                {"item", "Password Complexity Requirements"},
                {"currentCompliance", RandomInt(75, 85)},
                {"targetCompliance", 100},
                {"issues", nlohmann::json::array({
                    "Minimum password length is 8 characters (Azure AD requires 8-256)",
                    "Password expiration set to 90 days (Azure AD recommends no expiration)",
                    "Password history count is 12 (Azure AD supports up to 24)",
                    "Account lockout threshold is 5 attempts (Azure AD default is 10)"
                })},
                {"recommendations", nlohmann::json::array({
                    "Update minimum password length policy",
                    "Remove password expiration requirement",
                    "Increase password history count",
                    "Adjust account lockout settings",
                    "Implement Azure AD Password Protection"
                })},
                {"implementationSteps", nlohmann::json::array({
                    "Review current password policy settings",
                    "Create new Group Policy Object for updated policies",
                    "Test policy changes in pilot OU",
                    "Communicate changes to users",
                    "Deploy updated policies domain-wide",
                    "Monitor for authentication issues"
                })}
            };
        }
        else
        {
            // Generic detail response
            details = {
                {"category", strCategory},
                {"item", strItem},
                {"status", "Analysis Available"},
                {"message", "Detailed analysis for " + strItem + " in " + strCategory + " category"}
            };
        }
        
        return details;
    }

    // Generate PowerShell remediation script for specific action item
    std::string
    GenerateRemediationScript(
        _In_ const std::string& strItemId
        )
    {
        std::string script;
        
        if (strItemId == "1") // NTLM v1 replacement
        {
            script = R"(# PowerShell Script: Replace NTLM v1 Authentication
# This script helps identify and remediate NTLM v1 usage

# Check current NTLM settings
Write-Host "Checking current NTLM configuration..." -ForegroundColor Yellow
Get-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Lsa" -Name "LmCompatibilityLevel"

# Identify applications using NTLM v1
Write-Host "Scanning for NTLM v1 usage..." -ForegroundColor Yellow
Get-WinEvent -FilterHashtable @{LogName='Security'; ID=4624} | 
    Where-Object {$_.Message -like "*NTLM*"} | 
    Select-Object TimeCreated, Id, Message | 
    Format-Table -AutoSize

# Set NTLM compatibility level to disable v1
Write-Host "Updating NTLM compatibility level..." -ForegroundColor Green
Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Lsa" -Name "LmCompatibilityLevel" -Value 5

# Configure Kerberos settings
Write-Host "Configuring Kerberos settings..." -ForegroundColor Green
ksetup /setenctypeattr . AES256-CTS-HMAC-SHA1-96 AES128-CTS-HMAC-SHA1-96

Write-Host "NTLM v1 remediation completed. Restart required." -ForegroundColor Green)";
        }
        else if (strItemId == "2") // Password policy update
        {
            script = R"(# PowerShell Script: Update Password Complexity Policy
# This script updates password policies for Azure AD compatibility

Import-Module ActiveDirectory

# Get current password policy
Write-Host "Current password policy:" -ForegroundColor Yellow
Get-ADDefaultDomainPasswordPolicy

# Update password policy settings
Write-Host "Updating password policy for Azure AD compatibility..." -ForegroundColor Green

# Set minimum password length (Azure AD supports 8-256 characters)
Set-ADDefaultDomainPasswordPolicy -MinPasswordLength 12

# Remove password expiration (Azure AD best practice)
Set-ADDefaultDomainPasswordPolicy -MaxPasswordAge "00:00:00"

# Update password history
Set-ADDefaultDomainPasswordPolicy -PasswordHistoryCount 24

# Update account lockout settings
Set-ADDefaultDomainPasswordPolicy -LockoutThreshold 10
Set-ADDefaultDomainPasswordPolicy -LockoutDuration "00:30:00"
Set-ADDefaultDomainPasswordPolicy -LockoutObservationWindow "00:30:00"

# Verify changes
Write-Host "Updated password policy:" -ForegroundColor Green
Get-ADDefaultDomainPasswordPolicy

Write-Host "Password policy update completed successfully." -ForegroundColor Green)";
        }
        else if (strItemId == "3") // Hybrid identity setup
        {
            script = R"(# PowerShell Script: Azure AD Connect Preparation
# This script prepares the environment for Azure AD Connect

# Check prerequisites
Write-Host "Checking Azure AD Connect prerequisites..." -ForegroundColor Yellow

# Verify PowerShell version
$PSVersionTable.PSVersion

# Check .NET Framework version
Get-ChildItem "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" | 
    Get-ItemPropertyValue -Name Release | 
    ForEach-Object { [System.Version]::new($_ -band 0xFFFF, ($_ -shr 16) -band 0xFFFF) }

# Prepare service account for Azure AD Connect
Write-Host "Creating Azure AD Connect service account..." -ForegroundColor Green
$SecurePassword = ConvertTo-SecureString "P@ssw0rd123!" -AsPlainText -Force
New-ADUser -Name "AADConnect_SVC" -UserPrincipalName "<EMAIL>" -AccountPassword $SecurePassword -Enabled $true

# Add necessary permissions
Add-ADGroupMember -Identity "Enterprise Admins" -Members "AADConnect_SVC"

# Prepare UPN suffixes
Write-Host "Configuring UPN suffixes..." -ForegroundColor Green
# Add your Azure AD domain as UPN suffix
# Set-ADForest -UPNSuffixes @{Add="yourdomain.onmicrosoft.com"}

Write-Host "Azure AD Connect preparation completed." -ForegroundColor Green
Write-Host "Next steps: Download and install Azure AD Connect from Microsoft" -ForegroundColor Yellow)";
        }
        else
        {
            // Generic script template
            script = R"(# PowerShell Remediation Script
# Generated for Action Item: )" + strItemId + R"(

Write-Host "Starting remediation process..." -ForegroundColor Green

# Add your remediation steps here
# This is a template script for action item: )" + strItemId + R"(

Write-Host "Remediation script template generated." -ForegroundColor Yellow
Write-Host "Please customize this script for your specific environment." -ForegroundColor Yellow)";
        }
        
        return script;
    }
}

/*++

Routine Description:

    Handles messages for Entra ID Migration assessment sent from the WebView.

Arguments:

    message - The JSON message containing the action and parameters.

Return Value:

    None.

--*/
VOID
WebViewMessageHandler::HandleEntraMigrationMessage(
    _In_ const nlohmann::json& message
    )
{
    // Verify message contains the action field and it's a string
    if (!message.contains("action") || !message["action"].is_string())
    {
        LOGERROR("Entra ID migration message missing action field or not a string");
        return;
    }

    // Extract the action as a string
    std::string strAction = message["action"];
    LOGVERBOSE("Processing Entra ID migration action: " + strAction);

    try
    {
        if (strAction == "startAssessment")
        {
            LOGVERBOSE("Starting Entra ID migration assessment");
            
            // Simulate assessment process with delay
            Sleep(1500); // 1.5 second delay to simulate analysis
            
            // Generate comprehensive assessment data
            nlohmann::json assessmentData = GenerateAssessmentData();
            
            // Send assessment results back to WebView
            nlohmann::json response = {
                {"type", "entraidMigration"},
                {"action", "assessmentData"},
                {"data", assessmentData}
            };
            
            SendMessageToWebView(Utf8ToWide(response.dump()));
            LOGVERBOSE("Assessment data sent to WebView");
        }
        else if (strAction == "getDetails")
        {
            LOGVERBOSE("Received request for detailed information");
            
            std::string strCategory = message.contains("category") && message["category"].is_string() ? 
                message["category"] : "";
            std::string strItem = message.contains("item") && message["item"].is_string() ? 
                message["item"] : "";
            
            LOGVERBOSE("Detail request: category=" + strCategory + ", item=" + strItem);
            
            // Generate detailed information
            nlohmann::json detailData = GenerateDetailData(strCategory, strItem);
            
            // Send detail data back to WebView
            nlohmann::json response = {
                {"type", "entraidMigration"},
                {"action", "detailData"},
                {"details", detailData}
            };
            
            SendMessageToWebView(Utf8ToWide(response.dump()));
        }
        else if (strAction == "markComplete")
        {
            LOGVERBOSE("Marking action item as complete");
            
            std::string strItemId = message.contains("itemId") && message["itemId"].is_string() ? 
                message["itemId"] : "";
            
            LOGVERBOSE("Marking item " + strItemId + " as complete");
            
            // In a real implementation, this would update the backend state
            // For now, we just log the action
            
            // Send confirmation back to WebView
            nlohmann::json response = {
                {"type", "entraidMigration"},
                {"action", "itemCompleted"},
                {"itemId", strItemId},
                {"status", "success"}
            };
            
            SendMessageToWebView(Utf8ToWide(response.dump()));
        }
        else if (strAction == "generateScript")
        {
            LOGVERBOSE("Generating remediation script");
            
            std::string strItemId = message.contains("itemId") && message["itemId"].is_string() ? 
                message["itemId"] : "";
            
            LOGVERBOSE("Generating script for item: " + strItemId);
            
            // Generate PowerShell remediation script
            std::string script = GenerateRemediationScript(strItemId);
            
            // Send script back to WebView
            nlohmann::json response = {
                {"type", "entraidMigration"},
                {"action", "scriptGenerated"},
                {"itemId", strItemId},
                {"script", script}
            };
            
            SendMessageToWebView(Utf8ToWide(response.dump()));
        }
        else if (strAction == "exportReport")
        {
            LOGVERBOSE("Exporting migration assessment report");
            
            // In a real implementation, this would generate a comprehensive report
            // For now, we simulate the export process
            
            // Generate timestamp for report filename
            auto now = std::chrono::system_clock::now();
            auto now_time_value = std::chrono::system_clock::to_time_t(now);
            
            std::tm tmStruct;
            localtime_s(&tmStruct, reinterpret_cast<const time_t*>(&now_time_value));
            
            CHAR szTimestamp[100];
            strftime(szTimestamp, sizeof(szTimestamp), "%Y%m%d_%H%M%S", &tmStruct);
            
            std::string strReportPath = "C:\\Reports\\EntraID_Migration_Assessment_" + 
                std::string(szTimestamp) + ".pdf";
            
            // Send export status back to WebView
            nlohmann::json response = {
                {"type", "entraidMigration"},
                {"action", "reportExported"},
                {"status", "success"},
                {"reportPath", strReportPath}
            };
            
            SendMessageToWebView(Utf8ToWide(response.dump()));
        }
        else
        {
            LOGINFO("Unknown Entra ID migration action: " + strAction);
            
            // Send error response for unknown action
            nlohmann::json errorResponse = {
                {"type", "entraidMigration"},
                {"action", "error"},
                {"message", "Unknown action: " + strAction}
            };
            
            SendMessageToWebView(Utf8ToWide(errorResponse.dump()));
        }
    }
    catch (const std::exception& e)
    {
        LOGERROR("Exception in Entra ID migration handler: " + std::string(e.what()));
        
        // Send error response to WebView
        nlohmann::json errorResponse = {
            {"type", "entraidMigration"},
            {"action", "error"},
            {"message", "Internal error: " + std::string(e.what())}
        };
        
        SendMessageToWebView(Utf8ToWide(errorResponse.dump()), true);
    }
}