# Interactive PowerShell Command Generation Test
param(
    [string]$BaseUrl = "http://localhost:8082"
)

Write-Host "INTERACTIVE POWERSHELL COMMAND GENERATOR" -ForegroundColor Cyan
Write-Host "Base URL: $BaseUrl" -ForegroundColor Yellow
Write-Host "Type 'exit' or 'quit' to stop the interactive session" -ForegroundColor Gray
Write-Host "=" * 80

function Test-UserInput {
    param(
        [string]$UserInput
    )
    
    Write-Host "`nProcessing Input:" -ForegroundColor Green
    Write-Host "  $UserInput" -ForegroundColor White
    Write-Host "-" * 60
    
    $requestBody = @{
        userInput = $UserInput
        userId = "interactive-test-$(Get-Random)"
        environment = "production"
        priority = "normal"
        context = @{
            department = "IT"
            role = "administrator"
        }
    }
    
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    
    try {
        Write-Host "Sending request to IntentPlanningService..." -ForegroundColor Yellow
        
        $response = Invoke-RestMethod -Uri "$BaseUrl/api/IntentPlanning/analyze" -Method Post -Body ($requestBody | ConvertTo-Json -Depth 10) -ContentType "application/json" -TimeoutSec 60
        $stopwatch.Stop()
        
        # Display basic results
        if ($response.success) {
            Write-Host "SUCCESS" -ForegroundColor Green
        } else {
            Write-Host "FAILED" -ForegroundColor Red
            Write-Host "  Error: $($response.errorMessage)" -ForegroundColor Red
        }

        Write-Host "Response Time: $($stopwatch.ElapsedMilliseconds)ms" -ForegroundColor Gray
        
        # Extract and display workflow details
        if ($response.primaryWorkflow -and $response.primaryWorkflow.steps) {
            $stepCount = $response.primaryWorkflow.steps.Count
            Write-Host "Generated $stepCount workflow step$(if($stepCount -ne 1){'s'})" -ForegroundColor Cyan

            Write-Host "`nDETAILED WORKFLOW STEPS:" -ForegroundColor Magenta
            
            for ($i = 0; $i -lt $response.primaryWorkflow.steps.Count; $i++) {
                $step = $response.primaryWorkflow.steps[$i]
                
                Write-Host "`n  STEP $($i + 1):" -ForegroundColor Yellow
                Write-Host "    Description: $($step.description)" -ForegroundColor White

                if ($step.operation -and $step.operation -ne "fallback_operation") {
                    Write-Host "    PowerShell Command:" -ForegroundColor Green
                    Write-Host "      $($step.operation)" -ForegroundColor Cyan
                } elseif ($step.parameters -and $step.parameters.command -and $step.parameters.command -ne "fallback_operation") {
                    Write-Host "    PowerShell Command:" -ForegroundColor Green
                    Write-Host "      $($step.parameters.command)" -ForegroundColor Cyan
                } else {
                    Write-Host "    Fallback Operation (LLM failed to generate specific command)" -ForegroundColor Red
                }
                
                if ($step.parameters) {
                    if ($step.parameters.commandName) {
                        Write-Host "    Command Name: $($step.parameters.commandName)" -ForegroundColor Gray
                    }
                    if ($step.parameters.requiresApproval) {
                        Write-Host "    Requires Approval: $($step.parameters.requiresApproval)" -ForegroundColor Yellow
                    }
                }

                Write-Host "    Tool ID: $($step.toolId)" -ForegroundColor Gray
                Write-Host "    Timeout: $($step.timeout) seconds" -ForegroundColor Gray

                if ($step.retryPolicy) {
                    Write-Host "    Max Retries: $($step.retryPolicy.maxRetries)" -ForegroundColor Gray
                }
            }
            
            # Display workflow metadata
            Write-Host "`nWORKFLOW METADATA:" -ForegroundColor Magenta
            Write-Host "  Workflow ID: $($response.primaryWorkflow.workflowId)" -ForegroundColor Gray
            Write-Host "  Risk Level: $($response.primaryWorkflow.riskLevel)" -ForegroundColor Gray
            Write-Host "  Estimated Time: $($response.primaryWorkflow.estimatedTotalTimeSeconds) seconds" -ForegroundColor Gray
            Write-Host "  Planning Method: $($response.primaryWorkflow.planningMethod)" -ForegroundColor Gray
            Write-Host "  Planning Confidence: $($response.primaryWorkflow.planningConfidence)" -ForegroundColor Gray
            
        } else {
            Write-Host "No workflow steps generated" -ForegroundColor Red
        }

        # Display intent analysis results
        if ($response.analysisConfidence) {
            Write-Host "`nINTENT ANALYSIS:" -ForegroundColor Magenta
            Write-Host "  Analysis Confidence: $($response.analysisConfidence)" -ForegroundColor Gray
            Write-Host "  Analysis Method: $($response.analysisMethod)" -ForegroundColor Gray
        }
        
    }
    catch {
        $stopwatch.Stop()
        Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Response Time: $($stopwatch.ElapsedMilliseconds)ms" -ForegroundColor Gray

        if ($_.Exception.Response) {
            try {
                $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
                $responseBody = $reader.ReadToEnd()
                Write-Host "Response Body: $responseBody" -ForegroundColor Red
            }
            catch {
                Write-Host "Could not read error response body" -ForegroundColor Red
            }
        }
    }
}

# Interactive loop
while ($true) {
    Write-Host "`n" + "=" * 80
    Write-Host "Enter your IT request (or 'exit'/'quit' to stop):" -ForegroundColor Cyan
    $userInput = Read-Host "> "

    if ($userInput -eq "exit" -or $userInput -eq "quit" -or $userInput -eq "") {
        Write-Host "`nGoodbye!" -ForegroundColor Green
        break
    }

    Test-UserInput -UserInput $userInput
}

Write-Host "`nInteractive session completed!" -ForegroundColor Green
