!if 0
Copyright (c) Microsoft Corporation.  All rights reserved
!endif

TARGETNAME=aiwfbdr
TARGETTYPE=PROGRAM
UMTYPE=windows
UMENTRY=wwinmain
TARGET_DESTINATION=aiwfbdr

MSC_WARNING_LEVEL=/W4 /WX

WEBVIEW2LOCATION=$(OSDEPENDSROOT)\EdgeWebView2SDK
WEBVIEW2SDK=$(WEBVIEW2LOCATION)\build\native\include

!IF "$(_BUILDARCH)" == "amd64"
WEBVIEW2LOADER=$(WEBVIEW2LOCATION)\build\native\x64\WebView2LoaderStatic.lib
!endif

!IF "$(_BUILDARCH)" == "x86"
WEBVIEW2LOADER=$(WEBVIEW2LOCATION)\build\native\x86\WebView2LoaderStatic.lib
!endif

!IF "$(_BUILDARCH)" == "arm64"
WEBVIEW2LOADER=$(WEBVIEW2LOCATION)\build\native\arm64\WebView2LoaderStatic.lib
!endif

USE_MSVCRT                  = 1
USE_UNICRT                  = 1
USE_STL                     = 1
STL_VER                     = STL_VER_CURRENT
USE_DEFAULT_WIN32_LIBS      = 0
USE_NATIVE_EH               = 1

#PRECOMPILED_INCLUDE=pch.h
#PRECOMPILED_CXX=1

# 
# https://github.com/microsoft/STL/issues/4978
# workaround for STL bug 4978.. sigh
#
C_DEFINES = $(C_DEFINES) -D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR -DUNICODE

#
# Set it to 1 for the time being, remove it when productzation code is ready
#
MUI_VERIFY_NO_LOC_RESOURCE  = 1

SOURCES=\
    main.cpp \
    resources.rc \
    WebViewMessageHandler.cpp \

INCLUDES=\
    $(INCLUDES); \
    $(MINWIN_INTERNAL_PRIV_SDK_INC_PATH_L); \
    $(ONECORE_EXTERNAL_SDK_INC_PATH_L); \
    $(PROJECT_INTERNAL_SDK_METADATA_PATH)\cppwinrt; \
    $(OSDependsRoot)\EdgeWebView2SDK\build\native\include-winrt; \
    $(ONECOREUAPWINDOWS_RESTRICTED_INC_PATH_L); \
    $(PROJECT_INTERNAL_SDK_METADATA_PATH)\cppwinrt; \
    $(WEBVIEW2SDK); \
    ..\..\prod\common; \
    ..\..\prod\common\nlohmann-json\include; \
    ..\..\prod\common\hnswlib; \

TARGETLIBS=\
    $(WEBVIEW2LOADER) \
    $(ONECORE_EXTERNAL_SDK_LIB_PATH)\oleaut32.lib \
    $(ONECORE_EXTERNAL_SDK_LIB_PATH)\xmllite.lib \
    $(ONECORE_EXTERNAL_SDK_LIB_PATH)\onecore.lib \
    $(ONECORE_EXTERNAL_SDK_LIB_PATH)\CoreMessaging.lib \
    $(ONECOREUAP_EXTERNAL_SDK_LIB_PATH)\d3d11.lib \
    $(ONECOREUAP_EXTERNAL_SDK_LIB_PATH)\d2d1.lib \
    $(ONECOREUAP_INTERNAL_SDK_LIB_PATH)\onecoreuapuuid.lib \
    $(MINCORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\ext-ms-win-ntuser-message-l1-1-0.lib \
    $(MINCORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\ext-ms-win-ntuser-window-l1-1-1.lib \
    $(ONECORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\api-ms-win-ntuser-sysparams-l1-1-0.lib \
    $(MINCORE_INTERNAL_PRIV_SDK_LIB_VPATH_L)\ext-ms-win-rtcore-ntuser-cursor-l1.lib \
    $(MODERNCORE_INTERNAL_PRIV_SDK_LIB_VPATH_L)\ext-ms-win-ntuser-rim-l1.lib \
    $(MINWIN_INTERNAL_PRIV_SDK_LIB_PATH_L)\api-ms-win-core-misc-l1-1-0.lib \
    $(ONECORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\OneCore_Forwarder_ole32.lib \
    $(ONECORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\OneCore_Forwarder_shlwapi.lib \
    $(ONECORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\OneCore_Forwarder_version.lib \
    $(ONECORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\OneCore_Forwarder_gdiplus.lib \
    $(ONECORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\onecore_forwarder_shell32.lib \
    $(MODERNCORE_INTERNAL_PRIV_SDK_LIB_VPATH_L)\api-ms-win-rtcore-ntuser-synch-l1.lib \
    $(MODERNCORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\api-ms-win-rtcore-ntuser-window-l1-1-0.lib \
    $(MINCORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\ext-ms-win-gdi-dc-l1-2-0.lib \
    $(MINCORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\ext-ms-win-gdi-devcaps-l1-1-0.lib \
    $(MINCORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\ext-ms-win-gdi-font-l1-1-0.lib \
    $(MINCORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\ext-ms-win-gdi-font-l1-1-2.lib \
    $(MINCORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\ext-ms-win-ntuser-dc-access-ext-l1-1-0.lib \
    $(MINCORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\ext-ms-win-ntuser-dialogbox-l1-1-0.lib \
    $(MINCORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\ext-ms-win-ntuser-draw-l1-1-0.lib \
    $(MINCORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\ext-ms-win-ntuser-gui-l1-1-0.lib \
    $(MINCORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\ext-ms-win-ntuser-message-l1-1-0.lib \
    $(MINCORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\ext-ms-win-ntuser-window-l1-1-0.lib \
    $(MINCORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\ext-ms-win-rtcore-gdi-object-l1-1-0.lib \
    $(MINCORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\ext-ms-win-rtcore-ntuser-sysparams-l1-1-0.lib \
    $(ONECOREINETCORE_INTERNAL_LIB_PATH_L)\ext-ms-win-core-iuri-l1-1-0.lib \
    $(MINCORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\ext-ms-win-gdi-draw-l1-1-0.lib \
    $(ONECORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\ext-ms-win-dwmapi-ext-l1-1-0.lib \
    $(ONECORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\OneCore_Forwarder_user32.lib \
    $(ONECORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\OneCore_Forwarder_gdi32.lib \

PASS0_BINPLACE = $(PASS0_BINPLACE) \
    -:DEST $(TARGET_DESTINATION)\htmlui\ htmlui\index.html \
    -:DEST $(TARGET_DESTINATION)\htmlui\ htmlui\placeholder.svg \
    -:DEST $(TARGET_DESTINATION)\htmlui\assets htmlui\assets\index-CXrecSf5.js \
    -:DEST $(TARGET_DESTINATION)\htmlui\assets htmlui\assets\index-D3cqCuyM.css \