/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    aimxrpcclient.cpp

Abstract:
    AIMXSRV RPC client implementation.

Author:
    <PERSON> (SNAKE FIGHTER) (lindakup) 06/10/2025

--*/
#include "pch.hxx"
#include "aimxrpc_c.c"
#include "aimxrpc.h"
#include <unordered_map>
#include <mutex>
#include <algorithm>
#include <objbase.h>
#include "AimxCommon.h"

#include "aimxrpcclient.cpp.tmh"


// Bidirectional mapping between GUID and AIMX_CLIENT_INFO*
static std::unordered_map<GUID, AIMX_CLIENT_INFO*, GuidHash, GuidEqual> g_GuidToInfoMap;
static std::unordered_map<AIMX_CLIENT_INFO*, GUID> g_InfoToGuidMap;
static std::mutex g_ContextMapMutex;

// Add a mapping (thread-safe)
void AddContextMapping(const GUID& guid, AIMX_CLIENT_INFO* info) {
    std::lock_guard<std::mutex> lock(g_ContextMapMutex);
    g_GuidToInfoMap[guid] = info;
    g_InfoToGuidMap[info] = guid;
}

// Remove a mapping by GUID (thread-safe)
void RemoveContextMappingByGuid(const GUID& guid) {
    std::lock_guard<std::mutex> lock(g_ContextMapMutex);
    auto it = g_GuidToInfoMap.find(guid);
    if (it != g_GuidToInfoMap.end()) {
        g_InfoToGuidMap.erase(it->second);
        g_GuidToInfoMap.erase(it);
    }
}

// Remove a mapping by info pointer (thread-safe)
void RemoveContextMappingByInfo(AIMX_CLIENT_INFO* info) {
    std::lock_guard<std::mutex> lock(g_ContextMapMutex);
    auto it = g_InfoToGuidMap.find(info);
    if (it != g_InfoToGuidMap.end()) {
        g_GuidToInfoMap.erase(it->second);
        g_InfoToGuidMap.erase(it);
    }
}

// Lookup info by GUID (thread-safe)
AIMX_CLIENT_INFO* LookupInfoByGuid(const GUID& guid) {
    std::lock_guard<std::mutex> lock(g_ContextMapMutex);
    auto it = g_GuidToInfoMap.find(guid);
    return (it != g_GuidToInfoMap.end()) ? it->second : nullptr;
}

// Lookup GUID by info pointer (thread-safe)
GUID LookupGuidByInfo(AIMX_CLIENT_INFO* info) {
    std::lock_guard<std::mutex> lock(g_ContextMapMutex);
    auto it = g_InfoToGuidMap.find(info);
    return (it != g_InfoToGuidMap.end()) ? it->second : GUID{};
}

BOOL
AimxRpcClient::CreateAimxRpcClientHandle(
    _Outptr_result_nullonfailure_ HANDLE* pRpcHandle
    )
{
    TraceInfo(AimxClient, "Entry");
    LPWSTR pszStringBinding = NULL;
    RPC_STATUS RpcStatus;
    HANDLE hRpc = NULL;
    BOOL fResult = FALSE;
    RPC_SECURITY_QOS_V3 qosV3;
    const SID LocalSystemSid = { SID_REVISION, 1, SECURITY_NT_AUTHORITY, SECURITY_LOCAL_SYSTEM_RID };

    *pRpcHandle = NULL; // Initialize the output handle

    // Create a string binding for the AIMXSRV RPC interface
    RpcStatus = RpcStringBindingCompose(
        NULL,                       // no UUID
        L"ncalrpc",
        NULL,                       // Local server call only
        AIMXSRV_LRPC_ENDPOINT,
        NULL,                       // Options 
        &pszStringBinding);
    if (RpcStatus != RPC_S_OK)
    {
        TraceErr(AimxClient, "RpcStringBindingCompose failed: %u", RpcStatus);
        goto Exit;
    }

    // Create the RPC binding handle
    RpcStatus = RpcBindingFromStringBinding(
        pszStringBinding,
        &hRpc);

    if (RpcStatus != RPC_S_OK)
    {
        TraceErr(AimxClient, "RpcBindingFromStringBinding failed: %u", RpcStatus);
        goto Exit;
    }

    // Set the security quality of service for the RPC binding
    // Requires server to be running as system and impersonate the client.
    RtlZeroMemory(&qosV3, sizeof(qosV3));
    qosV3.Version = RPC_C_SECURITY_QOS_VERSION_3;
    qosV3.Capabilities = RPC_C_QOS_CAPABILITIES_MUTUAL_AUTH;
    qosV3.ImpersonationType = RPC_C_IMP_LEVEL_IMPERSONATE;
    qosV3.IdentityTracking = RPC_C_QOS_IDENTITY_STATIC;
    qosV3.AdditionalSecurityInfoType = 0;
    qosV3.Sid = (PSID)&LocalSystemSid; 

    RpcStatus = RpcBindingSetAuthInfoEx(
        hRpc,
        NULL,
        RPC_C_AUTHN_LEVEL_PKT_PRIVACY, 
        RPC_C_AUTHN_WINNT,
        NULL,                       // Authentication identity
        0,                          // Authzsvc
        (RPC_SECURITY_QOS*)&qosV3);

    if (RpcStatus != RPC_S_OK)
    {
        TraceErr(AimxClient, "RpcBindingSetAuthInfoEx failed: %u", RpcStatus);
        goto Exit;
    }

    *pRpcHandle = hRpc;
    hRpc = NULL;
    fResult = TRUE;

Exit:
    if (hRpc)
    {
        RpcBindingFree(&hRpc);
    }
    RpcStringFree(&pszStringBinding);
    TraceInfo(AimxClient, "Exit");
    return fResult;
}

AIMX_CLIENT_INFO*
AimxClientAllocInfo()
/*++

Routine Description:
    Allocates and zeroes an AIMX_CLIENT_INFO structure using MIDL_user_allocate.
    If AIMX_CLIENT_INFO has pointer members, allocates them as well.

Return Value:
    Pointer to allocated AIMX_CLIENT_INFO, or nullptr on failure.

--*/
{
    AIMX_CLIENT_INFO* info = nullptr;

    info = (AIMX_CLIENT_INFO*)MIDL_user_allocate(sizeof(AIMX_CLIENT_INFO));
    if (info)
    {
        ZeroMemory(info, sizeof(AIMX_CLIENT_INFO));
    }

    return info;
}

void AimxClientFreeInfo(
    _In_ AIMX_CLIENT_INFO* info
    )
    /*++

Routine Description:
    Frees an AIMX_CLIENT_INFO structure using MIDL_user_free.
    If AIMX_CLIENT_INFO has pointer members, frees them as well.

Arguments:
    info - Pointer to AIMX_CLIENT_INFO to free.

Return Value:
    None.

--*/
{
    if (!info) return;
    // Free the RPC handle if it exists
    if (info->RpcHandle)
    {
        RpcBindingFree(&info->RpcHandle);
        info->RpcHandle = nullptr; // Set to nullptr after freeing
    }
    // Free any other members if they were allocated
    if (!info->ConnectionIdStr.empty())
    {
        MIDL_user_free((void*)info->ConnectionIdStr.c_str());
        info->ConnectionIdStr.clear();
    }
    if (info->ConnectionId != GUID{})
    {
        info->ConnectionId = GUID{}; // Reset the GUID
    }
    MIDL_user_free(info);
}

HRESULT
AimxRpcClient::AimxConnect(
    _Out_ GUID* pContextId
    )
/*++

Routine Description:
    Establishes a new AIMX client context by connecting to the server, allocates and initializes an AIMX_CLIENT_INFO structure, and returns a new GUID context ID.

Arguments:
    pContextId - Receives the new GUID context ID on success.

Return Value:
    HRESULT indicating success or failure.

--*/
{
    TraceInfo(AimxClient, "Entry");
    HRESULT hr = S_OK;
    HANDLE rpcHandle = NULL;
    AIMXR_HANDLE contextHandle = nullptr;
    AIMX_CLIENT_INFO* clientInfo = nullptr;

    if (!pContextId)
    {
        TraceErr(AimxClient, "Invalid argument: pContextId is null");
        return E_INVALIDARG;
    }

    *pContextId = GUID{};
    if (!CreateAimxRpcClientHandle(&rpcHandle)) {
        hr = HRESULT_FROM_WIN32(GetLastError());
        TraceErr(AimxClient, "CreateAimxRpcClientHandle failed: %!HRESULT!", hr);
        goto Exit;
    }

    RpcTryExcept
    {
        hr = c_AimxrConnect(
            rpcHandle,
            &contextHandle);
    }
    RpcExcept(I_RpcExceptionFilter(RpcExceptionCode()))
    {
        hr = HRESULT_FROM_WIN32(RpcExceptionCode());
        TraceErr(AimxClient, "c_AimxrConnect exception: %!HRESULT!", hr);
    } RpcEndExcept;

    if (FAILED(hr) || !contextHandle)
    {
        TraceErr(AimxClient, "c_AimxrConnect failed: %!HRESULT!", hr);
        goto Exit;
    }

    // Generate a new GUID for the context ID
    hr = CoCreateGuid(pContextId);
    if (FAILED(hr))
    {
        TraceErr(AimxClient, "CoCreateGuid failed: %!HRESULT!", hr);
        goto Exit;
    }
    // Allocate and populate AIMX_CLIENT_INFO using MIDL_user_allocate
    clientInfo = AimxClientAllocInfo();
    if (!clientInfo) {
        hr = E_OUTOFMEMORY;
        TraceErr(AimxClient, "AimxClientAllocInfo failed for AIMX_CLIENT_INFO");
        goto Exit;
    }

    clientInfo->RpcHandle = contextHandle;
    clientInfo->ConnectionId = *pContextId;
    AddContextMapping(*pContextId, clientInfo);
    contextHandle = nullptr; // map owns context handle now.

    TraceInfo(AimxClient, "c_AimxrConnect succeeded, contextId: %!GUID!", pContextId);

Exit:
    if (rpcHandle)
    {
        RpcBindingFree(&rpcHandle);
    }
    if (contextHandle)
    {
        RpcBindingFree(&contextHandle);
    }
    if (FAILED(hr))
    {
        AimxClientFreeInfo(clientInfo);
        clientInfo = nullptr;
    }

    TraceInfo(AimxClient, "Exit");
    return hr;
}

HRESULT
AimxRpcClient::AimxClose(
    _In_ GUID contextId
    )
/*++

Routine Description:
    Closes the AIMX client context associated with the specified GUID, calls the server to close the context, removes the mapping, and frees the AIMX_CLIENT_INFO structure.

Arguments:
    contextId - GUID identifying the client context to close.

Return Value:
    HRESULT indicating success or failure.

--*/
{
    TraceInfo(AimxClient, "Entry. GUID is %!GUID!", &contextId);
    HRESULT hr = S_OK;
    AIMX_CLIENT_INFO* clientInfo = nullptr;

    clientInfo = LookupInfoByGuid(contextId);
    if (!clientInfo || !clientInfo->RpcHandle)
    {
        TraceErr(AimxClient, "Invalid argument: clientInfo/RpcHandle is null for GUID %!GUID!", &contextId);
        return E_INVALIDARG;
    }

    RpcTryExcept
    {
        hr = c_AimxrClose(&clientInfo->RpcHandle);
    }
    RpcExcept(I_RpcExceptionFilter(RpcExceptionCode()))
    {
        hr = HRESULT_FROM_WIN32(RpcExceptionCode());
        TraceErr(AimxClient, "c_AimxrClose exception: %!HRESULT!", hr);
    }RpcEndExcept;

    if (FAILED(hr))
    {
        TraceErr(AimxClient, "c_AimxrClose failed: %!HRESULT!", hr);
        goto Exit;
    }

    // Remove and free the mapping
    RemoveContextMappingByGuid(contextId);
    AimxClientFreeInfo(clientInfo);
    TraceInfo(AimxClient, "c_AimxrClose succeeded for contextId: %!GUID!", &contextId);

Exit:
    TraceInfo(AimxClient, "Exit. hr: %!HRESULT!", hr);
    return hr;
}

HRESULT
AimxRpcClient::AimxProcessPrompt(
    _In_ GUID contextId,
    _In_ LPCWSTR InputPrompt,
    _Out_ LPWSTR* Response)
/*++

Routine Description:
    Processes a prompt using the AIMX client context identified by the specified GUID. Sends the prompt to the server and retrieves the response.

Arguments:
    contextId   - GUID identifying the client context to use.
    InputPrompt - Prompt string to process.
    Response    - Receives the response string (allocated by the server, must be freed by caller).

Return Value:
    HRESULT indicating success or failure.

--*/
{
    TraceInfo(AimxClient, "Entry");
    HRESULT hr = S_OK;
    AIMX_CLIENT_INFO* clientInfo = nullptr;

    if (contextId == GUID{} || !InputPrompt || !Response)
    {
        TraceErr(AimxClient, "Invalid argument");
        return E_INVALIDARG;
    }

    *Response = nullptr;

    clientInfo = LookupInfoByGuid(contextId);
    if (!clientInfo || !clientInfo->RpcHandle)
    {
        TraceErr(AimxClient, "Invalid argument: clientInfo/RpcHandle is null for GUID %!GUID!", &contextId);
        return E_INVALIDARG;
    }

    RpcTryExcept
    {
        hr = c_AimxrProcessPrompt(
            clientInfo->RpcHandle,
            InputPrompt,
            Response);
    }
    RpcExcept(I_RpcExceptionFilter(RpcExceptionCode()))
    {
        hr = HRESULT_FROM_WIN32(RpcExceptionCode());
        TraceErr(AimxClient, "c_AimxrProcessPrompt exception: %!HRESULT!", hr);
    }RpcEndExcept;

    if (FAILED(hr))
    {
        TraceErr(AimxClient, "c_AimxrProcessPrompt failed: %!HRESULT!", hr);
    }

    TraceInfo(AimxClient, "Exit, hr: %!HRESULT!", hr);
    return hr;
}



HRESULT
AimxRpcClient::AimxPollConversationMessages(
    _In_ GUID contextId,
    _Out_ LPWSTR* messages
    )
/*++

Routine Description:
    Polls for new conversation messages

Arguments:
    contextId - The context ID for the session.
    messages - Output allocated string containing JSON array of messages (caller must free)

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxClient, "Entry");
    HRESULT hr = S_OK;
    AIMX_CLIENT_INFO* clientInfo = nullptr;

    if (contextId == GUID{} || !messages)
    {
        TraceErr(AimxClient, "Invalid arguments");
        return E_INVALIDARG;
    }

    *messages = nullptr;

    clientInfo = LookupInfoByGuid(contextId);
    if (!clientInfo || !clientInfo->RpcHandle)
    {
        TraceErr(AimxClient, "Invalid argument: clientInfo/RpcHandle is null for GUID %!GUID!", &contextId);
        return E_INVALIDARG;
    }

    RpcTryExcept
    {
        hr = c_AimxrPollConversationMessages(
            clientInfo->RpcHandle,
            messages);
    }
    RpcExcept(I_RpcExceptionFilter(RpcExceptionCode()))
    {
        hr = HRESULT_FROM_WIN32(RpcExceptionCode());
        TraceErr(AimxClient, "c_AimxrPollConversationMessages exception: %!HRESULT!", hr);
    }RpcEndExcept;

    if (FAILED(hr))
    {
        TraceErr(AimxClient, "c_AimxrPollConversationMessages failed: %!HRESULT!", hr);
    }

    TraceInfo(AimxClient, "Exit, hr: %!HRESULT!", hr);
    return hr;
}

HRESULT
AimxRpcClient::AimxGetConversationStatus(
    _In_ GUID contextId,
    _Out_ LPWSTR* statusJson
    )
/*++

Routine Description:
    Gets the current status of the conversation session.

Arguments:
    contextId - The context ID for the session.
    statusJson - Output allocated string containing JSON status (caller must free)

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxClient, "Entry");
    HRESULT hr = S_OK;
    AIMX_CLIENT_INFO* clientInfo = nullptr;

    if (contextId == GUID{} || !statusJson)
    {
        TraceErr(AimxClient, "Invalid arguments");
        return E_INVALIDARG;
    }

    *statusJson = nullptr;

    clientInfo = LookupInfoByGuid(contextId);
    if (!clientInfo || !clientInfo->RpcHandle)
    {
        TraceErr(AimxClient, "Invalid argument: clientInfo/RpcHandle is null for GUID %!GUID!", &contextId);
        return E_INVALIDARG;
    }

    RpcTryExcept
    {
        hr = c_AimxrGetConversationStatus(
            clientInfo->RpcHandle,
            statusJson);
    }
    RpcExcept(I_RpcExceptionFilter(RpcExceptionCode()))
    {
        hr = HRESULT_FROM_WIN32(RpcExceptionCode());
        TraceErr(AimxClient, "c_AimxrGetConversationStatus exception: %!HRESULT!", hr);
    }RpcEndExcept;

    if (FAILED(hr))
    {
        TraceErr(AimxClient, "c_AimxrGetConversationStatus failed: %!HRESULT!", hr);
    }

    TraceInfo(AimxClient, "Exit, hr: %!HRESULT!", hr);
    return hr;
}

HRESULT
AimxRpcClient::AimxStartConversation(
    _In_ GUID contextId,
    _In_ LPCWSTR query,
    _In_ LONG executionMode
    )
/*++

Routine Description:
    Starts an interactive conversation with real-time updates.

Arguments:
    contextId - The context ID for the session.
    query - The query to process.
    executionMode - The execution mode (automated or interactive).

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxClient, "Entry");
    HRESULT hr = S_OK;
    AIMX_CLIENT_INFO* clientInfo = nullptr;

    if (contextId == GUID{} || !query)
    {
        TraceErr(AimxClient, "Invalid arguments");
        return E_INVALIDARG;
    }

    clientInfo = LookupInfoByGuid(contextId);
    if (!clientInfo || !clientInfo->RpcHandle)
    {
        TraceErr(AimxClient, "Invalid argument: clientInfo/RpcHandle is null for GUID %!GUID!", &contextId);
        return E_INVALIDARG;
    }

    RpcTryExcept
    {
        hr = c_AimxrStartConversation(
            clientInfo->RpcHandle,
            query,
            executionMode);
    }
    RpcExcept(I_RpcExceptionFilter(RpcExceptionCode()))
    {
        hr = HRESULT_FROM_WIN32(RpcExceptionCode());
        TraceErr(AimxClient, "c_AimxrStartConversation exception: %!HRESULT!", hr);
    }RpcEndExcept;

    if (FAILED(hr))
    {
        TraceErr(AimxClient, "c_AimxrStartConversation failed: %!HRESULT!", hr);
    }

    TraceInfo(AimxClient, "Exit, hr: %!HRESULT!", hr);
    return hr;
}

HRESULT
AimxRpcClient::AimxGetLlmStatus(
    _In_ GUID contextId,
    _Out_ LPWSTR* statusJson
    )
/*++

Routine Description:
    Gets the current LLM service status and connectivity information.

Arguments:
    contextId - The context ID for the session.
    statusJson - Output allocated string containing JSON status (caller must free)

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxClient, "Entry");
    HRESULT hr = S_OK;
    AIMX_CLIENT_INFO* clientInfo = nullptr;

    if (contextId == GUID{} || !statusJson)
    {
        TraceErr(AimxClient, "Invalid arguments");
        return E_INVALIDARG;
    }

    *statusJson = nullptr;

    clientInfo = LookupInfoByGuid(contextId);
    if (!clientInfo || !clientInfo->RpcHandle)
    {
        TraceErr(AimxClient, "Invalid argument: clientInfo/RpcHandle is null for GUID %!GUID!", &contextId);
        return E_INVALIDARG;
    }

    RpcTryExcept
    {
        hr = c_AimxrGetLlmStatus(
            clientInfo->RpcHandle,
            statusJson);
    }
    RpcExcept(I_RpcExceptionFilter(RpcExceptionCode()))
    {
        hr = HRESULT_FROM_WIN32(RpcExceptionCode());
        TraceErr(AimxClient, "c_AimxrGetLlmStatus exception: %!HRESULT!", hr);
    }RpcEndExcept;

    if (FAILED(hr))
    {
        TraceErr(AimxClient, "c_AimxrGetLlmStatus failed: %!HRESULT!", hr);
    }

    TraceInfo(AimxClient, "Exit, hr: %!HRESULT!", hr);
    return hr;
}

HRESULT
AimxRpcClient::AimxGetMcpServerInfo(
    _In_ GUID contextId,
    _Out_ LPWSTR* serverInfoJson
    )
/*++

Routine Description:
    Gets information about registered MCP servers and their available tools.

Arguments:
    contextId - The context ID for the session.
    serverInfoJson - Output allocated string containing JSON server info (caller must free)

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxClient, "Entry");
    HRESULT hr = S_OK;
    AIMX_CLIENT_INFO* clientInfo = nullptr;

    if (contextId == GUID{} || !serverInfoJson)
    {
        TraceErr(AimxClient, "Invalid arguments");
        return E_INVALIDARG;
    }

    *serverInfoJson = nullptr;

    clientInfo = LookupInfoByGuid(contextId);
    if (!clientInfo || !clientInfo->RpcHandle)
    {
        TraceErr(AimxClient, "Invalid argument: clientInfo/RpcHandle is null for GUID %!GUID!", &contextId);
        return E_INVALIDARG;
    }

    RpcTryExcept
    {
        hr = c_AimxrGetMcpServerInfo(
            clientInfo->RpcHandle,
            serverInfoJson);
    }
    RpcExcept(I_RpcExceptionFilter(RpcExceptionCode()))
    {
        hr = HRESULT_FROM_WIN32(RpcExceptionCode());
        TraceErr(AimxClient, "c_AimxrGetMcpServerInfo exception: %!HRESULT!", hr);
    }RpcEndExcept;

    if (FAILED(hr))
    {
        TraceErr(AimxClient, "c_AimxrGetMcpServerInfo failed: %!HRESULT!", hr);
    }

    TraceInfo(AimxClient, "Exit, hr: %!HRESULT!", hr);
    return hr;
}

//constructor
AimxRpcClient::AimxRpcClient()
{
    TraceInfo(AimxClient, "Entry");
    TraceInfo(AimxClient, "Exit");
}

//destructor
AimxRpcClient::~AimxRpcClient()
{
    TraceInfo(AimxClient, "Entry");
    TraceInfo(AimxClient, "Exit");
}
