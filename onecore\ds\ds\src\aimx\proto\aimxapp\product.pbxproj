<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="ProductBuild" ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" Label="FirstImport" />
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <PropertyGroup Label="PropertySheets">
    <PlatformToolset>v110</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup>
    <BinplaceEarlyProductBuildAdditionalOptions>/:NOPDB</BinplaceEarlyProductBuildAdditionalOptions>
  </PropertyGroup>
  <ItemGroup Condition="'$(_BuildArch)'=='amd64'" >
    <BinplaceEarlyProductBuild Include="$(OSDependsRoot)\EdgeWebView2SDK\runtimes\win-x64\native\WebView2Loader.dll">
      <DestinationFolder>aimx</DestinationFolder>
    </BinplaceEarlyProductBuild>
  </ItemGroup>
  <ItemGroup Condition="'$(_BuildArch)'=='arm64'" >
    <BinplaceEarlyProductBuild Include="$(OSDependsRoot)\EdgeWebView2SDK\runtimes\win-$(_BuildArch)\native\WebView2Loader.dll">
      <DestinationFolder>aimx</DestinationFolder>
    </BinplaceEarlyProductBuild>
  </ItemGroup>
  <ItemGroup Condition="'$(_BuildArch)'=='x86'" >
    <BinplaceEarlyProductBuild Include="$(OSDependsRoot)\EdgeWebView2SDK\runtimes\win-$(_BuildArch)\native\WebView2Loader.dll">
      <DestinationFolder>aimx</DestinationFolder>
    </BinplaceEarlyProductBuild>
  </ItemGroup>  
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
</Project>