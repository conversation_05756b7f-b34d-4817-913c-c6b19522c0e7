// -------------------------------------------------------------------------------
// <copyright file="NativeMethods.cs" company="Microsoft">
//  Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
//
// <summary>
// Declares various native interop declarations.
// </summary>
//
// Owner:       lindakup 
// History:     (lindakup) Created 6/6/2025
// -------------------------------------------------------------------------------

using System;
using System.Runtime.InteropServices;
using System.Text;

namespace Microsoft.Windows.AIMX
{
    internal static class NativeMethods
    {
        // DllImport for AimxConnect
        [DllImport("aimxclient.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = false)]
        internal static extern uint AimxConnect(out Guid contextId);
        // Function definition placeholder (if needed)

        // DllImport for AimxClose
        [DllImport("aimxclient.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = false)]
        internal static extern uint AimxClose(Guid contextId);
        // Function definition placeholder (if needed)

        // DllImport for AimxProcessPrompt
        [DllImport("aimxclient.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = false)]
        public static extern uint AimxProcessPrompt(Guid contextId, [MarshalAs(UnmanagedType.LPWStr)] string promptText, out IntPtr responseText);
        // Function definition placeholder (if needed)

        // DllImport for AimxFree
        [DllImport("aimxclient.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = false)]
        public static extern void AimxFree(IntPtr p);

        // Conversation management methods (polling-based)
        [DllImport("aimxclient.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = false)]
        public static extern uint AimxPollConversationMessages(
            Guid contextId,
            out IntPtr messagesPtr);

        [DllImport("aimxclient.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = false)]
        public static extern uint AimxGetConversationStatus(
            Guid contextId,
            out IntPtr statusPtr);

        [DllImport("aimxclient.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = false)]
        public static extern uint AimxStartConversation(
            Guid contextId,
            [MarshalAs(UnmanagedType.LPWStr)] string query,
            int executionMode);

        [DllImport("aimxclient.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = false)]
        public static extern uint AimxGetLlmStatus(
            Guid contextId,
            out IntPtr statusPtr);

        [DllImport("aimxclient.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = false)]
        public static extern uint AimxGetMcpServerInfo(
            Guid contextId,
            out IntPtr serverInfoPtr);
    }
}