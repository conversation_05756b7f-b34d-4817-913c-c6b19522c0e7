<#
.SYNOPSIS
    MCP Protocol Handler Module
    
.DESCRIP<PERSON>ON
    Handles the Model Context Protocol (MCP) communication including:
    - JSON-RPC message parsing and formatting
    - MCP initialization handshake
    - Tool listing and execution requests
    - Error handling and responses

.AUTHOR
    Rupo Zhang (rizhang) 07-21-2025
#>

# Import constants module
Import-Module "$PSScriptRoot\McpConstants.psm1" -Force

# Global variables for MCP server state
$script:McpServer = $null
$script:McpTools = @{}
$script:McpCapabilities = @{
    tools = @{
        listChanged = $true
    }
    logging = @{}
}

function New-McpServer {
    [CmdletBinding()]
    param()



    $server = @{
        IsRunning = $false
        InputReader = $null
        OutputWriter = $null
    }

    $script:McpServer = $server
    return $server
}

function Start-McpServer {
    [CmdletBinding()]
    param(
        [hashtable]$Server
    )

    try {
        $Server.IsRunning = $true
        $Server.InputReader = [Console]::In
        $Server.OutputWriter = [Console]::Out



        # Main message processing loop
        while ($Server.IsRunning) {
            try {
                # Read JSON-RPC message from stdin
                $inputLine = $Server.InputReader.ReadLine()

                if ($null -eq $inputLine) {
                    # EOF reached, exit gracefully

                    break
                }

                if ([string]::IsNullOrWhiteSpace($inputLine)) {
                    continue
                }



                # Process the MCP message
                $response = Invoke-McpStdioMessage -InputLine $inputLine

                if ($null -ne $response) {
                    # Send response to stdout
                    $responseJson = $response | ConvertTo-Json -Depth 10 -Compress
                    $Server.OutputWriter.WriteLine($responseJson)
                    $Server.OutputWriter.Flush()


                }
            }
            catch {


                # Send error response if possible
                try {
                    $errorResponse = @{
                        jsonrpc = "2.0"
                        id = $null
                        error = @{
                            code = -32603
                            message = "Internal error"
                            data = $_.Exception.Message
                        }
                    }

                    $errorJson = $errorResponse | ConvertTo-Json -Depth 5 -Compress
                    $Server.OutputWriter.WriteLine($errorJson)
                    $Server.OutputWriter.Flush()
                }
                catch {

                }
            }
        }
    }
    catch {

        throw
    }
}

function Invoke-McpStdioMessage {
    [CmdletBinding()]
    param(
        [string]$InputLine
    )

    try {
        # Parse JSON-RPC request
        $jsonRequest = $InputLine | ConvertFrom-Json

        # Process the MCP request
        return Invoke-McpMessage -Request $jsonRequest
    }
    catch {


        return New-McpErrorResponse -Id $null -ErrorType "ParseError" -ErrorMessage $_.Exception.Message
    }
}

function Invoke-McpMessage {
    [CmdletBinding()]
    param(
        [PSCustomObject]$Request
    )
    
    $method = $Request.method
    $params = $Request.params
    $id = $Request.id
    

    
    switch ($method) {
        { $_ -eq (Get-McpMethod -Method "Initialize") } {
            return Handle-Initialize -Params $params -Id $id
        }
        { $_ -eq (Get-McpMethod -Method "ToolsList") } {
            return Handle-ToolsList -Id $id
        }
        { $_ -eq (Get-McpMethod -Method "ToolsCall") } {
            return Handle-ToolsCall -Params $params -Id $id
        }
        { $_ -eq (Get-McpNotification -Notification "Initialized") } {
            return Handle-Initialized
        }
        default {
            return New-McpErrorResponse -Id $id -ErrorType "MethodNotFound" -ErrorMessage "Unknown method: $method"
        }
    }
}

function Handle-Initialize {
    [CmdletBinding()]
    param(
        [PSCustomObject]$Params,
        [string]$Id
    )
    

    
    $result = @{
        protocolVersion = Get-McpProtocolVersion
        capabilities = Get-McpCapabilities
        serverInfo = Get-McpServerInfo
    }

    return New-McpResponse -Id $Id -Result $result
}

function Handle-Initialized {

    return $null  # No response needed for notifications
}

function Handle-ToolsList {
    [CmdletBinding()]
    param(
        [string]$Id
    )
    

    
    $tools = Get-RegisteredTools
    
    $result = @{
        tools = $tools
    }

    return New-McpResponse -Id $Id -Result $result
}

function Handle-ToolsCall {
    [CmdletBinding()]
    param(
        [PSCustomObject]$Params,
        [string]$Id
    )
    
    $toolName = $Params.name
    $arguments = $Params.arguments
    

    
    try {
        $result = Invoke-McpTool -Name $toolName -Arguments $arguments
        
        $toolResult = @{
            content = @(
                @{
                    type = "text"
                    text = $result
                }
            )
        }

        return New-McpResponse -Id $Id -Result $toolResult
    }
    catch {

        
        return New-McpErrorResponse -Id $Id -ErrorType "InternalError" -ErrorMessage "Tool execution failed: $($_.Exception.Message)"
    }
}

# Export functions
Export-ModuleMember -Function @(
    'New-McpServer',
    'Start-McpServer',
    'Invoke-McpMessage'
)

# Re-export functions from McpConstants for convenience
Export-ModuleMember -Function @(
    'Get-McpProtocolVersion',
    'Get-JsonRpcVersion',
    'Get-McpMethod',
    'Get-McpNotification',
    'Get-McpErrorCode',
    'Get-McpErrorMessage',
    'Get-McpServerInfo',
    'Get-McpClientInfo',
    'Get-McpCapabilities',
    'New-McpResponse',
    'New-McpErrorResponse'
)
