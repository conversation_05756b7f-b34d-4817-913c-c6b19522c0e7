<!-- filepath: d:\os\src\onecore\ds\ds\src\adai\proto\win32\aimx\htmlui\html\entraid-migration.html -->
<!--++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    entraid-migration.html

Abstract:

    This module implements the Entra ID Migration readiness assessment interface.
    Provides comprehensive analysis of on-premises AD environment including protocol
    compatibility, security policy assessment, and actionable migration guidance.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 05/23/2025

---->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Entra ID Migration Assessment</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/entraid-migration.css">
</head>
<body>
    <div class="migration-container">
        <header class="migration-header">
            <h1>Entra ID Migration Readiness Assessment</h1>
            <div class="assessment-controls">
                <div class="assessment-status" id="assessmentStatus">
                    <span class="status-indicator" id="statusIndicator"></span>
                    <span class="status-text" id="statusText">Analyzing Environment...</span>
                </div>
                <button id="refreshAssessment" class="control-btn refresh-btn">
                    <span class="icon">↻</span> Refresh Analysis
                </button>
                <button id="exportReport" class="control-btn primary-btn">
                    <span class="icon">📊</span> Export Report
                </button>
            </div>
        </header>

        <!-- Tab Navigation -->
        <div class="tab-navigation">
            <button class="tab-btn active" data-tab="overview">Readiness Overview</button>
            <button class="tab-btn" data-tab="protocols">Protocol Analysis</button>
            <button class="tab-btn" data-tab="security">Security Policies</button>
            <button class="tab-btn" data-tab="actionable">Actionable Items</button>
        </div>

        <!-- Overview Tab -->
        <div id="overview-tab" class="tab-content active">
            <div class="overview-grid">
                <!-- Overall Readiness Score -->
                <div class="overview-card readiness-score">
                    <h3>Overall Migration Readiness</h3>
                    <div class="score-display">
                        <div class="score-circle" id="readinessCircle">
                            <span class="score-value" id="readinessScore">--</span>
                            <span class="score-label">%</span>
                        </div>
                        <div class="score-status" id="scoreStatus">Calculating...</div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="overview-card quick-stats">
                    <h3>Assessment Summary</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value" id="totalUsers">--</div>
                            <div class="stat-label">User Accounts</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="totalGroups">--</div>
                            <div class="stat-label">Groups</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="criticalIssues">--</div>
                            <div class="stat-label">Critical Issues</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="warnings">--</div>
                            <div class="stat-label">Warnings</div>
                        </div>
                    </div>
                </div>

                <!-- Readiness Distribution Chart -->
                <div class="overview-card chart-card">
                    <h3>Readiness Distribution</h3>
                    <div class="chart-container">
                        <canvas id="readinessChart"></canvas>
                    </div>
                    <div class="chart-legend" id="readinessLegend"></div>
                </div>

                <!-- Category Breakdown -->
                <div class="overview-card category-breakdown">
                    <h3>Category Assessment</h3>
                    <div class="category-list" id="categoryList">
                        <!-- Dynamic content -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Protocol Analysis Tab -->
        <div id="protocols-tab" class="tab-content">
            <div class="protocols-grid">
                <!-- Protocol Compatibility Chart -->
                <div class="protocol-card chart-card">
                    <h3>Protocol Compatibility Analysis</h3>
                    <div class="chart-container">
                        <canvas id="protocolChart"></canvas>
                    </div>
                </div>

                <!-- Unsupported Protocols -->
                <div class="protocol-card">
                    <h3>Unsupported Protocols</h3>
                    <div class="protocol-list" id="unsupportedProtocols">
                        <!-- Dynamic content -->
                    </div>
                </div>

                <!-- Migration Recommendations -->
                <div class="protocol-card full-width">
                    <h3>Protocol Migration Recommendations</h3>
                    <div class="recommendations-list" id="protocolRecommendations">
                        <!-- Dynamic content -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Policies Tab -->
        <div id="security-tab" class="tab-content">
            <div class="security-grid">
                <!-- Security Compliance Chart -->
                <div class="security-card chart-card">
                    <h3>Security Policy Compliance</h3>
                    <div class="chart-container">
                        <canvas id="securityChart"></canvas>
                    </div>
                </div>

                <!-- Policy Issues -->
                <div class="security-card">
                    <h3>Policy Issues</h3>
                    <div class="policy-issues" id="policyIssues">
                        <!-- Dynamic content -->
                    </div>
                </div>

                <!-- Remediation Actions -->
                <div class="security-card full-width">
                    <h3>Security Remediation Plan</h3>
                    <div class="remediation-plan" id="remediationPlan">
                        <!-- Dynamic content -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Actionable Items Tab -->
        <div id="actionable-tab" class="tab-content">
            <div class="actionable-grid">
                <!-- Progress Tracker -->
                <div class="action-card progress-tracker">
                    <h3>Migration Progress</h3>
                    <div class="progress-display">
                        <div class="progress-bar-container">
                            <div class="progress-bar" id="migrationProgress"></div>
                        </div>
                        <div class="progress-stats">
                            <span id="completedTasks">0</span> of <span id="totalTasks">0</span> tasks completed
                        </div>
                    </div>
                </div>

                <!-- Priority Actions -->
                <div class="action-card">
                    <h3>High Priority Actions</h3>
                    <div class="priority-actions" id="priorityActions">
                        <!-- Dynamic content -->
                    </div>
                </div>

                <!-- Action Items List -->
                <div class="action-card full-width">
                    <h3>Detailed Action Items</h3>
                    <div class="actions-filter">
                        <button class="filter-btn active" data-filter="all">All</button>
                        <button class="filter-btn" data-filter="critical">Critical</button>
                        <button class="filter-btn" data-filter="high">High Priority</button>
                        <button class="filter-btn" data-filter="medium">Medium</button>
                        <button class="filter-btn" data-filter="completed">Completed</button>
                    </div>
                    <div class="action-items-list" id="actionItemsList">
                        <!-- Dynamic content -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Item Detail Modal -->
    <div id="actionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="actionTitle">Action Item Details</h2>
                <button class="close-button">&times;</button>
            </div>
            <div class="modal-body">
                <div id="actionDetails">
                    <!-- Dynamic content -->
                </div>
            </div>
            <div class="modal-footer">
                <button id="markCompleteBtn" class="primary-btn">Mark as Complete</button>
                <button id="generateScriptBtn" class="secondary-btn">Generate Script</button>
                <button class="cancel-btn">Close</button>
            </div>
        </div>
    </div>

    <script src="../js/entraid-migration.js"></script>
</body>
</html>