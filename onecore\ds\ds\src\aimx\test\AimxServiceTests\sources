TARGETNAME=AimxSrvTaef
TARGETTYPE=DY<PERSON><PERSON>K
TARGET_DESTINATION=retail

TEST_CODE=1

USE_MSVCRT=1
USE_UNICRT=1
USE_STL=1
STL_VER=STL_VER_CURRENT

C_DEFINES=$(C_DEFINES) -DWIN32 -D_WIN32 -DUNICODE -D_UNICODE

# Disable these effing warnings for string conversion issues and enable exception handling
MSC_WARNING_LEVEL=/W3 /EHsc /wd4244 /wd4267

PRECOMPILED_INCLUDE = pch.hxx
COMPILE_CXX = 1

# Enable C++ exception handling with proper unwind semantics
USE_NATIVE_EH=1

DLLENTRY=_DllMainCRTStartup

SOURCES=\
    AimxRpcTests.cpp \
    AimxProtocolTests.cpp \
    ProtocolValidationTests.cpp \
    SimpleProtocolTest.cpp \
    AimxServiceTests.cpp \

INCLUDES=\
    ..\..\prod\common; \
    ..\..\prod\aimxsrv\inc; \
    ..\..\prod\aimxsrv\server; \
    ..\..\prod\aimxsrv\client; \
    ..\..\prod\aimxsrv\dll; \
    ..\..\prod\common\nlohmann; \
    $(OBJ_PATH); \
    $(OBJ_PATH)\$(O); \
    $(OBJ_PATH)\..\..\prod\aimxsrv\idl\$(O); \
    $(BASE_INC_PATH); \
    $(PROJECT_ROOT)\ds\src\adai\proto\win32\aimxsrv\client; \
    $(PROJECT_ROOT)\ds\src\adai\proto\win32\aimxsrv\test; \
    $(INTERNAL_SDK_INC_PATH); \
    $(ONECORE_INTERNAL_SDK_INC_PATH); \
    $(ONECORE_INTERNAL_PRIV_SDK_INC_PATH_L); \
    $(ONECORESDKTOOLS_INTERNAL_INC_PATH_L)\wextest\cue; \

TARGETLIBS_PROJECT=\
    $(OBJ_PATH)\..\..\prod\aimxsrv\client\lib\$(O)\aimxclient_s.lib \

TARGETLIBS=\
    $(TARGETLIBS_PROJECT) \
    $(MINWIN_EXTERNAL_SDK_LIB_PATH_L)\ntdll.lib                            \
    $(MINWIN_EXTERNAL_SDK_LIB_PATH_L)\rpcrt4.lib                            \
    $(MINCORE_EXTERNAL_SDK_LIB_VPATH_L)\mincore.lib                        \
    $(ONECORESDKTOOLS_INTERNAL_LIB_PATH_L)\WexTest\Cue\Wex.Common.lib      \
    $(ONECORESDKTOOLS_INTERNAL_LIB_PATH_L)\WexTest\Cue\Wex.Logger.lib      \
    $(ONECORESDKTOOLS_INTERNAL_LIB_PATH_L)\WexTest\Cue\Te.Common.lib       \
    $(ONECORE_EXTERNAL_SDK_LIB_PATH)\secur32.lib                \
    $(ONECORE_EXTERNAL_SDK_LIB_PATH)\ntdsapi.lib                \
    $(ONECORE_EXTERNAL_SDK_LIB_PATH)\netapi32.lib               \

MUI_VERIFY_NO_LOC_RESOURCE=1
