/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    McpStdioClient.h

Abstract:

    Header file for the MCP Stdio Client component that handles JSON-RPC communication
    with MCP servers over stdin/stdout pipes. Provides tool discovery and execution
    capabilities following the Model Context Protocol specification.

Author:

    <PERSON><PERSON><PERSON> (r<PERSON><PERSON>) 07/12/2025

--*/

#pragma once

#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <atomic>
#include <map>
#include "AimxCommon.h"
#include "nlohmann/json.hpp"
#include "McpJsonRpc.h"
#include "McpSvrMgr.h"

// MCP Stdio Client for JSON-RPC communication over stdin/stdout
class McpStdioClient
{
public:
    // Constructor
    explicit McpStdioClient(const std::string& serverName);
    
    // Destructor
    ~McpStdioClient();

    // Delete copy constructor and assignment operator
    McpStdioClient(const McpStdioClient&) = delete;
    McpStdioClient& operator=(const McpStdioClient&) = delete;

    // Enhanced connection with environment and working directory support
    HRESULT ConnectPersistent(
        _In_ const std::string& command,
        _In_ const std::vector<std::string>& arguments,
        _In_ const std::map<std::string, std::string>& environment = {},
        _In_ const std::string& workingDirectory = ""
        );

    // List available tools from the server
    HRESULT ListTools(
        _Out_ std::vector<MCP_TOOL_INFO>& tools
        );

    // Execute a tool with timeout support
    HRESULT CallToolWithTimeout(
        _In_ const std::string& toolName,
        _In_ const nlohmann::json& parameters,
        _In_ DWORD timeoutMs,
        _Out_ nlohmann::json& result
        );

    // Disconnect from server
    void Disconnect();

    // Check if client is connected
    bool IsConnected() const { return m_isConnected.load(); }

    // Check if client is healthy
    bool IsHealthy() const { return m_isHealthy.load(); }

    // Get server name
    const std::string& GetServerName() const { return m_serverName; }

private:
    // JSON-RPC communication methods
    HRESULT SendJsonRpcRequest(
        _In_ const nlohmann::json& request,
        _Out_ nlohmann::json& response
        );

    HRESULT ReadJsonResponse(
        _Out_ nlohmann::json& response
        );

    // Enhanced JSON-RPC with timeout support
    HRESULT SendJsonRpcRequestWithTimeout(
        _In_ const nlohmann::json& request,
        _Out_ nlohmann::json& response,
        _In_ DWORD timeoutMs
        );

    HRESULT ReadJsonResponseWithTimeout(
        _Out_ nlohmann::json& response,
        _In_ DWORD timeoutMs
        );

    HRESULT WriteJsonRequest(
        _In_ const nlohmann::json& request
        );

    // Helper methods
    std::string BuildCommandLine(
        _In_ const std::string& command,
        _In_ const std::vector<std::string>& arguments
        );

    // Enhanced process creation with environment support
    HRESULT CreateServerProcess(
        _In_ const std::string& command,
        _In_ const std::vector<std::string>& arguments,
        _In_ const std::map<std::string, std::string>& environment,
        _In_ const std::string& workingDirectory
        );

    // Environment block creation
    std::string CreateEnvironmentBlock(
        _In_ const std::map<std::string, std::string>& environment
        );

    // MCP protocol initialization
    HRESULT InitializeMcpHandshake();

    // Health monitoring
    HRESULT PerformHealthCheck();
    void UpdateLastActivity();

    // Process and pipe handles
    HANDLE m_hChildStdInRd;
    HANDLE m_hChildStdInWr;
    HANDLE m_hChildStdOutRd;
    HANDLE m_hChildStdOutWr;
    HANDLE m_hChildProcess;
    HANDLE m_hChildThread;

    // Client state
    std::atomic<int> m_requestId;
    std::atomic<bool> m_isConnected;
    std::atomic<bool> m_isHealthy;
    std::string m_serverName;

    // Enhanced state tracking
    DWORD m_processId;
    FILETIME m_lastActivity;
    FILETIME m_connectionTime;
    std::map<std::string, std::string> m_environment;
    std::string m_workingDirectory;
    std::string m_command;
    std::vector<std::string> m_arguments;
};
