/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    chat.js

Abstract:

    This module implements the chat interface functionality for AIMX.
    Handles message display, streaming content updates, and user input processing,
    providing real-time interaction with the LLM backend through the WebView bridge.

Communication Protocol:
    
    Frontend to Backend:
        1. prompt: Send user query to the LLM
           Format: {type: "prompt", content: "string", useRag: boolean}
    
    Backend to Frontend:
        1. userMessage: Display a user message
           Format: {type: "userMessage", content: "string"}
        
        2. assistantMessage: Display a complete assistant response
           Format: {type: "assistantMessage", content: "string"}
        
        3. streamStart: Begin streaming a response
           Format: {type: "streamStart", sessionId: "string"}
        
        4. streamUpdate: Update a streaming message with new content
           Format: {type: "streamUpdate", sessionId: "string", content: "string"}
        
        5. streamEnd: Finalize a streaming message
           Format: {type: "streamEnd", sessionId: "string"}
        
        6. error: Display an error message
           Format: {type: "error", content: "string"}

Author:

    <PERSON><PERSON><PERSON> (rizhang) 03/29/2025

--*/

// Create a chat namespace within the aimx global object
window.aimx = window.aimx || {};
window.aimx.chat = (function() {
    // DOM elements used by the chat
    const elements = {
        promptInput: null,
        sendButton: null,
        messagesContainer: null,
        chatContainer: null
    };
    
    // Message tracking for streaming responses
    const messageTracker = {
        processedIds: [],
        messageContents: {},
        addMessage: function(id) {
            this.processedIds.push(id);
            this.messageContents[id] = ""; // Initialize with empty content
            // Keep track of last 50 messages only
            if (this.processedIds.length > 50) {
                const removedId = this.processedIds.shift();
                delete this.messageContents[removedId]; // Clean up content cache
            }
        },
        hasMessage: function(id) {
            return this.processedIds.includes(id);
        },
        updateContent: function(id, newChunk) {
            if (this.messageContents[id] !== undefined) {
                this.messageContents[id] += newChunk;
                return this.messageContents[id];
            }
            return newChunk;
        },
        getContent: function(id) {
            return this.messageContents[id] || "";
        }
    };
    
    // Initialize the chat module
    function initialize() {
        console.log("Initializing chat module");
        
        // Store DOM elements
        elements.promptInput = document.getElementById('promptInput');
        elements.sendButton = document.getElementById('sendButton');
        elements.messagesContainer = document.getElementById('messagesContainer');
        elements.chatContainer = document.getElementById('chatContainer');
        
        console.log("Chat container exists: " + !!elements.chatContainer);
        console.log("Messages container exists: " + !!elements.messagesContainer);
        
        // Set up event listeners
        setupEventListeners();
    }
    
    // Set up chat-specific event listeners
    function setupEventListeners() {
        // Auto-resize the input field
        if (elements.promptInput) {
            elements.promptInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });
            
            // Send message on Enter (but allow Shift+Enter for new lines)
            elements.promptInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendPrompt();
                }
            });
        }
        
        // Set up send button
        if (elements.sendButton) {
            elements.sendButton.addEventListener('click', sendPrompt);
        }
    }
    
    // Functions for adding and managing messages
    
    // Add a message to the chat
    function addMessage(content, type) {
        try {
            console.log(`Adding ${type} message`);
            
            const messageDiv = document.createElement('div');
            messageDiv.classList.add('message', `${type}-message`);
            
            const messageContent = document.createElement('div');
            messageContent.classList.add('message-content');
            
            // Check if the response contains error information or diagnostics
            if (type === 'bot' && (content.startsWith('Error:') || content.includes('Response structure:'))) {
                messageContent.classList.add('error-message');
                messageContent.innerHTML = formatErrorResponse(content);
            } else {
                messageContent.textContent = content;
            }
            
            messageDiv.appendChild(messageContent);
            elements.messagesContainer.appendChild(messageDiv);
            
            // Scroll to bottom
            elements.chatContainer.scrollTop = elements.chatContainer.scrollHeight;
            
            // Remove welcome message if it exists
            const welcomeMessage = document.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.style.display = 'none';
            }
            
            console.log("Message added successfully");
        } catch (e) {
            console.error(`Error adding message: ${e.message}`);
        }
    }
    
    // Format error responses for display
    function formatErrorResponse(content) {
        // Simple formatting for error messages
        return content
            .replace(/Error:/g, '<span class="error-label">Error:</span>')
            .replace(/\n/g, '<br>')
            .replace(/Response structure:/g, '<span class="diagnostics-label">Response structure:</span>')
            .replace(/Raw response:/g, '<span class="diagnostics-label">Raw response:</span>');
    }
    
    // Show typing indicator while waiting for response
    function showTypingIndicator() {
        try {
            console.log("Showing typing indicator");
            
            const indicatorDiv = document.createElement('div');
            indicatorDiv.classList.add('typing-indicator');
            indicatorDiv.id = 'typingIndicator';
            
            for (let i = 0; i < 3; i++) {
                const dot = document.createElement('div');
                dot.classList.add('typing-dot');
                indicatorDiv.appendChild(dot);
            }
            
            elements.messagesContainer.appendChild(indicatorDiv);
            elements.chatContainer.scrollTop = elements.chatContainer.scrollHeight;
        } catch (e) {
            console.error(`Error showing typing indicator: ${e.message}`);
        }
    }
    
    // Remove typing indicator
    function removeTypingIndicator() {
        try {
            console.log("Removing typing indicator");
            
            const indicator = document.getElementById('typingIndicator');
            if (indicator) {
                indicator.remove();
            }
        } catch (e) {
            console.error(`Error removing typing indicator: ${e.message}`);
        }
    }
    
    // Create a streaming message that will be updated incrementally
    function createStreamingMessage(messageId) {
        // Add tracking for this message ID
        if (messageTracker.hasMessage(messageId)) {
            console.warn(`Message ${messageId} already exists! Creating with modified ID.`);
            messageId = messageId + '-' + Date.now();
        }
        
        messageTracker.addMessage(messageId);
        removeTypingIndicator();
        
        // Count messages before creation for debugging
        const beforeCount = document.querySelectorAll('.message').length;
        
        const messageDiv = document.createElement('div');
        messageDiv.classList.add('message', 'bot-message');
        messageDiv.id = `message-${messageId}`;
        
        const messageContent = document.createElement('div');
        messageContent.classList.add('message-content', 'streaming-content');
        messageContent.id = `content-${messageId}`;
        
        // Add initial placeholder with cursor
        messageContent.innerHTML = '<span class="typing-cursor"></span>';
        
        messageDiv.appendChild(messageContent);
        elements.messagesContainer.appendChild(messageDiv);
        
        // Count messages after creation for debugging
        const afterCount = document.querySelectorAll('.message').length;
        console.log(`Message count before: ${beforeCount}, after: ${afterCount}`);
        
        // Scroll to bottom
        elements.chatContainer.scrollTop = elements.chatContainer.scrollHeight;
        
        // Remove welcome message if it exists
        const welcomeMessage = document.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.style.display = 'none';
        }
        
        console.log(`Created streaming message with ID: ${messageId}`);
    }
    
    // Update a streaming message with new content
    function updateStreamingMessage(messageId, chunk, fullText) {
        const contentElement = document.getElementById(`content-${messageId}`);
        if (contentElement) {
            try {
                // Make debug logging less verbose
                if (chunk && chunk.length > 0) {
                    console.log(`Updating content for ${messageId}, chunk length: ${chunk.length}`);
                }
                
                // Make sure we have valid text
                const safeText = fullText || "";
                
                // For debugging: log the content being processed
                if (safeText.length > 0 && safeText.length < 100) {
                    console.log(`Content being displayed: "${safeText}"`);
                }
                
                // Use simple string replacement instead of HTML method to ensure consistency
                let htmlContent = safeText
                    .replace(/&/g, "&amp;")
                    .replace(/</g, "&lt;")
                    .replace(/>/g, "&gt;")
                    .replace(/"/g, "&quot;")
                    .replace(/'/g, "&#039;")
                    .replace(/\n/g, "<br>");
                    
                contentElement.innerHTML = htmlContent + '<span class="typing-cursor"></span>';
                
                // Scroll to bottom
                elements.chatContainer.scrollTop = elements.chatContainer.scrollHeight;
            } catch (error) {
                console.error("Error in updateStreamingMessage:", error);
            }
        } else {
            console.error(`Could not find content element for message ID: content-${messageId}`);
            
            // Debug available elements
            const allContentElements = document.querySelectorAll('[id^="content-"]');
            console.log(`Found ${allContentElements.length} content elements:`);
            allContentElements.forEach(el => console.log(`  ${el.id}`));
        }
    }
    
    // Finalize a streaming message when complete
    function finalizeStreamingMessage(messageId) {
        const contentElement = document.getElementById(`content-${messageId}`);
        if (contentElement) {
            // Remove the typing cursor
            const cursor = contentElement.querySelector('.typing-cursor');
            if (cursor) {
                cursor.remove();
            }
            
            // Remove the streaming class
            contentElement.classList.remove('streaming-content');
        }
    }
    
    // Send a prompt to the host application
    function sendPrompt() {
        try {
            const prompt = elements.promptInput.value.trim();
            if (!prompt) return;
            
            console.log(`Sending prompt: ${prompt.substring(0, 20)}...`);
            
            // Add user message to UI
            addMessage(prompt, 'user');
            
            // Show typing indicator
            showTypingIndicator();
            
            // Reset input
            elements.promptInput.value = '';
            elements.promptInput.style.height = 'auto';
            
            // Return focus to the input field
            elements.promptInput.focus();
            
            // Check if RAG toggle is enabled
            const useRag = document.getElementById('ragToggle') && document.getElementById('ragToggle').checked;
            
            const ragToggle = document.getElementById('ragToggle');
            ragToggle.addEventListener('change', function () {
                if (this.checked) {
                    console.log("RAG is enabled");
                    sendToNative({
                        type: "ragToggleChecked",
                        action: "browse",
                        mode: "folder"
                    });
                }
            });

            // Prepare the message payload
            const message = {
                type: 'prompt',
                content: prompt,
                useRag: useRag // Include RAG toggle state
            };
            
            // Send to host application
            if (window.chrome && window.chrome.webview) {
                window.chrome.webview.postMessage(JSON.stringify(message));
                console.log("Message sent to host");
            } else {
                console.error("WebView API not available!");
            }
        } catch (error) {
            console.error("Error sending prompt:", error);
        }
    }
    
    // Public API
    return {
        initialize,
        addMessage,
        showTypingIndicator,
        removeTypingIndicator,
        createStreamingMessage,
        updateStreamingMessage,
        finalizeStreamingMessage,
        messageTracker
    };
})();

// Initialize the chat module on DOMContentLoaded
document.addEventListener('DOMContentLoaded', function() {
    window.aimx.chat.initialize();
});
