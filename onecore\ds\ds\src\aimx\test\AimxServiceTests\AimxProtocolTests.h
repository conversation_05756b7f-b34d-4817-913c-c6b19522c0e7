/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    AimxProtocolTests.h

Abstract:
    TAEF test class declaration for comprehensive AIMX RPC Server protocol testing.
    Tests the complete workflow including <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ner, and <PERSON><PERSON>.

--*/

#pragma once

#include "WexTestClass.h"

using namespace WEX::Logging;
using namespace WEX::Common;
using namespace WEX::TestExecution;

class AimxProtocolTests : public WEX::TestClass<AimxProtocolTests>
{
public:
    BEGIN_TEST_CLASS(AimxProtocolTests)
        TEST_CLASS_PROPERTY(L"TestClass", L"AIMX Protocol")
        TEST_CLASS_PROPERTY(L"Description", L"Comprehensive AIMX RPC Server protocol testing")
    END_TEST_CLASS()

    // Test complete chatbot query workflow (planning -> execution)
    BEGIN_TEST_METHOD(TestChatbotQueryWorkflow)
        TEST_METHOD_PROPERTY(L"Priority", L"1")
        TEST_METHOD_PROPERTY(L"Description", L"Tests complete chatbot query workflow with planning and execution")
    END_TEST_METHOD()

    // Test direct query in automated mode
    BEGIN_TEST_METHOD(TestDirectQueryAutomatedMode)
        TEST_METHOD_PROPERTY(L"Priority", L"1")
        TEST_METHOD_PROPERTY(L"Description", L"Tests direct PowerShell query execution in automated mode")
    END_TEST_METHOD()

    // Test direct query in interactive mode
    BEGIN_TEST_METHOD(TestDirectQueryInteractiveMode)
        TEST_METHOD_PROPERTY(L"Priority", L"1")
        TEST_METHOD_PROPERTY(L"Description", L"Tests direct PowerShell query planning in interactive mode")
    END_TEST_METHOD()

    // Test operation cancellation
    BEGIN_TEST_METHOD(TestOperationCancellation)
        TEST_METHOD_PROPERTY(L"Priority", L"2")
        TEST_METHOD_PROPERTY(L"Description", L"Tests cancellation of running operations")
    END_TEST_METHOD()

    // Test error handling scenarios
    BEGIN_TEST_METHOD(TestErrorHandling)
        TEST_METHOD_PROPERTY(L"Priority", L"2")
        TEST_METHOD_PROPERTY(L"Description", L"Tests various error conditions and response handling")
    END_TEST_METHOD()

    // Test multiple concurrent operations
    BEGIN_TEST_METHOD(TestMultipleOperations)
        TEST_METHOD_PROPERTY(L"Priority", L"3")
        TEST_METHOD_PROPERTY(L"Description", L"Tests handling of multiple concurrent operations")
    END_TEST_METHOD()
};