<!--++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    chat.html

Abstract:

    This module implements the main chat interface for AIMX.
    Provides the message display area, input controls, and UI elements
    for interacting with the LLM through a conversational interface.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 03/29/2025

---->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIMX Chat</title>
    <link rel="stylesheet" href="../css/styles.css">
</head>
<body>
    <div class="container">
        <div class="chat-container" id="chatContainer">
            <div class="welcome-message">
                <h1>Welcome to aimx</h1>
                <p>Your AI assistant powered by local LLM</p>
            </div>
            <div id="messagesContainer" class="messages-container"></div>
        </div>
        <div class="input-container">
            <div class="input-wrapper">
                <textarea id="promptInput" placeholder="Ask me anything..." rows="1"></textarea>
                <button id="sendButton" class="send-button">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M15.5 1.5L0.5 6.5L6.5 8.5L8.5 14.5L15.5 1.5Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
            <div class="input-controls">
                <label class="rag-toggle">
                    <input type="checkbox" id="ragToggle">
                    <span class="toggle-label">Use Knowledge Base</span>
                </label>
            </div>
            <div class="disclaimer" id="endpointDisclaimer">
                aimx is connecting to the LLM service...
            </div>
        </div>
    </div>
</body>
</html>
