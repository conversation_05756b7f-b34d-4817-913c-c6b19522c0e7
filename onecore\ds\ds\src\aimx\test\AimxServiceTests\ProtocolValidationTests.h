/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    ProtocolValidationTests.h

Abstract:
    TAEF test class for AIMX protocol validation and edge case testing.


--*/

#pragma once

#include "WexTestClass.h"
#include <thread>
#include <chrono>

using namespace WEX::Logging;
using namespace WEX::Common;
using namespace WEX::TestExecution;

class ProtocolValidationTests : public WEX::TestClass<ProtocolValidationTests>
{
public:
    BEGIN_TEST_CLASS(ProtocolValidationTests)
        TEST_CLASS_PROPERTY(L"TestClass", L"AIMX Protocol Validation")
        TEST_CLASS_PROPERTY(L"Description", L"Validates AIMX protocol compliance and edge cases")
    END_TEST_CLASS()

    // Test request validation and error handling
    BEGIN_TEST_METHOD(TestRequestValidation)
        TEST_METHOD_PROPERTY(L"Priority", L"1")
        TEST_METHOD_PROPERTY(L"Description", L"Tests request validation and various error conditions")
    END_TEST_METHOD()

    // Test response format compliance
    BEGIN_TEST_METHOD(TestResponseFormat)
        TEST_METHOD_PROPERTY(L"Priority", L"1")
        TEST_METHOD_PROPERTY(L"Description", L"Validates response format compliance with protocol specification")
    END_TEST_METHOD()

    // Test status transition logic
    BEGIN_TEST_METHOD(TestStatusTransitions)
        TEST_METHOD_PROPERTY(L"Priority", L"2")
        TEST_METHOD_PROPERTY(L"Description", L"Tests valid status transitions and state management")
    END_TEST_METHOD()

    // Test execution plan format validation
    BEGIN_TEST_METHOD(TestExecutionPlanFormat)
        TEST_METHOD_PROPERTY(L"Priority", L"2")
        TEST_METHOD_PROPERTY(L"Description", L"Validates execution plan JSON structure and content")
    END_TEST_METHOD()
};