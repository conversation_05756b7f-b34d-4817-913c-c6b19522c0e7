<#
.SYNOPSIS
    MCP Tool Registry Module
    
.DESCRIPTION
    Manages registration and execution of PowerShell-based tools for the MCP server.
    Provides a framework for registering tools with their metadata and execution logic.

.AUTHOR
    Rup<PERSON> Zhang (rizhang) 07-21-2025
#>

# Global tool registry
$script:RegisteredTools = @{}

function Register-McpTool {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$Name,
        
        [Parameter(Mandatory)]
        [string]$Description,
        
        [Parameter(Mandatory)]
        [scriptblock]$ScriptBlock,
        
        [hashtable]$InputSchema = @{},
        
        [string[]]$Tags = @()
    )
    

    
    $tool = @{
        name = $Name
        description = $Description
        inputSchema = $InputSchema
        tags = $Tags
        scriptBlock = $ScriptBlock
    }
    
    $script:RegisteredTools[$Name] = $tool
    

}

function Get-RegisteredTools {
    [CmdletBinding()]
    param()
    
    $tools = @()
    
    foreach ($toolName in $script:RegisteredTools.Keys) {
        $tool = $script:RegisteredTools[$toolName]
        
        $toolInfo = @{
            name = $tool.name
            description = $tool.description
            inputSchema = $tool.inputSchema
        }
        
        if ($tool.tags -and $tool.tags.Count -gt 0) {
            $toolInfo.tags = $tool.tags
        }
        
        $tools += $toolInfo
    }
    
    return $tools
}

function Invoke-McpTool {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$Name,

        [object]$Arguments = @{}
    )
    
    if (-not $script:RegisteredTools.ContainsKey($Name)) {
        throw "Tool not found: $Name"
    }
    
    $tool = $script:RegisteredTools[$Name]
    
    # Convert PSCustomObject to hashtable if needed
    if ($Arguments -is [PSCustomObject]) {
        $argHash = @{}
        $Arguments.PSObject.Properties | ForEach-Object {
            $argHash[$_.Name] = $_.Value
        }
        $Arguments = $argHash
    }



    try {
        # Execute the tool's script block with the provided arguments
        $result = & $tool.scriptBlock $Arguments
        
        # Convert result to string if it's not already
        if ($null -eq $result) {
            $result = "Tool executed successfully (no output)"
        }
        elseif ($result -is [string]) {
            # Already a string, use as-is
        }
        elseif ($result -is [array]) {
            $result = $result -join "`n"
        }
        else {
            $result = $result | Out-String
        }
        

        return $result
    }
    catch {

        throw "Tool execution failed: $($_.Exception.Message)"
    }
}

function Unregister-McpTool {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$Name
    )
    
    if ($script:RegisteredTools.ContainsKey($Name)) {
        $script:RegisteredTools.Remove($Name)

    }
    else {

    }
}

function Clear-McpTools {
    [CmdletBinding()]
    param()
    
    $script:RegisteredTools.Clear()
}

function Register-DefaultTools {
    <#
    .SYNOPSIS
        Placeholder for default tool registration
    .DESCRIPTION
        This function is kept for compatibility but does not register any tools.
        The MCP server is designed as a pure framework - tools should be registered
        by the consuming application as needed.
    #>
    [CmdletBinding()]
    param()

    # No default tools registered - this is a pure framework
    # Tools should be registered by the consuming application
}

# Export functions
Export-ModuleMember -Function @(
    'Register-McpTool',
    'Get-RegisteredTools',
    'Invoke-McpTool',
    'Unregister-McpTool',
    'Clear-McpTools',
    'Register-DefaultTools'
)
