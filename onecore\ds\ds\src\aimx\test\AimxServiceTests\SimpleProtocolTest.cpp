/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    SimpleProtocolTest.cpp

Abstract:
    Simple test script to demonstrate AIMX RPC Server functionality.
    This test validates the basic workflow and can be used as a reference.

--*/

#include "pch.hxx"
#include "aimxrpcclient.h"
#include "SimpleProtocolTest.h"
#include <nlohmann/json.hpp>
#include <iostream>
#include <thread>
#include <chrono>

void SimpleProtocolTest::RunBasicWorkflowTest()
{
    Log::Comment(L"Starting AIMX Protocol Basic Workflow Test");
    
    GUID contextId = {};
    HRESULT hr = S_OK;
    AimxRpcClient* rpcClient = new AimxRpcClient();
    
    VERIFY_IS_NOT_NULL(rpcClient, L"RPC client creation failed");

    try
    {
        // Step 1: Connect to server
        Log::Comment(L"Step 1: Connecting to AIMX RPC Server...");
        hr = rpcClient->AimxConnect(&contextId);
        VERIFY_SUCCEEDED(hr, L"AimxConnect should succeed");
        VERIFY_IS_FALSE(IsEqualGUID(contextId, GUID{}), L"ContextId should not be GUID_NULL after connect");
        Log::Comment(L"✓ Successfully connected to server");

        // Step 2: Send a simple chatbot query
        Log::Comment(L"Step 2: Sending chatbot query...");
        
        nlohmann::json chatbotRequest;
        chatbotRequest["requestType"] = 1; // AIMX_CHATBOT_QUERY
        chatbotRequest["query"] = "What is the current domain password policy?";
        chatbotRequest["executionMode"] = 2; // AIMX_MODE_INTERACTIVE

        std::string requestStr = chatbotRequest.dump(2); // Pretty print with 2 spaces
        std::wstring requestWStr(requestStr.begin(), requestStr.end());

        Log::Comment(L"Sending request:");
        Log::Comment(String().Format(L"%ws", requestWStr.c_str()));

        LPWSTR response = nullptr;
        hr = rpcClient->AimxProcessPrompt(contextId, requestWStr.c_str(), &response);
        VERIFY_SUCCEEDED(hr, L"Chatbot query should succeed");
        VERIFY_IS_NOT_NULL(response, L"Response should not be null");

        // Parse and validate response
        std::wstring responseWStr(response);
        std::string responseStr(responseWStr.begin(), responseWStr.end());
        
        Log::Comment(L"Received response:");
        Log::Comment(String().Format(L"%ws", response));

        VERIFY_IS_TRUE(nlohmann::json::accept(responseStr), L"Response should be valid JSON");
        
        nlohmann::json responseJson = nlohmann::json::parse(responseStr);
        VERIFY_IS_TRUE(responseJson.contains("success"), L"Response should contain success field");
        VERIFY_IS_TRUE(responseJson["success"].get<bool>(), L"Request should succeed");
        VERIFY_IS_TRUE(responseJson.contains("operationId"), L"Response should contain operation ID");

        std::string operationId = responseJson["operationId"].get<std::string>();
        Log::Comment(String().Format(L"✓ Operation started with ID: %hs", operationId.c_str()));

        if (response) MIDL_user_free(response);

        // Step 3: Poll for planning completion
        Log::Comment(L"Step 3: Polling for planning completion...");
        
        bool planningComplete = false;
        int pollCount = 0;
        const int maxPolls = 15; // 15 seconds timeout

        while (!planningComplete && pollCount < maxPolls)
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
            pollCount++;

            nlohmann::json statusRequest;
            statusRequest["requestType"] = 3; // AIMX_PLAN_STATUS
            statusRequest["operationId"] = operationId;

            std::string statusRequestStr = statusRequest.dump();
            std::wstring statusRequestWStr(statusRequestStr.begin(), statusRequestStr.end());

            response = nullptr;
            hr = rpcClient->AimxProcessPrompt(contextId, statusRequestWStr.c_str(), &response);
            
            if (SUCCEEDED(hr) && response)
            {
                std::wstring statusResponseWStr(response);
                std::string statusResponseStr(statusResponseWStr.begin(), statusResponseWStr.end());
                nlohmann::json statusResponseJson = nlohmann::json::parse(statusResponseStr);

                std::string currentStatus = statusResponseJson["status"].get<std::string>();
                Log::Comment(String().Format(L"Poll #%d - Status: %hs", pollCount, currentStatus.c_str()));

                if (currentStatus == "PLAN_READY")
                {
                    planningComplete = true;
                    Log::Comment(L"✓ Planning completed successfully!");
                    
                    if (statusResponseJson.contains("executionPlan"))
                    {
                        std::string planStr = statusResponseJson["executionPlan"].get<std::string>();
                        Log::Comment(L"Execution Plan:");
                        Log::Comment(String().Format(L"%hs", planStr.c_str()));
                    }
                }
                else if (currentStatus == "FAILED")
                {
                    Log::Comment(L"✗ Planning failed");
                    break;
                }

                MIDL_user_free(response);
            }
        }

        VERIFY_IS_TRUE(planningComplete, L"Planning should complete within timeout");

        // Step 4: Test direct query in automated mode
        Log::Comment(L"Step 4: Testing direct query in automated mode...");

        nlohmann::json directRequest;
        directRequest["requestType"] = 2; // AIMX_DIRECT_QUERY
        directRequest["command"] = "Get-ADDefaultDomainPasswordPolicy";
        directRequest["executionMode"] = 1; // AIMX_MODE_AUTOMATED

        std::string directRequestStr = directRequest.dump();
        std::wstring directRequestWStr(directRequestStr.begin(), directRequestStr.end());

        response = nullptr;
        hr = rpcClient->AimxProcessPrompt(contextId, directRequestWStr.c_str(), &response);
        VERIFY_SUCCEEDED(hr, L"Direct query should succeed");

        if (response)
        {
            std::wstring directResponseWStr(response);
            std::string directResponseStr(directResponseWStr.begin(), directResponseWStr.end());
            nlohmann::json directResponseJson = nlohmann::json::parse(directResponseStr);

            Log::Comment(L"Direct Query Response:");
            Log::Comment(String().Format(L"%ws", response));

            VERIFY_IS_TRUE(directResponseJson["success"].get<bool>(), L"Direct query should succeed");
            
            std::string directOperationId = directResponseJson["operationId"].get<std::string>();
            Log::Comment(String().Format(L"✓ Direct query started with ID: %hs", directOperationId.c_str()));

            MIDL_user_free(response);
        }

        // Step 5: Test error handling
        Log::Comment(L"Step 5: Testing error handling...");

        nlohmann::json invalidRequest;
        invalidRequest["requestType"] = 999; // Invalid request type

        std::string invalidRequestStr = invalidRequest.dump();
        std::wstring invalidRequestWStr(invalidRequestStr.begin(), invalidRequestStr.end());

        response = nullptr;
        hr = rpcClient->AimxProcessPrompt(contextId, invalidRequestWStr.c_str(), &response);
        
        // This should either fail at RPC level or return error response
        if (SUCCEEDED(hr) && response)
        {
            std::wstring errorResponseWStr(response);
            std::string errorResponseStr(errorResponseWStr.begin(), errorResponseWStr.end());
            
            if (nlohmann::json::accept(errorResponseStr))
            {
                nlohmann::json errorResponseJson = nlohmann::json::parse(errorResponseStr);
                
                if (errorResponseJson.contains("success") && !errorResponseJson["success"].get<bool>())
                {
                    Log::Comment(L"✓ Error handling working correctly");
                    Log::Comment(String().Format(L"Error message: %hs", 
                        errorResponseJson.value("errorMessage", "Unknown error").c_str()));
                }
            }
            
            MIDL_user_free(response);
        }
        else
        {
            Log::Comment(L"✓ Invalid request properly rejected at RPC level");
        }

        Log::Comment(L"✓ All basic workflow tests completed successfully!");
    }
    catch (const std::exception& e)
    {
        Log::Comment(String().Format(L"Exception during test: %hs", e.what()));
        VERIFY_FAIL(L"Test failed with exception");
    }

    // Cleanup
    if (rpcClient)
    {
        hr = rpcClient->AimxClose(contextId);
        VERIFY_SUCCEEDED(hr, L"AimxClose should succeed");
    }

    if (rpcClient)
    {
        delete rpcClient;
    }

    Log::Comment(L"AIMX Protocol Basic Workflow Test completed successfully!");
}

void SimpleProtocolTest::RunComponentValidationTest()
{
    Log::Comment(L"Starting AIMX Component Validation Test");
    
    GUID contextId = {};
    HRESULT hr = S_OK;
    AimxRpcClient* rpcClient = new AimxRpcClient();
    
    VERIFY_IS_NOT_NULL(rpcClient, L"RPC client creation failed");

    // Connect to server
    hr = rpcClient->AimxConnect(&contextId);
    VERIFY_SUCCEEDED(hr, L"AimxConnect should succeed");

    // Test 1: Validate RequestHandler
    Log::Comment(L"Test 1: Validating RequestHandler component...");
    
    struct RequestTest {
        std::string name;
        nlohmann::json request;
        bool expectSuccess;
    };

    std::vector<RequestTest> requestTests = {
        {
            "Valid chatbot query",
            nlohmann::json{{"requestType", 1}, {"query", "Test"}, {"executionMode", 2}},
            true
        },
        {
            "Valid direct query",
            nlohmann::json{{"requestType", 2}, {"command", "Test"}, {"executionMode", 1}},
            true
        },
        {
            "Invalid request type",
            nlohmann::json{{"requestType", 999}},
            false
        },
        {
            "Missing query field",
            nlohmann::json{{"requestType", 1}, {"executionMode", 2}},
            false
        }
    };

    for (const auto& test : requestTests)
    {
        Log::Comment(String().Format(L"Testing: %hs", test.name.c_str()));
        
        std::string requestStr = test.request.dump();
        std::wstring requestWStr(requestStr.begin(), requestStr.end());

        LPWSTR response = nullptr;
        hr = rpcClient->AimxProcessPrompt(contextId, requestWStr.c_str(), &response);

        bool testPassed = false;
        
        if (test.expectSuccess)
        {
            if (SUCCEEDED(hr) && response)
            {
                std::string responseStr(response, response + wcslen(response));
                if (nlohmann::json::accept(responseStr))
                {
                    nlohmann::json responseJson = nlohmann::json::parse(responseStr);
                    testPassed = responseJson.value("success", false);
                }
            }
        }
        else
        {
            // For failure cases, we expect either RPC failure or error response
            testPassed = FAILED(hr) || 
                        (response && !nlohmann::json::parse(std::string(response, response + wcslen(response))).value("success", true));
        }

        if (testPassed)
        {
            Log::Comment(L"  ✓ PASS");
        }
        else
        {
            Log::Comment(L"  ✗ FAIL");
        }

        if (response) MIDL_user_free(response);
    }

    // Test 2: Validate Planner component
    Log::Comment(L"Test 2: Validating Planner component...");
    
    nlohmann::json plannerTestRequest;
    plannerTestRequest["requestType"] = 1;
    plannerTestRequest["query"] = "Test planner with a complex query that requires planning";
    plannerTestRequest["executionMode"] = 2;

    std::string plannerRequestStr = plannerTestRequest.dump();
    std::wstring plannerRequestWStr(plannerRequestStr.begin(), plannerRequestStr.end());

    LPWSTR response = nullptr;
    hr = rpcClient->AimxProcessPrompt(contextId, plannerRequestWStr.c_str(), &response);
    
    if (SUCCEEDED(hr) && response)
    {
        std::string responseStr(response, response + wcslen(response));
        nlohmann::json responseJson = nlohmann::json::parse(responseStr);
        
        if (responseJson.value("success", false))
        {
            std::string operationId = responseJson["operationId"].get<std::string>();
            Log::Comment(L"  ✓ Planner started operation successfully");
            
            // Wait briefly and check if status changes
            std::this_thread::sleep_for(std::chrono::milliseconds(2000));
            
            nlohmann::json statusRequest;
            statusRequest["requestType"] = 3;
            statusRequest["operationId"] = operationId;

            std::string statusRequestStr = statusRequest.dump();
            std::wstring statusRequestWStr(statusRequestStr.begin(), statusRequestStr.end());

            MIDL_user_free(response);
            response = nullptr;
            
    hr = rpcClient->AimxProcessPrompt(contextId, statusRequestWStr.c_str(), &response);
            
            if (SUCCEEDED(hr) && response)
            {
                std::string statusResponseStr(response, response + wcslen(response));
                nlohmann::json statusResponseJson = nlohmann::json::parse(statusResponseStr);
                
                std::string status = statusResponseJson.value("status", "UNKNOWN");
                Log::Comment(String().Format(L"  ✓ Planner status: %hs", status.c_str()));
                
                if (status == "PLAN_READY" || status == "PLANNING")
                {
                    Log::Comment(L"  ✓ Planner component working correctly");
                }
            }
        }
        
        if (response) MIDL_user_free(response);
    }

    // Test 3: Validate Orchestrator component
    Log::Comment(L"Test 3: Validating Orchestrator component...");
    
    nlohmann::json orchestratorTestRequest;
    orchestratorTestRequest["requestType"] = 2;
    orchestratorTestRequest["command"] = "Test orchestrator execution";
    orchestratorTestRequest["executionMode"] = 1; // Automated mode for immediate execution

    std::string orchestratorRequestStr = orchestratorTestRequest.dump();
    std::wstring orchestratorRequestWStr(orchestratorRequestStr.begin(), orchestratorRequestStr.end());

    response = nullptr;
    hr = rpcClient->AimxProcessPrompt(contextId, orchestratorRequestWStr.c_str(), &response);
    
    if (SUCCEEDED(hr) && response)
    {
        std::string responseStr(response, response + wcslen(response));
        nlohmann::json responseJson = nlohmann::json::parse(responseStr);
        
        if (responseJson.value("success", false))
        {
            std::string status = responseJson.value("status", "UNKNOWN");
            Log::Comment(String().Format(L"  ✓ Orchestrator status: %hs", status.c_str()));
            
            if (status == "EXECUTING" || status == "COMPLETED")
            {
                Log::Comment(L"  ✓ Orchestrator component working correctly");
            }
        }
        
        MIDL_user_free(response);
    }

    // Cleanup
    hr = rpcClient->AimxClose(contextId);
    VERIFY_SUCCEEDED(hr, L"AimxClose should succeed");
    delete rpcClient;

    Log::Comment(L"AIMX Component Validation Test completed!");
}