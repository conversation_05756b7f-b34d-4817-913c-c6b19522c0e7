// filepath: d:\os\src\onecore\ds\ds\src\adai\proto\win32\aimx\htmlui\js\entraid-migration.js
/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    entraid-migration.js

Abstract:

    This module implements the Entra ID Migration assessment functionality.
    Handles analysis of AD environment readiness, displays interactive charts,
    and provides detailed migration guidance with actionable items.

Communication Protocol:
    
    Frontend to Backend:
        1. startAssessment: Begin migration readiness analysis
           Format: {type: "entraidMigration", action: "startAssessment"}
        
        2. getDetails: Request detailed information for specific area
           Format: {type: "entraidMigration", action: "getDetails", category: "string", item: "string"}
        
        3. markComplete: Mark an action item as completed
           Format: {type: "entraidMigration", action: "markComplete", itemId: "string"}
        
        4. generateScript: Request PowerShell script for remediation
           Format: {type: "entraidMigration", action: "generateScript", itemId: "string"}
    
    Backend to Frontend:
        1. assessmentData: Provide assessment results
           Format: {type: "entraidMigration", action: "assessmentData", data: {...}}
        
        2. detailData: Provide detailed drill-down information
           Format: {type: "entraidMigration", action: "detailData", details: {...}}
        
        3. scriptGenerated: Deliver generated PowerShell script
           Format: {type: "entraidMigration", action: "scriptGenerated", script: "string"}

Author:

    Rupo Zhang (rizhang) 05/23/2025

--*/

// Create namespace for Entra ID Migration
const EntraidMigration = (function() {
    // Private variables
    let charts = {};
    let assessmentData = {};
    let actionItems = [];
    let currentFilter = 'all';
    let isInitialized = false;

    // Chart colors matching the existing theme
    const chartColors = {
        ready: '#4CAF50',
        warning: '#FF9800', 
        critical: '#F44336',
        info: '#2196F3',
        success: '#8BC34A',
        error: '#E91E63'
    };

    // Initialize the migration assessment module
    function initialize() {
        if (isInitialized) return;
        
        console.log("Initializing Entra ID Migration Assessment");
        
        // Set up event listeners
        setupEventListeners();
        
        // Initialize charts
        initializeCharts();
        
        // Start initial assessment
        startAssessment();
        
        isInitialized = true;
    }

    // Set up all event listeners
    function setupEventListeners() {
        // Tab navigation
        const tabButtons = document.querySelectorAll('.tab-btn');
        tabButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                const tabId = this.dataset.tab;
                switchTab(tabId);
            });
        });

        // Refresh assessment
        const refreshBtn = document.getElementById('refreshAssessment');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', startAssessment);
        }

        // Export report
        const exportBtn = document.getElementById('exportReport');
        if (exportBtn) {
            exportBtn.addEventListener('click', exportReport);
        }

        // Action item filters
        const filterButtons = document.querySelectorAll('.filter-btn');
        filterButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                const filter = this.dataset.filter;
                setActionFilter(filter);
            });
        });

        // Modal close handlers
        const closeButtons = document.querySelectorAll('.close-button, .cancel-btn');
        closeButtons.forEach(btn => {
            btn.addEventListener('click', closeModal);
        });

        // Action modal buttons
        const markCompleteBtn = document.getElementById('markCompleteBtn');
        if (markCompleteBtn) {
            markCompleteBtn.addEventListener('click', markActionComplete);
        }

        const generateScriptBtn = document.getElementById('generateScriptBtn');
        if (generateScriptBtn) {
            generateScriptBtn.addEventListener('click', generateRemediationScript);
        }

        // Setup WebView message handler
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.addEventListener('message', handleNativeMessage);
        }
    }

    // Initialize charts with placeholder data
    function initializeCharts() {
        // Readiness distribution pie chart
        const readinessCtx = document.getElementById('readinessChart');
        if (readinessCtx) {
            charts.readiness = new Chart(readinessCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Ready', 'Minor Issues', 'Major Issues', 'Critical Issues'],
                    datasets: [{
                        data: [0, 0, 0, 0],
                        backgroundColor: [chartColors.ready, chartColors.warning, chartColors.error, chartColors.critical],
                        borderWidth: 2,
                        borderColor: '#2C2C2C'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: { color: '#FFFFFF', font: { size: 12 } }
                        }
                    },
                    onClick: (event, elements) => {
                        if (elements.length > 0) {
                            const index = elements[0].index;
                            const label = charts.readiness.data.labels[index];
                            drillDownReadiness(label);
                        }
                    }
                }
            });
        }

        // Protocol compatibility bar chart
        const protocolCtx = document.getElementById('protocolChart');
        if (protocolCtx) {
            charts.protocol = new Chart(protocolCtx, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Supported %',
                        data: [],
                        backgroundColor: chartColors.ready,
                        borderColor: chartColors.ready,
                        borderWidth: 1
                    }, {
                        label: 'Unsupported %',
                        data: [],
                        backgroundColor: chartColors.critical,
                        borderColor: chartColors.critical,
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    scales: {
                        x: {
                            stacked: true,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#AAAAAA' }
                        },
                        y: {
                            stacked: true,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#AAAAAA' }
                        }
                    },
                    plugins: {
                        legend: {
                            labels: { color: '#FFFFFF', font: { size: 12 } }
                        }
                    },
                    onClick: (event, elements) => {
                        if (elements.length > 0) {
                            const index = elements[0].index;
                            const protocol = charts.protocol.data.labels[index];
                            drillDownProtocol(protocol);
                        }
                    }
                }
            });
        }

        // Security compliance radar chart
        const securityCtx = document.getElementById('securityChart');
        if (securityCtx) {
            charts.security = new Chart(securityCtx, {
                type: 'radar',
                data: {
                    labels: ['Password Policies', 'Account Lockout', 'Group Policies', 'Audit Policies', 'Trust Relationships'],
                    datasets: [{
                        label: 'Compliance %',
                        data: [0, 0, 0, 0, 0],
                        backgroundColor: 'rgba(76, 175, 80, 0.2)',
                        borderColor: chartColors.ready,
                        borderWidth: 2,
                        pointBackgroundColor: chartColors.ready,
                        pointBorderColor: '#FFFFFF',
                        pointHoverBackgroundColor: '#FFFFFF',
                        pointHoverBorderColor: chartColors.ready
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            angleLines: { color: 'rgba(255, 255, 255, 0.1)' },
                            pointLabels: { color: '#FFFFFF', font: { size: 11 } },
                            ticks: { color: '#AAAAAA', backdropColor: 'transparent' }
                        }
                    },
                    plugins: {
                        legend: {
                            labels: { color: '#FFFFFF', font: { size: 12 } }
                        }
                    },
                    onClick: (event, elements) => {
                        if (elements.length > 0) {
                            const index = elements[0].index;
                            const policy = charts.security.data.labels[index];
                            drillDownSecurity(policy);
                        }
                    }
                }
            });
        }
    }

    // Switch between tabs
    function switchTab(tabId) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabId}-tab`).classList.add('active');

        // Trigger chart resize if needed
        setTimeout(() => {
            Object.values(charts).forEach(chart => {
                if (chart && chart.resize) chart.resize();
            });
        }, 100);
    }

    // Start assessment process
    function startAssessment() {
        console.log("Starting Entra ID migration assessment");
        
        // Update status
        updateAssessmentStatus('analyzing', 'Analyzing Environment...');
        
        // Clear existing data
        assessmentData = {};
        actionItems = [];
        
        // Request assessment from native backend
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'entraidMigration',
                action: 'startAssessment'
            });        } else {
            // No mock data - will wait for backend response
            console.log("Waiting for backend assessment data...");
        }
    }

    // Handle messages from native backend
    function handleNativeMessage(event) {
        let message;
        try {
            message = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;
        } catch (e) {
            console.error('Failed to parse message:', e);
            return;
        }

        if (message.type !== 'entraidMigration') return;

        switch (message.action) {
            case 'assessmentData':
                handleAssessmentData(message.data);
                break;
            case 'detailData':
                handleDetailData(message.details);
                break;
            case 'scriptGenerated':
                handleGeneratedScript(message.script);
                break;
            default:
                console.log('Unknown action:', message.action);
        }
    }

    // Handle assessment data from backend
    function handleAssessmentData(data) {
        assessmentData = data;
        console.log('Received assessment data:', data);

        // Update overall readiness score
        updateReadinessScore(data.overallScore || 75);
        
        // Update quick stats
        updateQuickStats(data.stats || {});
        
        // Update charts
        updateReadinessChart(data.readiness || {});
        updateProtocolChart(data.protocols || {});
        updateSecurityChart(data.security || {});
        
        // Update category breakdown
        updateCategoryBreakdown(data.categories || []);
        
        // Update protocol lists
        updateProtocolLists(data.protocols || {});
        
        // Update security policy issues
        updateSecurityIssues(data.security || {});
        
        // Update action items
        updateActionItems(data.actionItems || []);
        
        // Update status
        updateAssessmentStatus('complete', 'Analysis Complete');
    }    // Update assessment status indicator
    function updateAssessmentStatus(status, text) {
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        
        if (statusIndicator && statusText) {
            statusIndicator.className = `status-indicator ${status}`;
            statusText.textContent = text;
        }
    }

    // Update overall readiness score
    function updateReadinessScore(score) {
        const scoreElement = document.getElementById('readinessScore');
        const statusElement = document.getElementById('scoreStatus');
        
        if (scoreElement) {
            scoreElement.textContent = score;
        }
        
        if (statusElement) {
            let status = 'Needs Preparation';
            if (score >= 80) status = 'Ready for Migration';
            else if (score >= 60) status = 'Moderate Preparation Required';
            statusElement.textContent = status;
        }

        // Animate the circular progress
        const circle = document.getElementById('readinessCircle');
        if (circle) {
            circle.style.background = `conic-gradient(${chartColors.ready} ${score * 3.6}deg, #3C3C3C 0deg)`;
        }
    }

    // Update quick statistics
    function updateQuickStats(stats) {
        const elements = {
            totalUsers: document.getElementById('totalUsers'),
            totalGroups: document.getElementById('totalGroups'),
            criticalIssues: document.getElementById('criticalIssues'),
            warnings: document.getElementById('warnings')
        };

        Object.keys(elements).forEach(key => {
            if (elements[key] && stats[key] !== undefined) {
                elements[key].textContent = stats[key].toLocaleString();
            }
        });
    }

    // Update readiness distribution chart
    function updateReadinessChart(readiness) {
        if (charts.readiness) {
            charts.readiness.data.datasets[0].data = [
                readiness.ready || 0,
                readiness.minor || 0,
                readiness.major || 0,
                readiness.critical || 0
            ];
            charts.readiness.update();
        }
    }

    // Update protocol compatibility chart
    function updateProtocolChart(protocols) {
        if (charts.protocol) {
            const labels = Object.keys(protocols);
            const supportedData = labels.map(p => protocols[p].supported || 0);
            const unsupportedData = labels.map(p => protocols[p].unsupported || 0);

            charts.protocol.data.labels = labels;
            charts.protocol.data.datasets[0].data = supportedData;
            charts.protocol.data.datasets[1].data = unsupportedData;
            charts.protocol.update();
        }
    }

    // Update security compliance chart
    function updateSecurityChart(security) {
        if (charts.security) {
            charts.security.data.datasets[0].data = [
                security.passwordPolicies || 0,
                security.accountLockout || 0,
                security.groupPolicies || 0,
                security.auditPolicies || 0,
                security.trustRelationships || 0
            ];
            charts.security.update();
        }
    }

    // Update category breakdown
    function updateCategoryBreakdown(categories) {
        const categoryList = document.getElementById('categoryList');
        if (!categoryList) return;

        categoryList.innerHTML = '';
        
        categories.forEach(category => {
            const categoryItem = document.createElement('div');
            categoryItem.className = `category-item ${category.status}`;
            categoryItem.innerHTML = `
                <div class="category-info">
                    <div class="category-name">${category.name}</div>
                    <div class="category-score">${category.score}%</div>
                </div>
                <div class="category-details">
                    <span class="category-issues">${category.issues} issues</span>
                    <span class="category-status ${category.status}">${category.status}</span>
                </div>
            `;
            
            categoryItem.addEventListener('click', () => {
                drillDownCategory(category.name);
            });
            
            categoryList.appendChild(categoryItem);
        });
    }

    // Update protocol analysis lists
    function updateProtocolLists(protocols) {
        const unsupportedList = document.getElementById('unsupportedProtocols');
        const recommendationsList = document.getElementById('protocolRecommendations');
        
        if (unsupportedList) {
            unsupportedList.innerHTML = '';
            
            Object.keys(protocols).forEach(protocol => {
                if (protocols[protocol].unsupported > 0) {
                    const protocolItem = document.createElement('div');
                    protocolItem.className = 'protocol-item unsupported';
                    protocolItem.innerHTML = `
                        <div class="protocol-name">${protocol}</div>
                        <div class="protocol-impact">${protocols[protocol].unsupported}% unsupported</div>
                        <button class="view-details-btn" data-protocol="${protocol}">View Details</button>
                    `;
                    unsupportedList.appendChild(protocolItem);
                }
            });
        }
    }

    // Update security policy issues
    function updateSecurityIssues(security) {
        const policyIssues = document.getElementById('policyIssues');
        const remediationPlan = document.getElementById('remediationPlan');
        
        // Mock security issues data
        const issues = [
            { name: 'Password Complexity', severity: 'High', compliance: security.passwordPolicies || 0 },
            { name: 'Account Lockout', severity: 'Medium', compliance: security.accountLockout || 0 },
            { name: 'Group Policies', severity: 'Critical', compliance: security.groupPolicies || 0 }
        ];
        
        if (policyIssues) {
            policyIssues.innerHTML = '';
            
            issues.forEach(issue => {
                const issueItem = document.createElement('div');
                issueItem.className = `policy-issue ${issue.severity.toLowerCase()}`;
                issueItem.innerHTML = `
                    <div class="issue-name">${issue.name}</div>
                    <div class="issue-compliance">${issue.compliance}% compliant</div>
                    <div class="issue-severity ${issue.severity.toLowerCase()}">${issue.severity}</div>
                `;
                policyIssues.appendChild(issueItem);
            });
        }
    }

    // Update action items list
    function updateActionItems(items) {
        actionItems = items;
        renderActionItems();
        updateProgressTracker();
    }

    // Render action items based on current filter
    function renderActionItems() {
        const actionList = document.getElementById('actionItemsList');
        if (!actionList) return;

        const filteredItems = actionItems.filter(item => {
            if (currentFilter === 'all') return true;
            if (currentFilter === 'completed') return item.completed;
            return item.category === currentFilter;
        });

        actionList.innerHTML = '';
        
        filteredItems.forEach(item => {
            const actionItem = document.createElement('div');
            actionItem.className = `action-item ${item.category} ${item.completed ? 'completed' : ''}`;
            actionItem.innerHTML = `
                <div class="action-content">
                    <div class="action-header">
                        <div class="action-title">${item.title}</div>
                        <div class="action-category ${item.category}">${item.category.toUpperCase()}</div>
                    </div>
                    <div class="action-description">${item.description}</div>
                    <div class="action-details">
                        <span class="action-impact">${item.impact}</span>
                        <span class="action-effort">Effort: ${item.effort}</span>
                    </div>
                </div>
                <div class="action-controls">
                    <button class="view-action-btn" data-item-id="${item.id}">View Details</button>
                    ${!item.completed ? '<button class="complete-action-btn" data-item-id="' + item.id + '">Complete</button>' : '<span class="completed-badge">✓ Completed</span>'}
                </div>
            `;
            
            actionList.appendChild(actionItem);
        });

        // Add event listeners for action buttons
        actionList.querySelectorAll('.view-action-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const itemId = e.target.dataset.itemId;
                showActionDetails(itemId);
            });
        });

        actionList.querySelectorAll('.complete-action-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const itemId = e.target.dataset.itemId;
                markItemComplete(itemId);
            });
        });
    }

    // Update progress tracker
    function updateProgressTracker() {
        const completedTasks = actionItems.filter(item => item.completed).length;
        const totalTasks = actionItems.length;
        const progressPercent = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

        const progressBar = document.getElementById('migrationProgress');
        const completedElement = document.getElementById('completedTasks');
        const totalElement = document.getElementById('totalTasks');

        if (progressBar) {
            progressBar.style.width = `${progressPercent}%`;
        }
        
        if (completedElement) {
            completedElement.textContent = completedTasks;
        }
        
        if (totalElement) {
            totalElement.textContent = totalTasks;
        }
    }

    // Set action item filter
    function setActionFilter(filter) {
        currentFilter = filter;
        
        // Update filter buttons
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filter}"]`).classList.add('active');
        
        // Re-render action items
        renderActionItems();
    }

    // Show action item details in modal
    function showActionDetails(itemId) {
        const item = actionItems.find(a => a.id === itemId);
        if (!item) return;

        const modal = document.getElementById('actionModal');
        const title = document.getElementById('actionTitle');
        const details = document.getElementById('actionDetails');

        if (title) title.textContent = item.title;
        
        if (details) {
            details.innerHTML = `
                <div class="action-detail-section">
                    <h4>Description</h4>
                    <p>${item.description}</p>
                </div>
                <div class="action-detail-section">
                    <h4>Impact</h4>
                    <p>${item.impact}</p>
                </div>
                <div class="action-detail-section">
                    <h4>Effort Required</h4>
                    <p>${item.effort}</p>
                </div>
                <div class="action-detail-section">
                    <h4>Recommended Steps</h4>
                    <ol>
                        <li>Assess current ${item.title.toLowerCase()} implementation</li>
                        <li>Plan migration strategy</li>
                        <li>Test in development environment</li>
                        <li>Execute migration plan</li>
                        <li>Validate successful migration</li>
                    </ol>
                </div>
            `;
        }

        if (modal) {
            modal.classList.add('active');
            
            // Store current item ID for modal actions
            modal.dataset.currentItemId = itemId;
        }
    }

    // Mark action item as complete
    function markItemComplete(itemId) {
        const item = actionItems.find(a => a.id === itemId);
        if (!item) return;

        item.completed = true;
        
        // Update UI
        renderActionItems();
        updateProgressTracker();
        
        // Notify backend
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'entraidMigration',
                action: 'markComplete',
                itemId: itemId
            });
        }
    }

    // Mark action complete from modal
    function markActionComplete() {
        const modal = document.getElementById('actionModal');
        const itemId = modal?.dataset.currentItemId;
        
        if (itemId) {
            markItemComplete(itemId);
            closeModal();
        }
    }

    // Generate remediation script
    function generateRemediationScript() {
        const modal = document.getElementById('actionModal');
        const itemId = modal?.dataset.currentItemId;
        
        if (itemId && window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'entraidMigration',
                action: 'generateScript',
                itemId: itemId
            });
        }
    }

    // Handle generated script from backend
    function handleGeneratedScript(script) {
        // Create a new window or modal to show the script
        const scriptWindow = window.open('', '_blank', 'width=800,height=600');
        scriptWindow.document.write(`
            <html>
                <head><title>Remediation Script</title></head>
                <body style="font-family: monospace; padding: 20px; background: #1e1e1e; color: #ffffff;">
                    <h2>PowerShell Remediation Script</h2>
                    <pre style="background: #2d2d2d; padding: 15px; border-radius: 5px; overflow: auto;">${script}</pre>
                    <button onclick="navigator.clipboard.writeText(document.querySelector('pre').textContent)">Copy to Clipboard</button>
                </body>
            </html>
        `);
    }

    // Drill-down functions
    function drillDownReadiness(category) {
        console.log('Drilling down into readiness category:', category);
        // Request detailed data from backend or show filtered view
    }

    function drillDownProtocol(protocol) {
        console.log('Drilling down into protocol:', protocol);
        // Request detailed protocol analysis
    }

    function drillDownSecurity(policy) {
        console.log('Drilling down into security policy:', policy);
        // Request detailed security policy information
    }

    function drillDownCategory(category) {
        console.log('Drilling down into category:', category);
        // Request detailed category analysis
    }

    // Export assessment report
    function exportReport() {
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'entraidMigration',
                action: 'exportReport',
                data: assessmentData
            });
        }
    }

    // Close modal
    function closeModal() {
        const modal = document.getElementById('actionModal');
        if (modal) {
            modal.classList.remove('active');
        }
    }

    // Public API
    return {
        initialize,
        startAssessment,
        switchTab,
        setActionFilter,
        showActionDetails,
        markItemComplete
    };
})();

// Initialize when DOM is loaded or script is executed
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.migration-container')) {
        EntraidMigration.initialize();
    }
});

// Expose to window for external access
window.EntraidMigration = EntraidMigration;

// Auto-initialize if container already exists
if (document.readyState === 'complete' || document.readyState === 'interactive') {
    setTimeout(() => {
        if (document.querySelector('.migration-container')) {
            EntraidMigration.initialize();
        }
    }, 100);
}