/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    ADAIManager.cpp

Abstract:
    - This module implements the ADAIManager class, which is responsible for
    - initializing and managing the various components of the AIMX application.

Author:

    <PERSON><PERSON><PERSON><PERSON> (pumathur) 04/15/2025

--*/
#include "pch.h"
#include <stdlib.h>
#include <sstream>
#include <wrl.h>
#include <wrl/client.h>
#include <wil/com.h>
#include <urlmon.h>
#include <dwmapi.h>

#include <WebView2.h>
#include <WebView2EnvironmentOptions.h>

#include "WebViewMessageHandler.h"
#include "resource.h"

#include "ADAIManager.h"
#include "Debug.h"
#include <string>
#include <mutex>

static std::mutex instanceMutex;
// Initialize the static instance to nullptr
ADAIManager* ADAIManager::adaiManagerInstance = nullptr;

ADAIManager::ADAIManager() = default;

ADAIManager::~ADAIManager()
{
    Shutdown();
}

// Static method to get the singleton instance
ADAIManager* ADAIManager::GetInstance()
{
    std::lock_guard<std::mutex> lock(instanceMutex);
    if (adaiManagerInstance == nullptr) {
        adaiManagerInstance = new ADAIManager();
    }
    return adaiManagerInstance;
}

bool ADAIManager::InitializeComponents()
{
    try
    {
        LOGINFO("ADAIManager::InitializeComponents : Initializing LLM Service...");
        llmService = std::make_unique<LlmService>();

        LOGINFO("ADAIManager::InitializeComponents : Initializing ADAgent...");
        adAgent = std::make_unique<ADAgent>();

        LOGINFO("ADAIManager::InitializeComponents : Initializing Application Configuration...");
        AppConfig::getInstance().init();

        LOGINFO("ADAIManager::InitializeComponents : All components initialized successfully.");
        return true;
    } catch (const std::exception& e) {
        LOGERROR("ADAIManager::InitializeComponents : ADAIManager initialization failed: " + std::string(e.what()));
        return false;
    }
}

/*++

Routine Description:

    Initializes the RAG functionality for the application.

Arguments:

    None.

Return Value:

    bool indicating success or failure of the initialization.

--*/
bool ADAIManager::InitializeRAG()
{
    // Try to initialize the RAG database (non-critical, can continue if fails)
    try
    {
        auto llmService = GetLlmService();
        if (llmService != nullptr)
        {
            if(llmService == nullptr)
            {
                LOGERROR("ADAIManager::InitializeRAG : Failed to get LlmService instance.");
                return false;
            }
            if(AppConfig::getInstance().getDefaultRagDatabase().empty())
            {
                LOGINFO("ADAIManager::InitializeRAG : No default RAG database specified in the config file");
                return true;
            }
            bool ragInitialized = llmService->InitializeRagDatabase(AppConfig::getInstance().getDefaultRagDatabase());
            if (ragInitialized)
            {
                LOGINFO("ADAIManager::InitializeRAG : RAG database initialized successfully");
            }
            else
            {
                LOGINFO("ADAIManager::InitializeRAG : RAG database initialization failed, continuing without RAG functionality");
            }
            return ragInitialized;
        }
    }
    catch (const std::exception& e)
    {
        LOGERROR("ADAIManager::InitializeRAG : RAG initialization exception: " + std::string(e.what()));
    }
    
    return false;
}

bool ADAIManager::Initialize()
{
    LOGINFO("Initializing ADAIManager...");

    if (!InitializeComponents()) {
        LOGERROR("ADAIManager::Initialize : Failed to initialize components.");
        return false;
    }

    if (!InitializeRAG())
    {
        LOGERROR("ADAIManager::Initialize : Failed to initialize RAG.");
        return false;
    }

    LOGINFO("ADAIManager::Initialize : ADAIManager initialized successfully.");
    return true;
}

void ADAIManager::Shutdown()
{
    LOGINFO("ADAIManager::Shutdown : Shutting down ADAIManager...");
    CleanupComponents();
}

void ADAIManager::CleanupComponents()
{
    llmService.reset();
    adAgent.reset();
    LOGINFO("ADAIManager::CleanupComponents : All components cleaned up.");
}

LlmService* ADAIManager::GetLlmService() const
{
    return llmService.get();
}

ADAgent* ADAIManager::GetADAgent() const
{
    return adAgent.get();
}