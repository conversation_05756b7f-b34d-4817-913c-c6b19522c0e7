/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    Orchestrator.cpp

Abstract:
    Implementation of AIMX Orchestrator that executes planned operations by coordinating
    with components like MCP Servers and Data Collection Agents.

Author:

    <PERSON> (SNAKE FIGHTER) (lindakup) 06/03/2025

--*/

#include "pch.hxx"
#include "Orchestrator.h"
#include "StringUtils.h"
#include "AimxCommon.h"
#include "AimxConstants.h"
#include "McpToolManager.h"
#include "LLMInfer.h"
#include "ConversationManager.h"
#include "Orchestrator.cpp.tmh"
#include "AimxLlmConfig.h"

// Global map to track active execution operations (definition - declaration is in AimxCommon.h)
std::unordered_map<GUID, HANDLE, GuidHash, GuidEqual> g_ExecutionThreadMap;
std::mutex g_ExecutionThreadMapMutex;

std::unordered_map<GUID, ExecutionContext*, GuidHash, GuidEqual> g_ExecutionContextMap;
std::mutex g_ExecutionContextMapMutex;

HRESULT
Orchestrator::ExecuteOperationAsync(
    _In_ const GUID& operationId
    )
/*++

Routine Description:
    Starts asynchronous execution of an operation by creating a worker thread
    that will execute the planned steps.

Arguments:
    operationId - The unique identifier for the operation to execute

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    HRESULT hr = S_OK;
    HANDLE hThread = NULL;
    ExecutionContext* pContext = nullptr;
    std::shared_ptr<AIMX_OPERATION> operation;

    TraceInfo(AimxOrchestrator, "Entry - ExecuteOperationAsync for operation: %!GUID!", &operationId);

    // Find operation in global map
    {
        std::lock_guard<std::mutex> lock(g_OperationMapMutex);
        auto it = g_OperationMap.find(operationId);
        if (it == g_OperationMap.end())
        {
            TraceErr(AimxOrchestrator, "Operation not found: %!GUID!", &operationId);
            hr = E_INVALIDARG;
            goto Exit;
        }
        operation = it->second;
        TraceInfo(AimxOrchestrator, "Found operation with status: %d", operation->Status);
    }

    // Validate operation is ready for execution
    if (operation->Status != AIMX_STATUS_PLAN_READY && operation->Status != AIMX_STATUS_EXECUTING)
    {
        TraceErr(AimxOrchestrator, "Operation is not ready for execution. Status: %d", operation->Status);
        hr = E_INVALIDARG;
        goto Exit;
    }

    // Allocate context for worker thread
    pContext = new (std::nothrow) ExecutionContext();
    if (!pContext)
    {
        TraceErr(AimxOrchestrator, "Failed to allocate ExecutionContext");
        hr = E_OUTOFMEMORY;
        goto Exit;
    }

    pContext->OperationId = operationId;
    pContext->ExecutionPlan = operation->ExecutionPlan;

    // Create worker thread for execution
    hThread = CreateThread(
        NULL,                           // Default security attributes
        0,                              // Default stack size
        ExecutionWorkerThread,          // Thread function
        pContext,                       // Thread parameter
        0,                              // Default creation flags
        NULL                            // Don't need thread ID
        );

    if (!hThread)
    {
        DWORD dwError = GetLastError();
        hr = HRESULT_FROM_WIN32(dwError);
        TraceErr(AimxOrchestrator, "CreateThread failed: %!WINERROR!", dwError);
        goto Exit;
    }

    // Store thread handle for potential cancellation
    {
        std::lock_guard<std::mutex> lock(g_ExecutionThreadMapMutex);
        g_ExecutionThreadMap[operationId] = hThread;
    }

    // Update operation status
    operation->Status = AIMX_STATUS_EXECUTING;
    TraceInfo(AimxOrchestrator, "Successfully created execution worker thread, operation status set to EXECUTING");

    pContext = nullptr; // Thread owns the context now
    hThread = NULL;     // Thread map owns the handle now

Exit:
    if (pContext)
    {
        delete pContext;
    }
    if (hThread)
    {
        CloseHandle(hThread);
    }

    TraceInfo(AimxOrchestrator, "Exit - ExecuteOperationAsync. hr: %!HRESULT!", hr);
    return hr;
}


HRESULT
Orchestrator::CancelOperation(
    _In_ const GUID& operationId
    )
/*++

Routine Description:
    Cancels a running operation by signaling the worker thread to exit gracefully.

Arguments:
    operationId - The unique identifier for the operation to cancel

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    HRESULT hr = S_OK;
    HANDLE hThread = NULL;
    std::shared_ptr<AIMX_OPERATION> operation;
    ExecutionContext* pContext = nullptr;

    TraceInfo(AimxOrchestrator, "Entry");

    // Set cancelRequested flag in ExecutionContext
    {
        std::lock_guard<std::mutex> lock(g_ExecutionContextMapMutex);
        auto it = g_ExecutionContextMap.find(operationId);
        if (it != g_ExecutionContextMap.end())
        {
            pContext = it->second;
            if (pContext) {
                pContext->cancelRequested = true;
            }
        }
    }
    // Find and remove thread handle from map
    {
        std::lock_guard<std::mutex> lock(g_ExecutionThreadMapMutex);
        auto it = g_ExecutionThreadMap.find(operationId);
        if (it != g_ExecutionThreadMap.end())
        {
            hThread = it->second;
            g_ExecutionThreadMap.erase(it);
        }
    }

    // Wait for the execution thread to exit
    if (hThread)
    {
        WaitForSingleObject(hThread, INFINITE);
        CloseHandle(hThread);
    }

    // Update operation status
    {
        std::lock_guard<std::mutex> lock(g_OperationMapMutex);
        auto it = g_OperationMap.find(operationId);
        if (it != g_OperationMap.end())
        {
            operation = it->second;
            operation->Status = AIMX_STATUS_CANCELLED;
            GetSystemTimeAsFileTime(&operation->CompletionTime);
        }
    }

    TraceInfo(AimxOrchestrator, "Exit - CancelOperation. hr: %!HRESULT!", hr);
    return hr;
}

DWORD WINAPI
Orchestrator::ExecutionWorkerThread(
    _In_ LPVOID lpParameter
    )
/*++

Routine Description:
    Worker thread function that performs the actual execution work asynchronously.

Arguments:
    lpParameter - Pointer to ExecutionContext structure

Return Value:
    0 on success, or an error code on failure.

--*/
{
    HRESULT hr = S_OK;    ExecutionContext* pContext = static_cast<ExecutionContext*>(lpParameter);
    std::shared_ptr<AIMX_OPERATION> operation;
    nlohmann::json planJson;
    std::wstring finalResult;

    TraceInfo(AimxOrchestrator, "Entry - ExecutionWorkerThread for operation: %!GUID!", &pContext->OperationId);

    if (!pContext)
    {
        TraceErr(AimxOrchestrator, "Invalid context parameter");
        return ERROR_INVALID_PARAMETER;
    }

    // Send execution start notification
    ConversationSessionManager::NotifyOperationProgress(
        pContext->OperationId,
        L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_EXECUTION_START) + L"] Starting execution of planned steps"
    );

    // Find operation in global map
    {
        std::lock_guard<std::mutex> lock(g_OperationMapMutex);
        auto it = g_OperationMap.find(pContext->OperationId);
        if (it == g_OperationMap.end())
        {
            TraceErr(AimxOrchestrator, "Operation not found in execution worker thread");
            delete pContext;
            return ERROR_NOT_FOUND;
        }
        operation = it->second;
    }    try
    {
        // Use the execution plan JSON object directly
        if (pContext->ExecutionPlan.is_null())
        {
            TraceErr(AimxOrchestrator, "Execution plan is null");
            operation->Status = AIMX_STATUS_FAILED;
            hr = E_INVALIDARG;
            goto Exit;
        }
        nlohmann::json& planJson = pContext->ExecutionPlan;

        // Validate plan structure
        if (!planJson.contains(AimxConstants::JsonFields::AIMX_STEPS) || !planJson[AimxConstants::JsonFields::AIMX_STEPS].is_array())
        {
            TraceErr(AimxOrchestrator, "Invalid execution plan structure");
            operation->Status = AIMX_STATUS_FAILED;
            hr = E_INVALIDARG;
            goto Exit;
        }

        // Send initial execution progress
        ConversationSessionManager::NotifyOperationProgress(
            pContext->OperationId,
            L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_EXECUTION_START) + L"] " + std::wstring(AimxConstants::Messages::AIMX_MSG_STARTING_TOOL_EXECUTION)
        );

        // Execute each step in the plan
        const nlohmann::json& steps = planJson[AimxConstants::JsonFields::AIMX_STEPS];

        // somewhere to store combined results
        std::wstring combinedResults;
        std::wstring toolResults;

        for (size_t i = 0; i < steps.size(); ++i)
        {
            // Send step progress update
            std::wstring stepMessage = L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_EXECUTION_STEP) + L"] " +
                                      AimxConstants::PromptFormatting::AIMX_EXECUTING_STEP_PREFIX + std::to_wstring(i + 1) +
                                      AimxConstants::PromptFormatting::AIMX_OF_SEPARATOR + std::to_wstring(steps.size());
            ConversationSessionManager::NotifyOperationProgress(
                pContext->OperationId,
                stepMessage
            );

            // Check if operation was cancelled
            if (pContext->cancelRequested || operation->Status == AIMX_STATUS_CANCELLED)
            {
                TraceInfo(AimxOrchestrator, "Execution operation was cancelled");
                goto Exit;
            }
            const nlohmann::json& step = steps[i];
            if (!step.contains(AimxConstants::JsonFields::AIMX_STEP_ID) || !step[AimxConstants::JsonFields::AIMX_STEP_ID].is_number_integer())
            {
                TraceErr(AimxOrchestrator, "Invalid step structure - missing stepId");
                operation->Status = AIMX_STATUS_FAILED;
                hr = E_INVALIDARG;
                goto Exit;
            }

            int stepId = step[AimxConstants::JsonFields::AIMX_STEP_ID].get<int>();
            std::string action = step.value(AimxConstants::JsonFields::AIMX_ACTION, "");

            TraceInfo(AimxOrchestrator, "Executing step %d with action: %s", stepId, action.c_str());

            // Check if this is a GenerateResponse step - skip it since we handle response generation at the end
            if (action == ToString(AimxAction::GenerateResponse))
            {
                continue; // Skip execution, handle at the end
            }

            // Convert step JSON back to string for processing
            std::string stepJsonStr = step.dump();

            // Convert to wide string
            std::wstring stepJsonWStr = Utf8ToWide(stepJsonStr);

            // Execute the step
            std::wstring result;
            result.clear();
            hr = ExecuteStep(pContext->OperationId, stepId, stepJsonWStr, result);
            if (FAILED(hr))
            {
                TraceErr(AimxOrchestrator, "ExecuteStep failed for step %d: %!HRESULT!", stepId, hr);
                operation->Status = AIMX_STATUS_FAILED;
                ConversationSessionManager::NotifyOperationFailed(
                    pContext->OperationId,
                    L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_EXECUTION_TOOL_RUN)
                    + L"] " + std::wstring(AimxConstants::Messages::AIMX_MSG_TOOL_EXECUTION_FAILED)
                );
                goto Exit;
            }

            if (!result.empty())
            {
                toolResults += result + L"\n";
            }
            combinedResults += result;
        }

        // Generate final response using simplified fallback method
        std::wstring answerSummary;

        // Send progress notification
        ConversationSessionManager::NotifyOperationProgress(
            pContext->OperationId,
            L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_PROCESSING) + L"] Generating final response using AI assistant"
        );

        // Always use the simpler fallback method
        hr = GenerateLLMAnswerFromContext(operation->OriginalQuery, combinedResults, answerSummary);
        if (SUCCEEDED(hr))
        {
            ConversationSessionManager::NotifyOperationProgress(
                pContext->OperationId,
                L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_LLM_RESPONSE) + L"] AI response generated successfully"
            );
        }
        else
        {
            TraceErr(AimxOrchestrator, "GenerateLLMAnswerFromContext failed: %!HRESULT!", hr);
            answerSummary = combinedResults;
        }

        // Populate finalResult JSON with both combinedResults and answerSummary
        finalResult = AimxConstants::PromptFormatting::AIMX_JSON_OPEN_BRACE;
        finalResult += L"  \"" + Utf8ToWide(AimxConstants::JsonFields::AIMX_JSON_KEY_EXECUTION_STATUS) + L"\": \"" + Utf8ToWide(AimxConstants::JsonFields::AIMX_JSON_VALUE_COMPLETED) + L"\",\n";
        finalResult += L"  \"" + Utf8ToWide(AimxConstants::JsonFields::AIMX_JSON_KEY_STEPS_EXECUTED) + L"\": " + std::to_wstring(steps.size()) + L",\n";
        finalResult += L"  \"" + Utf8ToWide(AimxConstants::JsonFields::AIMX_JSON_KEY_EXECUTION_TIME) + L"\": \"" + std::to_wstring(steps.size() * 2) + AimxConstants::PromptFormatting::AIMX_SECONDS_SUFFIX + L"\",\n";
        finalResult += L"  \"" + Utf8ToWide(AimxConstants::JsonFields::AIMX_JSON_KEY_RESULT_DATA) + L"\": {\n";
        finalResult += L"    \"" + Utf8ToWide(AimxConstants::JsonFields::AIMX_JSON_KEY_COMBINED_RESULTS) + L"\": \"" + answerSummary + L"\"\n";
        finalResult += L"  },\n";
        finalResult += L"  \"" + Utf8ToWide(AimxConstants::JsonFields::AIMX_MESSAGE) + L"\": \"" + AimxConstants::Messages::AIMX_MSG_OPERATION_COMPLETED + L"\"\n";
        finalResult += AimxConstants::PromptFormatting::AIMX_JSON_CLOSE_BRACE;

        // Update operation with final result
        operation->Result = finalResult;
        operation->Status = AIMX_STATUS_COMPLETED;
        GetSystemTimeAsFileTime(&operation->CompletionTime);

        // Send completion notification
        std::wstring finalResult = answerSummary.empty() ? combinedResults : answerSummary;
        TraceInfo(AimxOrchestrator, "Sending completion notification with result: %ws", finalResult.c_str());
        ConversationSessionManager::NotifyOperationCompleted(
            pContext->OperationId,
            L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_EXECUTION_COMPLETED) + L"] " + finalResult
        );

        TraceInfo(AimxOrchestrator, "Execution completed successfully");
    }
    catch (...)
    {
        TraceErr(AimxOrchestrator, "Exception in execution worker thread");
        operation->Status = AIMX_STATUS_FAILED;
        ConversationSessionManager::NotifyOperationFailed(
            pContext->OperationId,
            L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_EXECUTION_START) + L"] " + std::wstring(AimxConstants::Messages::AIMX_MSG_EXECUTION_FAILED_UNEXPECTED)
        );
        hr = E_FAIL;
    }

Exit:
    // Remove thread handle from map
    {
        std::lock_guard<std::mutex> lock(g_ExecutionThreadMapMutex);
        g_ExecutionThreadMap.erase(pContext->OperationId);
    }

    delete pContext;
    TraceInfo(AimxOrchestrator, "Exit - ExecutionWorkerThread. hr: %!HRESULT!", hr);
    return SUCCEEDED(hr) ? 0 : hr;
}

HRESULT
Orchestrator::CallLLMProvider(
    _In_ const std::wstring& query,
    _Out_ std::wstring& response
)
{
    // Simulate a call to the LLM provider
    // SNAKE_TODO: NOT YET IMPLEMENTED. We unlikely support direct LLM inference.
    // This would be a placeholder for action to take when there are no tools
    // available from the MCP. We may want to call an LLM provider here. Or
    // we might want to scope to supported MCP server calls only. TBD.
    TraceInfo(AimxOrchestrator, L"Calling LLM Provider with query: %ls", query.c_str());

    // For now, just echo the query back as the response
    response = AimxConstants::PromptFormatting::AIMX_LLM_RESPONSE_PREFIX + query;
    return S_OK;
}

HRESULT
Orchestrator::ExecuteStep(
    _In_ const GUID& operationId,
    _In_ int stepId,
    _In_ const std::wstring& stepJson,
    _Out_ std::wstring& result
)
/*++

Routine Description:
    Executes a single step from the execution plan.

Arguments:
    operationId - The unique identifier for the operation
    stepId      - The step identifier
    stepJson    - The step definition in JSON format

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    HRESULT hr = S_OK;
    nlohmann::json stepValue;
    std::string stepUtf8;
    std::string action;
    std::wstring parametersWStr = L"";
    result.clear();

    TraceInfo(AimxOrchestrator, "Entry - ExecuteStep for step %d and OperationId %!GUID!", stepId, &operationId);

    // Convert step JSON to UTF-8
    stepUtf8 = WideToUtf8(stepJson);

    // Parse step JSON
    if (!nlohmann::json::accept(stepUtf8))
    {
        TraceErr(AimxOrchestrator, "Failed to parse step JSON");
        hr = E_INVALIDARG;
        goto Exit;
    }
    
    stepValue = nlohmann::json::parse(stepUtf8);

    // Validate step has required fields
    if (!stepValue.contains(AimxConstants::JsonFields::AIMX_ACTION) || !stepValue[AimxConstants::JsonFields::AIMX_ACTION].is_string())
    {
        TraceErr(AimxOrchestrator, "Step missing action field");
        hr = E_INVALIDARG;
        goto Exit;
    }


    action = stepValue[AimxConstants::JsonFields::AIMX_ACTION].get<std::string>();
    TraceInfo(AimxOrchestrator, "Executing step action: %s", action.c_str());

    // Figure out the Action
    AimxAction actionType;
    if (action == ToString(AimxAction::ProcessNaturalLanguage)) {
        actionType = AimxAction::ProcessNaturalLanguage;
    } else if (action == ToString(AimxAction::ExecuteTool)) {
        actionType = AimxAction::ExecuteTool;
    } else if (action == ToString(AimxAction::GenerateResponse)) {
        actionType = AimxAction::GenerateResponse;
    } else {
        actionType = static_cast<AimxAction>(-1);
        TraceErr(AimxOrchestrator, "Unknown action type: %s", action.c_str());
        hr = E_INVALIDARG;
        goto Exit;
    }

    switch (actionType) {
        case AimxAction::ProcessNaturalLanguage: {
            std::string query = stepValue.value(AimxConstants::JsonFields::AIMX_JSON_KEY_QUERY, "");
            std::wstring queryWStr = Utf8ToWide(query);
            std::wstring llmResponse;
            hr = CallLLMProvider(queryWStr, llmResponse);
            result = llmResponse;
            break;
        }
        case AimxAction::ExecuteTool: {
            // Get target server name and tool information
            std::string target = stepValue.value(AimxConstants::JsonFields::AIMX_TARGET, "");
            std::string toolNameStr = stepValue.value(AimxConstants::JsonFields::AIMX_TOOL_NAME, "");

            if (target.empty() || toolNameStr.empty())
            {
                TraceErr(AimxOrchestrator, "Missing target or tool name for execute_tool action");
                hr = E_INVALIDARG;
                goto Exit;
            }

            std::wstring serverName = Utf8ToWide(target);
            std::wstring toolName = Utf8ToWide(toolNameStr);

            // Get parameters if present
            nlohmann::json parameters = nlohmann::json::object();
            if (stepValue.contains(AimxConstants::JsonFields::AIMX_PARAMETERS) &&
                stepValue[AimxConstants::JsonFields::AIMX_PARAMETERS].is_object())
            {
                parameters = stepValue[AimxConstants::JsonFields::AIMX_PARAMETERS];
            }

            // Execute tool using McpToolManager
            TraceInfo(AimxOrchestrator, "Executing tool '%ws' on server '%ws'", toolName.c_str(), serverName.c_str());

            // Send tool execution start notification
            ConversationSessionManager::NotifyOperationProgress(
                operationId,
                L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_EXECUTION_TOOL_RUN) + L"] Executing tool: " + toolName + L" on server: " + serverName
            );

            MCP_TOOL_EXECUTION_RESULT toolResult = {};
            hr = McpToolManager::ExecuteTool(serverName, toolName, parameters, toolResult);
            if (FAILED(hr))
            {
                TraceErr(AimxOrchestrator, "Tool execution failed: %!HRESULT!", hr);
                ConversationSessionManager::NotifyOperationProgress(
                    operationId,
                    L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_EXECUTION_TOOL_RUN) + L"] Tool execution failed: " + toolName
                );
                goto Exit;
            }
            TraceInfo(AimxOrchestrator, "Tool execution completed with status: %d", static_cast<int>(toolResult.status));

            // Send tool execution result notification with full result
            std::wstring resultSummary = L"Tool '" + toolName + L"' completed successfully";
            if (!toolResult.result.empty())
            {
                // Include the complete result - let frontend handle formatting
                std::wstring resultContent = Utf8ToWide(toolResult.result.dump());
                resultSummary += L" (result: " + resultContent + L")";
            }
            ConversationSessionManager::NotifyOperationProgress(
                operationId,
                L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_EXECUTION_TOOL_RESULT) + L"] " + resultSummary
            );

            // Convert tool result to string
            if (toolResult.status == MCP_TOOL_EXECUTION_STATUS::COMPLETED)
            {
                // Convert JSON result to string
                if (toolResult.result.is_string())
                {
                    result = Utf8ToWide(toolResult.result.get<std::string>());
                }
                else
                {
                    result = Utf8ToWide(toolResult.result.dump());
                }
            }
            else
            {
                result = AimxConstants::PromptFormatting::AIMX_TOOL_EXECUTION_FAILED_PREFIX + toolResult.errorMessage;
                hr = E_FAIL;
            }
            break;
        }
        case AimxAction::GenerateResponse: {
            // This action should be handled by the main execution loop
            // after collecting all tool results
            TraceInfo(AimxOrchestrator, "GenerateResponse action - will be handled by main loop");
            result = L""; // Empty result for this step
            break;
        }
        default:
            TraceErr(AimxOrchestrator, "Unknown step action: %s", action.c_str());
            hr = E_INVALIDARG;
            goto Exit;
        }
    if (FAILED(hr))
    {
        TraceErr(AimxOrchestrator, "Failed to execute step action: %!HRESULT!", hr);
        goto Exit;
    }

Exit:
    TraceInfo(AimxOrchestrator, "Exit - ExecuteStep for step %d. hr: %!HRESULT!", stepId, hr);
    return hr;
}

HRESULT
Orchestrator::AggregateResults(
    _In_ const std::wstring& rawResults,
    _Out_ std::wstring& aggregatedResults
    )
/*++

Routine Description:
    Aggregates results from multiple data sources into a final response format.

Arguments:
    rawResults        - The raw results from data collection
    aggregatedResults - Receives the aggregated and formatted results

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    HRESULT hr = S_OK;

    TraceInfo(AimxOrchestrator, "Entry - AggregateResults");

    // This is a placeholder implementation
    // In a real implementation, this would:
    // 1. Parse results from multiple data sources
    // 2. Resolve conflicts and duplicates
    // 3. Format data according to client requirements
    // 4. Apply any necessary data transformations
    // 5. Generate final response structure

    // Simulate aggregation processing
    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    // Generate aggregated results
    aggregatedResults = AimxConstants::PromptFormatting::AIMX_JSON_OPEN_BRACE;
    aggregatedResults += L"  \"" + Utf8ToWide(AimxConstants::JsonFields::AIMX_JSON_KEY_AGGREGATION_STATUS) + L"\": \"" + Utf8ToWide(AimxConstants::JsonFields::AIMX_JSON_VALUE_COMPLETED) + L"\",\n";
    aggregatedResults += L"  \"" + Utf8ToWide(AimxConstants::JsonFields::AIMX_JSON_KEY_SOURCE_DATA) + L"\": \"" + rawResults + L"\",\n";
    aggregatedResults += L"  \"" + Utf8ToWide(AimxConstants::JsonFields::AIMX_JSON_KEY_FINAL_RESULT) + L"\": {\n";
    aggregatedResults += L"    \"" + Utf8ToWide(AimxConstants::JsonFields::AIMX_JSON_KEY_SUMMARY) + L"\": \"Data aggregation completed successfully\",\n";
    aggregatedResults += L"    \"" + Utf8ToWide(AimxConstants::JsonFields::AIMX_JSON_KEY_DATA_QUALITY) + L"\": \"" + Utf8ToWide(AimxConstants::JsonFields::AIMX_JSON_VALUE_HIGH) + L"\",\n";
    aggregatedResults += L"    \"" + Utf8ToWide(AimxConstants::JsonFields::AIMX_JSON_KEY_COMPLETENESS) + L"\": \"" + Utf8ToWide(AimxConstants::JsonFields::AIMX_JSON_VALUE_100_PERCENT) + L"\"\n";
    aggregatedResults += L"  },\n";
    aggregatedResults += L"  \"" + Utf8ToWide(AimxConstants::JsonFields::AIMX_JSON_KEY_AGGREGATION_TIME) + L"\": \"0.5" + AimxConstants::PromptFormatting::AIMX_SECONDS_SUFFIX + L"\"\n";
    aggregatedResults += AimxConstants::PromptFormatting::AIMX_JSON_CLOSE_BRACE;

    TraceInfo(AimxOrchestrator, "Result aggregation completed successfully");

    TraceInfo(AimxOrchestrator, "Exit - AggregateResults. hr: %!HRESULT!", hr);
    return hr;
}


HRESULT
GenerateLLMAnswerFromContext(
    _In_ const std::wstring& originalQuestion,
    _In_ const std::wstring& combinedResults,
    _Out_ std::wstring& answerSummary
    )
/*++

Routine Description:
    Generate a final answer summary using LLM based on the original question
    and combined results from tool execution. This is a fallback function
    for when the new LLMInfer component is not available.

Arguments:
    originalQuestion - The original user query
    combinedResults - Combined results from tool execution
    answerSummary - Output final answer summary

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxOrchestrator, "Generating LLM answer from context (fallback method)");

    try
    {
        // Try to use the new LLMInfer component first
        LLM_RESPONSE_RESULT responseResult = {};
        HRESULT hr = LLMInfer::GenerateResponse(originalQuestion, combinedResults, responseResult);
        if (SUCCEEDED(hr))
        {
            answerSummary = responseResult.response;
            TraceInfo(AimxOrchestrator, "Successfully generated answer using LLMInfer");
            return S_OK;
        }

        TraceWarn(AimxOrchestrator, "LLMInfer failed, using simple fallback: %!HRESULT!", hr);

        // Fallback: Create a simple formatted response
        answerSummary = AimxConstants::PromptFormatting::AIMX_BASED_ON_INFO_PREFIX;
        answerSummary += combinedResults;
        answerSummary += AimxConstants::PromptFormatting::AIMX_ADDRESSES_QUERY_PREFIX;
        answerSummary += originalQuestion;
        answerSummary += AimxConstants::PromptFormatting::AIMX_ADDRESSES_QUERY_SUFFIX;

        TraceInfo(AimxOrchestrator, "Generated fallback answer summary");
        return S_OK;
    }
    catch (...)
    {
        TraceErr(AimxOrchestrator, "Exception in GenerateLLMAnswerFromContext");

        // Ultimate fallback
        answerSummary = AimxConstants::PromptFormatting::AIMX_UNABLE_TO_GENERATE_PREFIX;
        answerSummary += combinedResults;

        return E_FAIL;
    }
}