#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Comprehensive AIMX deployment script that automatically runs in SYSTEM context
    
.DESCRIPTION
    This single script handles the complete AIMX deployment process:
    1. Installs .NET Runtime prerequisites
    2. Configures strong name verification bypass
    3. Runs with Administrator privileges
    4. Stops existing AIMX services and processes
    5. Copies binaries to appropriate system locations
    6. Installs PowerShell module for all users
    7. Starts the AIMX service
    
.PARAMETER BinaryPath
    Path to the folder containing the binary files with 'system' and 'programdata' subfolders (default: .\bin)

.PARAMETER Force
    Skip confirmation prompts and force deployment

.PARAMETER BackupExisting
    Create backups of existing files before overwriting (default: true)



.EXAMPLE
    .\Deploy-AIMX.ps1

.EXAMPLE
    .\Deploy-AIMX.ps1 -BinaryPath "C:\Temp\AIMX-Binaries"

.EXAMPLE
    .\Deploy-AIMX.ps1 -BinaryPath "C:\Temp\AIMX-Binaries" -Force -BackupExisting:$false
    
.NOTES
    This script requires Administrator privileges. Right-click PowerShell and "Run as Administrator".
    
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [string]$BinaryPath = ".\bin",

    [switch]$Force,

    [bool]$BackupExisting = $true
)

# Configuration
$ServiceName = "AIMXSrv"
$ProcessName = "netrag"
$SystemSubfolder = "system"
$ProgramDataSubfolder = "programdata"

# Function to get current user context
function Get-CurrentUserContext {
    try {
        $currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent()
        return @{
            Name = $currentUser.Name
            IsSystem = $currentUser.IsSystem
            IsAdmin = ([Security.Principal.WindowsPrincipal] $currentUser).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
        }
    }
    catch {
        return @{
            Name = "Unknown"
            IsSystem = $false
            IsAdmin = $false
        }
    }
}

# Requires Administrator privileges
#Requires -RunAsAdministrator

# Get current execution context
$userContext = Get-CurrentUserContext

# System paths using environment variables
$System32Path = [Environment]::GetFolderPath([Environment+SpecialFolder]::System)
$ProgramDataPath = [Environment]::GetFolderPath([Environment+SpecialFolder]::CommonApplicationData)

$AimxProgramDataPath = Join-Path $ProgramDataPath "Microsoft\AIMX"
$PowerShellProfilePath = Join-Path $System32Path "WindowsPowerShell\v1.0\profile.ps1"
$AimxModulePath = Join-Path $System32Path "aimx.psd1"

Write-Host "=== AIMX Deployment Script ===" -ForegroundColor Green
if ($env:AIMX_DEPLOY_RESTARTED -eq "1") {
    Write-Host "Running in restarted session (strong name bypass applied)" -ForegroundColor Cyan
}
Write-Host ""
Write-Host "Execution Context:" -ForegroundColor Yellow
Write-Host "  User: $($userContext.Name)" -ForegroundColor Cyan
Write-Host "  Is Admin: $($userContext.IsAdmin)" -ForegroundColor Cyan
Write-Host ""

# Validate input parameters
if (-not (Test-Path $BinaryPath)) {
    Write-Host "ERROR: Binary path does not exist: $BinaryPath" -ForegroundColor Red
    exit 1
}

$SystemSourcePath = Join-Path $BinaryPath $SystemSubfolder
$ProgramDataSourcePath = Join-Path $BinaryPath $ProgramDataSubfolder

if (-not (Test-Path $SystemSourcePath)) {
    Write-Host "ERROR: System subfolder not found: $SystemSourcePath" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $ProgramDataSourcePath)) {
    Write-Host "ERROR: ProgramData subfolder not found: $ProgramDataSourcePath" -ForegroundColor Red
    exit 1
}

Write-Host "Deployment Configuration:" -ForegroundColor Yellow
Write-Host "  Binary Source: $BinaryPath" -ForegroundColor Cyan
Write-Host "  System32 Target: $System32Path" -ForegroundColor Cyan
Write-Host "  ProgramData Target: $AimxProgramDataPath" -ForegroundColor Cyan
Write-Host "  PowerShell Profile: $PowerShellProfilePath" -ForegroundColor Cyan
Write-Host ""

# Confirmation prompt
if (-not $Force) {
    $confirmation = Read-Host "Proceed with AIMX deployment? (y/N)"
    if ($confirmation -notmatch '^[Yy]') {
        Write-Host "Deployment cancelled." -ForegroundColor Yellow
        exit 0
    }
    Write-Host ""
}

# Function to stop service safely
function Stop-AimxService {
    Write-Host "Checking AIMX service status..." -ForegroundColor Yellow
    
    try {
        $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
        if ($service -and $service.Status -eq 'Running') {
            Write-Host "Stopping $ServiceName service..." -ForegroundColor Yellow
            
            $result = & net stop $ServiceName 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Host "$ServiceName service stopped successfully." -ForegroundColor Green
                
                # Wait for service to fully stop
                $timeout = 30
                $elapsed = 0
                while ($elapsed -lt $timeout) {
                    $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
                    if (-not $service -or $service.Status -eq 'Stopped') {
                        break
                    }
                    Start-Sleep -Seconds 1
                    $elapsed++
                }
                
                if ($elapsed -ge $timeout) {
                    Write-Host "WARNING: Service did not stop within $timeout seconds" -ForegroundColor Yellow
                }
            } else {
                Write-Host "WARNING: Failed to stop service using net stop: $result" -ForegroundColor Yellow
            }
        } elseif ($service) {
            Write-Host "$ServiceName service is already stopped." -ForegroundColor Green
        } else {
            Write-Host "$ServiceName service not found (may not be installed yet)." -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "WARNING: Error checking service status: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# Function to kill process safely
function Stop-NetRagProcess {
    Write-Host "Checking for $ProcessName.exe processes..." -ForegroundColor Yellow
    
    try {
        $processes = Get-Process -Name $ProcessName -ErrorAction SilentlyContinue
        if ($processes) {
            Write-Host "Found $($processes.Count) $ProcessName.exe process(es). Terminating..." -ForegroundColor Yellow
            
            foreach ($process in $processes) {
                try {
                    $process.Kill()
                    $process.WaitForExit(5000)  # Wait up to 5 seconds
                    Write-Host "Process $($process.Id) terminated successfully." -ForegroundColor Green
                }
                catch {
                    Write-Host "WARNING: Failed to terminate process $($process.Id): $($_.Exception.Message)" -ForegroundColor Yellow
                }
            }
        } else {
            Write-Host "No $ProcessName.exe processes found." -ForegroundColor Green
        }
    }
    catch {
        Write-Host "WARNING: Error checking for processes: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}



# Step 1: Install .NET Runtime Prerequisites
Write-Host "=== Step 1: Installing .NET Runtime Prerequisites ===" -ForegroundColor Magenta

$DotNetFolder = Join-Path $PSScriptRoot "dotnet"
$DotNetRuntime = Join-Path $DotNetFolder "dotnet-runtime-8.0.18-win-x64.exe"
$AspNetCoreRuntime = Join-Path $DotNetFolder "aspnetcore-runtime-8.0.18-win-x64.exe"

# Check if .NET runtime installers exist
if (-not (Test-Path $DotNetFolder)) {
    Write-Host "WARNING: .NET runtime folder not found: $DotNetFolder" -ForegroundColor Yellow
    Write-Host "Skipping .NET runtime installation..." -ForegroundColor Yellow
} else {
    # Install .NET Runtime
    if (Test-Path $DotNetRuntime) {
        Write-Host "Installing .NET Runtime 8.0.18..." -ForegroundColor Yellow
        try {
            $process = Start-Process -FilePath $DotNetRuntime -ArgumentList "/install", "/quiet", "/norestart" -Wait -PassThru
            if ($process.ExitCode -eq 0) {
                Write-Host ".NET Runtime installed successfully" -ForegroundColor Green
            } elseif ($process.ExitCode -eq 1638) {
                Write-Host ".NET Runtime already installed (newer version exists)" -ForegroundColor Green
            } else {
                Write-Host "WARNING: .NET Runtime installation returned exit code $($process.ExitCode)" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "ERROR: Failed to install .NET Runtime: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "WARNING: .NET Runtime installer not found: $DotNetRuntime" -ForegroundColor Yellow
    }

    # Install ASP.NET Core Runtime
    if (Test-Path $AspNetCoreRuntime) {
        Write-Host "Installing ASP.NET Core Runtime 8.0.18..." -ForegroundColor Yellow
        try {
            $process = Start-Process -FilePath $AspNetCoreRuntime -ArgumentList "/install", "/quiet", "/norestart" -Wait -PassThru
            if ($process.ExitCode -eq 0) {
                Write-Host "ASP.NET Core Runtime installed successfully" -ForegroundColor Green
            } elseif ($process.ExitCode -eq 1638) {
                Write-Host "ASP.NET Core Runtime already installed (newer version exists)" -ForegroundColor Green
            } else {
                Write-Host "WARNING: ASP.NET Core Runtime installation returned exit code $($process.ExitCode)" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "ERROR: Failed to install ASP.NET Core Runtime: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "WARNING: ASP.NET Core Runtime installer not found: $AspNetCoreRuntime" -ForegroundColor Yellow
    }
}
Write-Host ""

# Step 2: Configure Strong Name Verification Bypass
Write-Host "=== Step 2: Configuring Strong Name Verification Bypass ===" -ForegroundColor Magenta

$snExe = Join-Path $DotNetFolder "sn.exe"

if (Test-Path $snExe) {
    Write-Host "Using sn.exe from: $snExe" -ForegroundColor Green
    try {
        # Skip strong name verification for all assemblies (global bypass)
        Write-Host "Configuring global strong name verification bypass..." -ForegroundColor Yellow
        $process = Start-Process -FilePath $snExe -ArgumentList "-Vr", "*,*" -Wait -PassThru -NoNewWindow
        if ($process.ExitCode -eq 0) {
            Write-Host "Strong name verification bypass configured successfully" -ForegroundColor Green

            # Check if we need to restart the script to take effect
            if (-not $env:AIMX_DEPLOY_RESTARTED) {
                Write-Host "Restarting PowerShell session to ensure strong name bypass takes effect..." -ForegroundColor Yellow
                Write-Host "Please wait..." -ForegroundColor Yellow

                # Set environment variable to prevent infinite restart loop
                $env:AIMX_DEPLOY_RESTARTED = "1"

                # Get current script parameters to pass to restarted session
                $scriptArgs = @()
                if ($BinaryPath -ne ".\bin") { $scriptArgs += "-BinaryPath", "`"$BinaryPath`"" }
                if ($Force) { $scriptArgs += "-Force" }
                if ($BackupExisting -eq $false) { $scriptArgs += "-BackupExisting:`$false" }

                # Restart the script in a new PowerShell session
                Start-Process -FilePath "powershell.exe" -ArgumentList "-ExecutionPolicy", "Bypass", "-File", "`"$PSCommandPath`"", $scriptArgs -Wait

                # Exit current session
                Write-Host "Deployment completed in restarted session." -ForegroundColor Green
                exit 0
            }
        } else {
            Write-Host "WARNING: Strong name verification bypass returned exit code $($process.ExitCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "ERROR: Failed to configure strong name verification bypass: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "WARNING: sn.exe not found at: $snExe" -ForegroundColor Yellow
    Write-Host "Strong name verification bypass not configured." -ForegroundColor Yellow
}
Write-Host ""

# Step 3: Stop services and processes
Write-Host "=== Step 3: Stopping Services and Processes ===" -ForegroundColor Magenta
Stop-AimxService
Stop-NetRagProcess
Write-Host ""

# Step 4: Copy system files
Write-Host "=== Step 4: Copying System Files ===" -ForegroundColor Magenta
Write-Host "Copying system files..." -ForegroundColor Yellow
Write-Host "  From: $SystemSourcePath" -ForegroundColor Cyan
Write-Host "  To: $System32Path" -ForegroundColor Cyan

try {
    # Use robocopy for more robust copying with retry and continue on failure
    # /E = copy subdirectories including empty ones
    # /R:3 = retry 3 times on failed copies
    # /W:1 = wait 1 second between retries
    # /NP = no progress display (cleaner output)
    # /NDL = no directory list
    # /NFL = no file list (only show summary)
    # /TEE = output to console and log
    $robocopyResult = & robocopy "$SystemSourcePath" "$System32Path" /E /R:3 /W:1 /NP /NDL /NFL /TEE

    # Robocopy exit codes: 0-7 are success, 8+ are errors
    # 0 = No files copied (no change needed)
    # 1 = Files copied successfully
    # 2 = Extra files or directories detected and removed
    # 3 = Files copied and extra files detected
    # 4 = Some mismatched files or directories detected
    # 5 = Some files copied, some mismatched
    # 6 = Extra files and mismatched files exist
    # 7 = Files copied, mismatched and extra files exist
    if ($LASTEXITCODE -le 7) {
        Write-Host "System files copied successfully (robocopy exit code: $LASTEXITCODE)" -ForegroundColor Green
        $systemCopySuccess = $true
        if ($LASTEXITCODE -ge 4) {
            Write-Host "Note: Some files may have been skipped due to access restrictions" -ForegroundColor Yellow
        }
    } else {
        Write-Host "WARNING: robocopy completed with issues (exit code: $LASTEXITCODE)" -ForegroundColor Yellow
        Write-Host "Some files may have been copied successfully despite errors" -ForegroundColor Yellow
        $systemCopySuccess = $true  # Continue deployment even with some copy issues
    }
} catch {
    Write-Host "ERROR: Failed to copy system files: $($_.Exception.Message)" -ForegroundColor Red
    $systemCopySuccess = $false
}
Write-Host ""

# Step 5: Copy program data files
Write-Host "=== Step 5: Copying Program Data Files ===" -ForegroundColor Magenta
Write-Host "Copying program data files..." -ForegroundColor Yellow
Write-Host "  From: $ProgramDataSourcePath" -ForegroundColor Cyan
Write-Host "  To: $AimxProgramDataPath" -ForegroundColor Cyan

try {
    # Ensure destination directory exists
    if (-not (Test-Path $AimxProgramDataPath)) {
        New-Item -Path $AimxProgramDataPath -ItemType Directory -Force | Out-Null
        Write-Host "Created destination directory: $AimxProgramDataPath" -ForegroundColor Green
    }

    # Use robocopy for more robust copying with retry and continue on failure
    # /E = copy subdirectories including empty ones
    # /R:3 = retry 3 times on failed copies
    # /W:1 = wait 1 second between retries
    # /NP = no progress display (cleaner output)
    # /NDL = no directory list
    # /NFL = no file list (only show summary)
    # /TEE = output to console and log
    $robocopyResult = & robocopy "$ProgramDataSourcePath" "$AimxProgramDataPath" /E /R:3 /W:1 /NP /NDL /NFL /TEE

    # Robocopy exit codes: 0-7 are success, 8+ are errors
    if ($LASTEXITCODE -le 7) {
        Write-Host "Program data files copied successfully (robocopy exit code: $LASTEXITCODE)" -ForegroundColor Green
        $programDataCopySuccess = $true
        if ($LASTEXITCODE -ge 4) {
            Write-Host "Note: Some files may have been skipped due to access restrictions" -ForegroundColor Yellow
        }
    } else {
        Write-Host "WARNING: robocopy completed with issues (exit code: $LASTEXITCODE)" -ForegroundColor Yellow
        Write-Host "Some files may have been copied successfully despite errors" -ForegroundColor Yellow
        $programDataCopySuccess = $true  # Continue deployment even with some copy issues
    }
} catch {
    Write-Host "ERROR: Failed to copy program data files: $($_.Exception.Message)" -ForegroundColor Red
    $programDataCopySuccess = $false
}
Write-Host ""

# Step 6: Install PowerShell module for all users
Write-Host "=== Step 6: Installing PowerShell Module ===" -ForegroundColor Magenta
try {
    # Check if AIMX.psd1 exists in System32
    if (Test-Path $AimxModulePath) {
        Write-Host "Found AIMX PowerShell module at: $AimxModulePath" -ForegroundColor Green

        # Prepare the import statement
        $importStatement = "Import-Module `"$AimxModulePath`""

        # Check if profile exists and read current content
        $profileContent = ""
        if (Test-Path $PowerShellProfilePath) {
            $profileContent = Get-Content $PowerShellProfilePath -Raw
        } else {
            # Create the profile directory if it doesn't exist
            $profileDir = Split-Path $PowerShellProfilePath -Parent
            if (-not (Test-Path $profileDir)) {
                New-Item -Path $profileDir -ItemType Directory -Force | Out-Null
            }
        }

        # Check if import statement already exists
        if ($profileContent -notmatch [regex]::Escape($importStatement)) {
            # Add the import statement
            if ($profileContent -and -not $profileContent.EndsWith("`n")) {
                $profileContent += "`n"
            }
            $profileContent += $importStatement + "`n"

            # Create backup if requested
            if ($BackupExisting -and (Test-Path $PowerShellProfilePath)) {
                $backupPath = $PowerShellProfilePath + ".backup." + (Get-Date -Format "yyyyMMdd-HHmmss")
                Copy-Item $PowerShellProfilePath $backupPath -Force
                Write-Host "Backed up existing profile to: $backupPath" -ForegroundColor Gray
            }

            # Write the updated profile
            $profileContent | Set-Content $PowerShellProfilePath -Encoding UTF8
            Write-Host "Added AIMX module import to PowerShell profile." -ForegroundColor Green
        } else {
            Write-Host "AIMX module import already exists in PowerShell profile." -ForegroundColor Green
        }
    } else {
        Write-Host "WARNING: AIMX.psd1 not found in System32. PowerShell module not configured." -ForegroundColor Yellow
    }
}
catch {
    Write-Host "ERROR: Failed to configure PowerShell module: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Step 5: Install and Start the AIMX Service
Write-Host "=== Step 5: Installing and Starting AIMX Service ===" -ForegroundColor Magenta

# Check if service is installed
$service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
if (-not $service) {
    Write-Host "$ServiceName service is not installed." -ForegroundColor Yellow

    # Check if service installer exists
    $serviceInstallerPath = Join-Path $System32Path "Aimx.ServiceInstaller.exe"
    if (Test-Path $serviceInstallerPath) {
        Write-Host "Found service installer at: $serviceInstallerPath" -ForegroundColor Green

        if (-not $Force) {
            $installChoice = Read-Host "Install $ServiceName service now? (Y/n)"
            if ($installChoice -match '^[Nn]') {
                Write-Host "Service installation skipped. You can install it manually later using: $serviceInstallerPath" -ForegroundColor Yellow
                Write-Host ""
                return
            }
        }

        Write-Host "Installing $ServiceName service..." -ForegroundColor Yellow
        try {
            $installResult = & $serviceInstallerPath 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Host "$ServiceName service installed successfully." -ForegroundColor Green

                # Refresh service object after installation
                Start-Sleep -Seconds 2
                $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
            } else {
                Write-Host "ERROR: Service installation failed with exit code $LASTEXITCODE" -ForegroundColor Red
                Write-Host "Output: $installResult" -ForegroundColor Gray
                Write-Host ""
                return
            }
        }
        catch {
            Write-Host "ERROR: Failed to run service installer: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host ""
            return
        }
    } else {
        Write-Host "ERROR: Service installer not found at: $serviceInstallerPath" -ForegroundColor Red
        Write-Host "Please ensure Aimx.ServiceInstaller.exe is copied to System32." -ForegroundColor Yellow
        Write-Host ""
        return
    }
}

# Run Initialize-AimxServiceWizard if service is installed
if ($service -or (Get-Service -Name $ServiceName -ErrorAction SilentlyContinue)) {
    Write-Host "Running AIMX Service Wizard..." -ForegroundColor Yellow

    try {
        # Import AIMX module if available to run the wizard
        if (Test-Path $AimxModulePath) {
            Import-Module $AimxModulePath -Force -ErrorAction SilentlyContinue

            # Check if the cmdlet is available
            $wizardCommand = Get-Command Initialize-AimxServiceWizard -ErrorAction SilentlyContinue
            if ($wizardCommand) {
                Write-Host "Initializing AIMX Service Wizard (Foundry setup)..." -ForegroundColor Green
                Initialize-AimxServiceWizard
                Write-Host "AIMX Service Wizard completed." -ForegroundColor Green
            } else {
                Write-Host "WARNING: Initialize-AimxServiceWizard cmdlet not found. Skipping wizard." -ForegroundColor Yellow
                Write-Host "You can run it manually later: Initialize-AimxServiceWizard" -ForegroundColor Gray
            }
        } else {
            Write-Host "WARNING: AIMX PowerShell module not found. Skipping wizard." -ForegroundColor Yellow
            Write-Host "You can run it manually later after importing the module." -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "WARNING: Failed to run AIMX Service Wizard: $($_.Exception.Message)" -ForegroundColor Yellow
        Write-Host "You can run it manually later: Initialize-AimxServiceWizard" -ForegroundColor Gray
    }

    # Now start the service
    Write-Host "Starting $ServiceName service..." -ForegroundColor Yellow
    try {
        $result = & sc.exe start $ServiceName 2>&1
        if ($LASTEXITCODE -eq 0 -or $result -match "already running") {
            Write-Host "$ServiceName service started successfully." -ForegroundColor Green
        } else {
            Write-Host "WARNING: Failed to start service: $result" -ForegroundColor Yellow
            Write-Host "You can manually start it later using: sc.exe start $ServiceName" -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "ERROR: Failed to start service: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "You can manually start it later using: sc.exe start $ServiceName" -ForegroundColor Gray
    }
} else {
    Write-Host "ERROR: $ServiceName service installation verification failed." -ForegroundColor Red
}
Write-Host ""

# Summary
Write-Host "=== Deployment Summary ===" -ForegroundColor Green
Write-Host ""
if ($systemCopySuccess -and $programDataCopySuccess) {
    Write-Host "AIMX deployment completed successfully!" -ForegroundColor Green
} else {
    Write-Host "AIMX deployment completed with some errors. Please review the output above." -ForegroundColor Yellow
}
Write-Host ""
Write-Host "Deployment Details:" -ForegroundColor Yellow
Write-Host "  System files: $(if ($systemCopySuccess) { "Success" } else { "Errors" })" -ForegroundColor $(if ($systemCopySuccess) { "Green" } else { "Red" })
Write-Host "  Program data files: $(if ($programDataCopySuccess) { "Success" } else { "Errors" })" -ForegroundColor $(if ($programDataCopySuccess) { "Green" } else { "Red" })
Write-Host "  PowerShell module: Configured" -ForegroundColor Green
Write-Host "  Service status: Check above for details" -ForegroundColor Cyan
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Verify the AIMX service is running: Get-Service AIMXSrv" -ForegroundColor Cyan
Write-Host "2. Test PowerShell module: Import-Module aimx" -ForegroundColor Cyan
Write-Host "3. Check AIMX functionality as needed" -ForegroundColor Cyan
Write-Host ""
