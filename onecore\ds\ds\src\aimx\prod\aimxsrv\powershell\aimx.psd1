@{
    ModuleVersion        = '1.0.0'
    GUID                 = 'fcb069ec-e47f-4349-8e07-7340aa661df7'
    Author               = 'Microsoft Corporation'
    CompanyName          = 'Microsoft'
    Copyright            = 'Copyright (c) Microsoft Corporation. All rights reserved.'
    Description          = 'AIMX PowerShell Module'
    PowerShellVersion    = '5.1'
    CompatiblePSEditions = @('Desktop', 'Core')
    RequiredModules      = @()
    ScriptsToProcess     = @()
    TypesToProcess       = @()
    DscResourcesToExport = @()
    NestedModules        = @('aimxpsh.dll')
    FunctionsToExport    = @()
    CmdletsToExport      = @(
        'Get-AimxQuestionResponse',
        'Connect-AimxServer',
        'Close-AimxServer',
        'Initialize-AimxServiceWizard',
        'Get-AimxConversationMessages',
        'Get-AimxConversationStatus',
        'Get-AimxLlmStatus',
        'Get-AimxMcpServerInfo',
        'Start-AimxConversation',
        'Stop-AimxConversation'
    )
    VariablesToExport    = @()
    AliasesToExport      = @()
    HelpInfoUri          = 'https://aka.ms/aimx-help'
}