/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    WebViewMessageHandler.cpp

Abstract:

    This module implements the core message handling for the WebView interface in Wfbldr.
    Serves as the central router for messages between the WebView and specialized handlers.

Author:

    <PERSON><PERSON><PERSON> (r<PERSON><PERSON>) 05/17/2025

--*/

#include "pch.h"
#include "WebViewMessageHandler.h"
#include <wil/com.h>
#include <WebView2.h>
#include <sstream>
#include <unordered_map>

using json = nlohmann::json;

// Define the static message type map
const std::unordered_map<std::string, WebViewMessageHandler::MessageType> 
WebViewMessageHandler::messageTypeMap = {
};

/*++

Routine Description:

    Constructor for WebViewMessageHandler class. Initializes the handler with
    a reference to the WebView2 control.

Arguments:

    webView - Pointer to the WebView2 control that this handler will manage.

Return Value:

    None.

--*/
WebViewMessageHandler::WebViewMessageHandler(
    _In_ ICoreWebView2* webView
    ) 
    : _webView(webView)

{
}

/*++

Routine Description:

    Destructor for WebViewMessageHandler class. Performs cleanup by
    unregistering the WebView2 message handler.

Arguments:

    None.

Return Value:

    None.

--*/
WebViewMessageHandler::~WebViewMessageHandler() 
{
    // Unregister message handler if needed
    if (_webView) 
    {
        _webView->remove_WebMessageReceived(_webMessageToken);
    }
}

/*++

Routine Description:

    Initializes the WebView message handler and sets up event handlers.

Arguments:

    None.

Return Value:

    None.

--*/
void
WebViewMessageHandler::Initialize()
{
    LOGINFO("Initializing WebViewMessageHandler");
    
    if (!_webView)
    {
        LOGERROR("Cannot initialize handler: WebView is null");
        return;
    }
    
    // Register for web message events
    HRESULT hr = _webView->add_WebMessageReceived(
        Microsoft::WRL::Callback<ICoreWebView2WebMessageReceivedEventHandler>(
            this, &WebViewMessageHandler::HandleWebMessage).Get(),
        &_webMessageToken);
    
    if (FAILED(hr)) 
    {
        LOGERROR("Failed to register for WebMessageReceived events: " + std::to_string(hr));
    } 
    else 
    {
        LOGINFO("Successfully registered for WebMessageReceived events");
    }
}

/*++

Routine Description:

    Handles messages received from the WebView. Acts as a central router to
    direct messages to the appropriate handler based on message type.

Arguments:

    sender - The WebView that sent the message.
    args - The message data.

Return Value:

    HRESULT indicating success or failure.

--*/
HRESULT
WebViewMessageHandler::HandleWebMessage(
    _In_ ICoreWebView2* /* sender */,
    _In_ ICoreWebView2WebMessageReceivedEventArgs* args
    )
{
    // Extract the message from WebView2
    wil::unique_cotaskmem_string messageRaw;
    args->get_WebMessageAsJson(&messageRaw);

    std::wstring message = messageRaw.get();
    LOGVERBOSE("Received message from WebView: " + WideToUtf8(message));
    
    try 
    {
        // Parse the message into JSON
        nlohmann::json parsedJson = ParseWebMessage(message);
        
        // Extract common message properties
        if (!parsedJson.contains("type") || !parsedJson["type"].is_string()) 
        {
            LOGERROR("JSON message missing 'type' field");
            return E_FAIL;
        }
        
        std::string messageType = parsedJson["type"];
        LOGVERBOSE("Processing message with type: " + messageType);
        
        // Get content if available
        std::wstring content;
        if (parsedJson.contains("content") && parsedJson["content"].is_string()) 
        {
            content = Utf8ToWide(parsedJson["content"]);
        }
                
        // processing message based on type
        // switch (HashMessageType(messageType))
        // {                  
        // }
    }
    catch (const std::exception& e) 
    {
        LOGERROR("Exception processing message: " + std::string(e.what()));
        return E_FAIL;
    }
    
    return S_OK;
}

/*++

Routine Description:

    Sends a message to the WebView.

Arguments:

    message - The message to send.

Return Value:

    None.

--*/
void
WebViewMessageHandler::SendMessageToWebView(
    _In_ const std::wstring& message,
    _In_ bool logFullMessage /* = true */
    )
{
    // Only log the first part of potentially large messages
    std::wstring logMessage = message.length() > 50 
        ? message.substr(0, 50) + L"..." 
        : message;
    
    LOGVERBOSE("Sending message to WebView: " + WideToUtf8(logFullMessage ? message : logMessage));
    
    if (!_webView) 
    {
        LOGERROR("Cannot send message: WebView is null");
        return;
    }
    
    // Verify that the message is valid JSON before sending
    try 
    {
        // Test parse to ensure we're sending valid JSON
        // This will throw if the JSON is invalid
        auto jsonVerify = nlohmann::json::parse(WideToUtf8(message));
        
        // Create a double-check string to make sure quotes are escaped properly
        std::string doubleCheckStr = WideToUtf8(message);
        size_t pos = doubleCheckStr.find("\\\"");
        if (pos != std::string::npos) 
        {
            LOGVERBOSE("Found escaped quotes in JSON, double checking format");
        }
        
        // Send message - store the HRESULT return value to avoid [[nodiscard]] warning
        HRESULT hr = _webView->PostWebMessageAsJson(message.c_str());
        if (FAILED(hr)) 
        {
            LOGERROR("Failed to send message to WebView: " + std::to_string(hr));
        }
    } 
    catch (const std::exception& e) 
    {
        LOGERROR("Invalid JSON message: " + std::string(e.what()));
        
        // Fallback: Send as properly escaped JSON string
        try 
        {
            nlohmann::json fallbackJson;
            fallbackJson["type"] = "assistantMessage";
            fallbackJson["content"] = WideToUtf8(message);
            
            std::string jsonStr = fallbackJson.dump();
            // Log the fallback JSON for debugging
            LOGVERBOSE("Using fallback JSON: " + jsonStr);
            
            std::wstring fallbackMessage = Utf8ToWide(jsonStr);
            // Store the HRESULT return value to avoid [[nodiscard]] warning
            HRESULT hr = _webView->PostWebMessageAsJson(fallbackMessage.c_str());
            if (FAILED(hr)) 
            {
                LOGERROR("Failed to send fallback message to WebView: " + std::to_string(hr));
            }
        } 
        catch (const std::exception& e2) 
        {
            LOGERROR("Failed to create fallback message: " + std::string(e2.what()));
        }
    }
}

/*++

Routine Description:

    Parses a JSON message from the WebView.

Arguments:

    json - The JSON message to parse.
    type - Output parameter that will receive the message type.
    content - Output parameter that will receive the message content.

Return Value:

    bool indicating whether the parsing was successful.

--*/
bool
WebViewMessageHandler::ParseJsonMessage(
    _In_ const std::wstring& json,
    _Out_ std::wstring& type,
    _Out_ std::wstring& content
    )
{
    try 
    {
        auto jsonObj = nlohmann::json::parse(WideToUtf8(json));
        
        if (jsonObj.contains("type") && jsonObj["type"].is_string()) 
        {
            type = Utf8ToWide(jsonObj["type"].get<std::string>());
        } 
        else 
        {
            LOGERROR("JSON message missing 'type' field");
            return false;
        }
        
        if (jsonObj.contains("content") && jsonObj["content"].is_string()) 
        {
            content = Utf8ToWide(jsonObj["content"].get<std::string>());
        } 
        else 
        {
            // Content is optional in some messages
            content = L"";
        }
        
        return true;
    } 
    catch (const std::exception& e) 
    {
        LOGERROR("Exception parsing JSON: " + std::string(e.what()));
        return false;
    }
}

/*++

Routine Description:

    Parses a WebView message, handling both quoted and unquoted JSON formats.

Arguments:

    message - The raw message from WebView2.

Return Value:

    nlohmann::json object containing the parsed message.

--*/
nlohmann::json
WebViewMessageHandler::ParseWebMessage(
    _In_ const std::wstring& message
    )
{
    // Check if the message is wrapped in quotes (JSON string) and needs to be unescaped first
    if (message.length() > 2 && message[0] == L'"' && message[message.length()-1] == L'"') {
        LOGVERBOSE("Detected quoted JSON message, unescaping");
        
        // Remove outer quotes and unescape
        std::wstring unquoted = message.substr(1, message.length() - 2);
        
        // Replace escaped quotes
        size_t pos = 0;
        while ((pos = unquoted.find(L"\\\"", pos)) != std::wstring::npos) {
            unquoted.replace(pos, 2, L"\"");
            pos += 1;
        }
        
        // Replace escaped backslashes
        pos = 0;
        while ((pos = unquoted.find(L"\\\\", pos)) != std::wstring::npos) {
            unquoted.replace(pos, 2, L"\\");
            pos += 1;
        }
        
        // Parse the unescaped JSON
        return nlohmann::json::parse(WideToUtf8(unquoted));
    }
    else {
        // Parse the message directly as JSON
        return nlohmann::json::parse(WideToUtf8(message));
    }
}

/*++

Routine Description:

    Generates a unique session ID for streaming responses.

Arguments:

    None.

Return Value:

    std::wstring containing the generated session ID.

--*/
std::wstring
WebViewMessageHandler::GenerateSessionId()
{
    static LONG sessionCounter = 0;
    LONG sessionId = InterlockedIncrement(&sessionCounter);
    
    // Get timestamp for additional uniqueness
    FILETIME ft;
    GetSystemTimeAsFileTime(&ft);
    
    std::wostringstream ss;
    ss << L"session_" << sessionId << L"_" << ft.dwLowDateTime;
    return ss.str();
}

/*++

Routine Description:

    Maps string message type to enum value for switch statement usage.

Arguments:

    messageType - String representation of the message type.

Return Value:

    MessageType enum value corresponding to the string.

--*/
WebViewMessageHandler::MessageType
WebViewMessageHandler::HashMessageType(
    _In_ const std::string& messageType
    )
{
    auto it = messageTypeMap.find(messageType);
    return (it != messageTypeMap.end()) ? it->second : MessageType::Unknown;
}
