<!--++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    ragmanagement.html

Abstract:

    This page contains the markup for the RAG (Retrieval-Augmented Generation) management interface.
    Provides UI for managing, updating and indexing knowledge bases to enhance
    LLM responses with domain-specific information.

Author:

    <PERSON><PERSON> (solaadekunle) 04/09/2025

---->
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIMX RAG Database Builder</title>
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/styles.css">
    <script>
        console.log("RAG Builder HTML loaded - Head script executed");
    </script>
</head>

<body>
    <div class="container">
        <h1>RAG DB Management</h1>
        <ul class="list-group" id="knowledgeBaseList">
            <!-- 
            <li class="list-group-item d-flex justify-content-between align-items-center">
                Knowledge Base 1
                <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="toggle1">
                    <label class="custom-control-label" for="toggle1">Set as default</label>
                </div>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center">
                Knowledge Base 2
                <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="toggle2">
                    <label class="custom-control-label" for="toggle2">Set as default</label>
                </div>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center">
                Knowledge Base 3
                <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="toggle3">
                    <label class="custom-control-label" for="toggle3">Set as default</label>
                </div>
            </li>
            -->
        </ul>
    </div>
</body>

</html>