/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    aimxrpcserver.h

Abstract:
    AIMXSRV server-side private declarations.

Author:
    <PERSON> (SNAKE FIGHTER) (lindakup) 06/09/2025

--*/

#pragma once
#include <windows.h>

// private definitions of AIMX_HANDLE for internal use 
typedef struct _AIMX_HANDLE {
    GUID ContextId; // Unique identifier for the context
    PSID pOwnerSid; // Security identifier for the owner
} AIMX_HANDLE, * PAIMX_HANDLE;

void
AimxpFreeContext(
    _In_opt_ PAIMX_HANDLE pContextHandle);

HRESULT
AimxpGetRpcClientSid(
    _Outptr_ PSID* ppClientSid);

class AimxRpcServer
{
public:
    static HRESULT RpcStartListening();
    static BOOL IsAcceptingCalls();
    static HRESULT RpcStopListening();

private:
    static BOOL _fListening;
    static BOOL _fAcceptingCalls;
    static HRESULT _StartRpcServer();
    static HRESULT _StopRpcServer();
    static void RpcAcceptCalls();

    static HRESULT MakeRpcSecurityDescriptor(
        _Outptr_ PSECURITY_DESCRIPTOR* ppRpcSD);

    AimxRpcServer();
    ~AimxRpcServer();
};
