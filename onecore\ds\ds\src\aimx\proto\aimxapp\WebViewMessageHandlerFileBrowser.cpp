/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    WebViewMessageHandlerFileBrowser.cpp

Abstract:

    This module implements file browsing functionality for the WebView message handler.
    Provides native file system dialogs for folder and file selection.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 03/28/2025

--*/

#include "pch.h"
#include "WebViewMessageHandler.h"
#include <shobjidl_core.h>
#include <wil/com.h>
#include <sstream>
#include <string>

/*++

Routine Description:

    Shows a native folder browser dialog and returns the result to the WebView.

Arguments:

    None.

Return Value:

    None.

--*/
void
WebViewMessageHandler::BrowseForFolder()
{
    LOGVERBOSE("Opening folder browser dialog");
    
    // Initialize COM for the file dialog
    HRESULT hr = CoInitializeEx(NULL, COINIT_APARTMENTTHREADED | COINIT_DISABLE_OLE1DDE);
    if (FAILED(hr)) 
    {
        LOGERROR("Failed to initialize COM for folder browser: " + std::to_string(hr));
        
        // Send error response
        std::wostringstream responseJson;
        responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"browseResponse\",";
        responseJson << L"\"success\":false,\"message\":\"Failed to initialize folder browser\"}";
        SendMessageToWebView(responseJson.str());
        return;
    }
    
    // Ensure COM is cleaned up when we exit this function
    auto comCleanup = wil::scope_exit([&]() 
    {
        CoUninitialize();
    });
    
    // Create file dialog
    wil::com_ptr<IFileDialog> pFileDialog;
    hr = CoCreateInstance(CLSID_FileOpenDialog, NULL, CLSCTX_INPROC_SERVER, 
                         IID_PPV_ARGS(&pFileDialog));
    if (FAILED(hr)) 
    {
        LOGERROR("Failed to create folder browser dialog: " + std::to_string(hr));
        
        // Send error response
        std::wostringstream responseJson;
        responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"browseResponse\",";
        responseJson << L"\"success\":false,\"message\":\"Failed to create folder browser dialog\"}";
        SendMessageToWebView(responseJson.str());
        return;
    }
    
    // Configure dialog for folder selection
    if (!ConfigureFileOpenDialog(reinterpret_cast<IFileOpenDialog*>(pFileDialog.get()), true)) 
    {
        return; // Error already sent by ConfigureFileOpenDialog
    }
    
    // Show the dialog
    hr = pFileDialog->Show(NULL);
    if (hr == HRESULT_FROM_WIN32(ERROR_CANCELLED)) 
    {
        SendBrowseCancellationResponse();
        return;
    }
    
    if (FAILED(hr)) 
    {
        LOGERROR("Failed to show folder browser dialog: " + std::to_string(hr));
        
        // Send error response
        std::wostringstream responseJson;
        responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"browseResponse\",";
        responseJson << L"\"success\":false,\"message\":\"Failed to show folder browser dialog\"}";
        SendMessageToWebView(responseJson.str());
        return;
    }
    
    // Process the selected folder
    GetSelectedFolder(pFileDialog.get());
}

/*++

Routine Description:

    Shows a native file browser dialog for selecting multiple files and returns
    the result to the WebView.

Arguments:

    None.

Return Value:

    None.

--*/
void
WebViewMessageHandler::BrowseForFiles()
{
    LOGVERBOSE("Opening file browser dialog");
    
    // Initialize COM for the file dialog
    HRESULT hr = CoInitializeEx(NULL, COINIT_APARTMENTTHREADED | COINIT_DISABLE_OLE1DDE);
    if (FAILED(hr)) 
    {
        LOGERROR("Failed to initialize COM for file browser: " + std::to_string(hr));
        
        // Send error response
        std::wostringstream responseJson;
        responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"browseResponse\",";
        responseJson << L"\"success\":false,\"message\":\"Failed to initialize file browser\"}";
        SendMessageToWebView(responseJson.str());
        return;
    }
    
    // Ensure COM is cleaned up when we exit this function
    auto comCleanup = wil::scope_exit([&]() 
    {
        CoUninitialize();
    });
    
    // Create file open dialog
    wil::com_ptr<IFileOpenDialog> pFileDialog;
    hr = CoCreateInstance(CLSID_FileOpenDialog, NULL, CLSCTX_INPROC_SERVER, 
                         IID_PPV_ARGS(&pFileDialog));
    if (FAILED(hr)) 
    {
        LOGERROR("Failed to create file browser dialog: " + std::to_string(hr));
        
        // Send error response
        std::wostringstream responseJson;
        responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"browseResponse\",";
        responseJson << L"\"success\":false,\"message\":\"Failed to create file browser dialog\"}";
        SendMessageToWebView(responseJson.str());
        return;
    }
    
    // Configure dialog for file selection
    if (!ConfigureFileOpenDialog(pFileDialog.get(), false)) 
    {
        return; // Error already sent by ConfigureFileOpenDialog
    }
    
    // Show the dialog
    hr = pFileDialog->Show(NULL);
    if (hr == HRESULT_FROM_WIN32(ERROR_CANCELLED)) 
    {
        SendBrowseCancellationResponse();
        return;
    }
    
    if (FAILED(hr)) 
    {
        LOGERROR("Failed to show file browser dialog: " + std::to_string(hr));
        
        // Send error response
        std::wostringstream responseJson;
        responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"browseResponse\",";
        responseJson << L"\"success\":false,\"message\":\"Failed to show file browser dialog\"}";
        SendMessageToWebView(responseJson.str());
        return;
    }
    
    // Get selected files
    wil::com_ptr<IShellItemArray> pItemArray;
    hr = pFileDialog->GetResults(&pItemArray);
    if (FAILED(hr)) 
    {
        LOGERROR("Failed to get file selection results: " + std::to_string(hr));
        
        // Send error response
        std::wostringstream responseJson;
        responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"browseResponse\",";
        responseJson << L"\"success\":false,\"message\":\"Failed to get file selection results\"}";
        SendMessageToWebView(responseJson.str());
        return;
    }
    
    // Process the selected files
    ProcessSelectedFiles(pItemArray.get());
}

/*++

Routine Description:

    Sends an error response for browse operations.

Arguments:

    errorMessage - The error message to include in the response.

Return Value:

    None.

--*/
void
WebViewMessageHandler::SendBrowseErrorResponse(
    _In_ const std::string& errorMessage
)
{
    LOGERROR(errorMessage);
    
    std::wostringstream responseJson;
    responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"browseResponse\",";
    responseJson << L"\"success\":false,\"message\":\"" << EscapeJsonStringW(Utf8ToWide(errorMessage)) << L"\"}";
    SendMessageToWebView(responseJson.str());
}

/*++

Routine Description:

    Sends a cancellation response for browse operations.

Arguments:

    None.

Return Value:

    None.

--*/
void
WebViewMessageHandler::SendBrowseCancellationResponse()
{
    LOGINFO("Browse operation cancelled by user");
    
    std::wostringstream responseJson;
    responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"browseResponse\",";
    responseJson << L"\"success\":false,\"message\":\"Operation cancelled\"}";
    SendMessageToWebView(responseJson.str());
}

/*++

Routine Description:

    Configures a file open dialog with appropriate options.

Arguments:

    pFileDialog - Pointer to the file dialog to configure.
    folderPicker - Whether to configure for folder picking (true) or file picking (false).

Return Value:

    bool indicating success or failure.

--*/
bool
WebViewMessageHandler::ConfigureFileOpenDialog(
    _In_ IFileOpenDialog* pFileDialog,
    _In_ bool folderPicker
)
{
    if (!pFileDialog) 
    {
        return false;
    }
    
    // Get current options
    DWORD dwOptions;
    HRESULT hr = pFileDialog->GetOptions(&dwOptions);
    if (FAILED(hr)) 
    {
        LOGERROR("Failed to get dialog options: " + std::to_string(hr));
        
        std::wostringstream responseJson;
        responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"browseResponse\",";
        responseJson << L"\"success\":false,\"message\":\"Failed to get dialog options\"}";
        SendMessageToWebView(responseJson.str());
        return false;
    }
    
    // Set appropriate options based on mode
    if (folderPicker) 
    {
        // Configure for folder selection
        hr = pFileDialog->SetOptions(dwOptions | FOS_PICKFOLDERS);
    } 
    else 
    {
        // Configure for multiple file selection
        hr = pFileDialog->SetOptions(dwOptions | FOS_ALLOWMULTISELECT | FOS_FILEMUSTEXIST);
        
        if (SUCCEEDED(hr)) 
        {
            // Set file filters for supported document types
            const COMDLG_FILTERSPEC fileTypes[] = {
                { L"Document Files", L"*.txt;*.docx;*.pdf;*.log" },
                { L"Text Files", L"*.txt" },
                { L"Log Files", L"*.log" },
                { L"Word Documents", L"*.docx" },
                { L"PDF Files", L"*.pdf" },
                { L"All Files", L"*.*" }
            };
            hr = pFileDialog->SetFileTypes(ARRAYSIZE(fileTypes), fileTypes);
        }
    }
    
    if (FAILED(hr)) 
    {
        LOGERROR("Failed to set dialog options: " + std::to_string(hr));
        
        std::wostringstream responseJson;
        responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"browseResponse\",";
        responseJson << L"\"success\":false,\"message\":\"Failed to set dialog options\"}";
        SendMessageToWebView(responseJson.str());
        return false;
    }
    
    return true;
}

/*++

Routine Description:

    Gets the selected folder from a file dialog and sends the result.

Arguments:

    pFileDialog - Pointer to the file dialog containing the selection.

Return Value:

    bool indicating success or failure.

--*/
bool
WebViewMessageHandler::GetSelectedFolder(
    _In_ IFileDialog* pFileDialog
)
{
    // Get the selected folder
    wil::com_ptr<IShellItem> pItem;
    HRESULT hr = pFileDialog->GetResult(&pItem);
    if (FAILED(hr)) 
    {
        LOGERROR("Failed to get folder selection: " + std::to_string(hr));
        
        std::wostringstream responseJson;
        responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"browseResponse\",";
        responseJson << L"\"success\":false,\"message\":\"Failed to get folder selection\"}";
        SendMessageToWebView(responseJson.str());
        return false;
    }
    
    // Get the folder path
    wil::unique_cotaskmem_string pszFolderPath;
    hr = pItem->GetDisplayName(SIGDN_FILESYSPATH, &pszFolderPath);
    if (FAILED(hr) || !pszFolderPath) 
    {
        LOGERROR("Failed to get folder path: " + std::to_string(hr));
        
        std::wostringstream responseJson;
        responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"browseResponse\",";
        responseJson << L"\"success\":false,\"message\":\"Failed to get folder path\"}";
        SendMessageToWebView(responseJson.str());
        return false;
    }
    
    // Successfully got folder path
    std::wstring folderPath = pszFolderPath.get();
    LOGINFO("Selected folder: " + WideToUtf8(folderPath));
    
    // Send success response
    std::wostringstream responseJson;
    responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"browseResponse\",";
    responseJson << L"\"success\":true,\"path\":\"" << EscapeJsonStringW(folderPath) << L"\"}";
    SendMessageToWebView(responseJson.str());
    
    return true;
}

/*++

Routine Description:

    Processes an array of shell items and constructs file information.

Arguments:

    pItemArray - Pointer to the shell item array to process.

Return Value:

    bool indicating success or failure.

--*/
bool
WebViewMessageHandler::ProcessSelectedFiles(
    _In_ IShellItemArray* pItemArray
)
{
    if (!pItemArray) 
    {
        LOGERROR("Invalid file selection");
        
        std::wostringstream responseJson;
        responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"browseResponse\",";
        responseJson << L"\"success\":false,\"message\":\"Invalid file selection\"}";
        SendMessageToWebView(responseJson.str());
        return false;
    }
    
    // Get file count
    DWORD fileCount = 0;
    HRESULT hr = pItemArray->GetCount(&fileCount);
    if (FAILED(hr)) 
    {
        LOGERROR("Failed to get file count: " + std::to_string(hr));
        
        std::wostringstream responseJson;
        responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"browseResponse\",";
        responseJson << L"\"success\":false,\"message\":\"Failed to get file count\"}";
        SendMessageToWebView(responseJson.str());
        return false;
    }
    
    if (fileCount == 0) 
    {
        LOGERROR("No files selected");
        
        std::wostringstream responseJson;
        responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"browseResponse\",";
        responseJson << L"\"success\":false,\"message\":\"No files selected\"}";
        SendMessageToWebView(responseJson.str());
        return false;
    }
    
    // Create a JSON array for file information
    nlohmann::json filesArray = nlohmann::json::array();
    
    // Process each selected file
    for (DWORD i = 0; i < fileCount; i++) 
    {
        wil::com_ptr<IShellItem> pItem;
        hr = pItemArray->GetItemAt(i, &pItem);
        if (FAILED(hr)) 
        {
            LOGERROR("Failed to get file at index " + std::to_string(i));
            continue;
        }
        
        wil::unique_cotaskmem_string pszFilePath;
        hr = pItem->GetDisplayName(SIGDN_FILESYSPATH, &pszFilePath);
        if (FAILED(hr) || !pszFilePath) 
        {
            LOGERROR("Failed to get file path at index " + std::to_string(i));
            continue;
        }
        
        CollectFileInfo(pszFilePath.get(), filesArray);
    }
    
    if (filesArray.empty()) 
    {
        LOGERROR("Failed to process any files");
        
        std::wostringstream responseJson;
        responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"browseResponse\",";
        responseJson << L"\"success\":false,\"message\":\"Failed to process any files\"}";
        SendMessageToWebView(responseJson.str());
        return false;
    }
    
    LOGINFO("Selected " + std::to_string(filesArray.size()) + " files using native browser");
    
    // Send response to the WebView with file information
    std::wostringstream responseJson;
    responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"browseResponse\",";
    responseJson << L"\"success\":true,\"files\":" << Utf8ToWide(filesArray.dump()) << L"}";
    SendMessageToWebView(responseJson.str());
    
    return true;
}

/*++

Routine Description:

    Collects file information for a specific file path.

Arguments:

    filePath - Path to the file to collect information about.
    filesArray - JSON array to add the file information to.

Return Value:

    None.

--*/
void
WebViewMessageHandler::CollectFileInfo(
    _In_ const wchar_t* filePath,
    _Inout_ nlohmann::json& filesArray
)
{
    if (!filePath) 
    {
        return;
    }
    
    std::wstring pathStr = filePath;
    std::wstring fileName = pathStr.substr(pathStr.find_last_of(L"\\") + 1);
    
    // Get file size
    WIN32_FILE_ATTRIBUTE_DATA fileAttr = {0};
    LARGE_INTEGER fileSize = {0};
    
    if (GetFileAttributesExW(filePath, GetFileExInfoStandard, &fileAttr)) 
    {
        fileSize.LowPart = fileAttr.nFileSizeLow;
        fileSize.HighPart = fileAttr.nFileSizeHigh;
    }
    
    // Add file info to array
    nlohmann::json fileInfo;
    fileInfo["name"] = WideToUtf8(fileName);
    fileInfo["path"] = WideToUtf8(pathStr);
    fileInfo["size"] = fileSize.QuadPart;
    
    filesArray.push_back(fileInfo);
}
