<#
.SYNOPSIS
    PowerShell-based MCP (Model Context Protocol) Server

.DESCRIPTION
    A pure PowerShell implementation of an MCP server that can handle:
    - MCP protocol initialization
    - Tool registration and listing
    - Tool execution via PowerShell commands
    - JSON-based communication with MCP hosts like Aimx

.AUTHOR
    Rupo Zhang (rizhang) 07-21-2025

.EXAMPLE
    .\Start-McpServer.ps1
#>

[CmdletBinding()]
param()

# Suppress warnings to keep stdout clean for JSON-RPC communication
$WarningPreference = 'SilentlyContinue'

# Import required modules
$ScriptRoot = $PSScriptRoot
Import-Module "$ScriptRoot\Modules\McpConstants.psm1" -Force -WarningAction SilentlyContinue
Import-Module "$ScriptRoot\Modules\McpProtocol.psm1" -Force -WarningAction SilentlyContinue
Import-Module "$ScriptRoot\Modules\McpToolRegistry.psm1" -Force -WarningAction SilentlyContinue

try {
    # Initialize the MCP server
    $server = New-McpServer

    # Note: This is a pure framework - no default tools are registered
    # Tools should be registered by the consuming application before starting the server
    # Example: Register-McpTool -Name "my-tool" -Description "..." -ScriptBlock { ... }

    # Start the server (this will block and handle stdio communication)
    Start-McpServer -Server $server


}
catch {
    # Exit silently on error to avoid contaminating stdout
    exit 1
}
