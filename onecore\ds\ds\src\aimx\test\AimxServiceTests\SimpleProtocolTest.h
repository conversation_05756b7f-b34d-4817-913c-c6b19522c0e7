/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    SimpleProtocolTest.h

Abstract:
    Header for simple AIMX RPC Server functionality demonstration test.

--*/

#pragma once

#include "WexTestClass.h"

using namespace WEX::Logging;
using namespace WEX::Common;
using namespace WEX::TestExecution;

class SimpleProtocolTest : public WEX::TestClass<SimpleProtocolTest>
{
public:
    BEGIN_TEST_CLASS(SimpleProtocolTest)
        TEST_CLASS_PROPERTY(L"TestClass", L"AIMX Simple Protocol Test")
        TEST_CLASS_PROPERTY(L"Description", L"Simple demonstration of AIMX RPC Server functionality")
    END_TEST_CLASS()

    // Basic workflow test demonstrating the core functionality
    BEGIN_TEST_METHOD(RunBasicWorkflowTest)
        TEST_METHOD_PROPERTY(L"Priority", L"1")
        TEST_METHOD_PROPERTY(L"Description", L"Demonstrates basic AIMX protocol workflow")
    END_TEST_METHOD()

    // Component validation test
    BEGIN_TEST_METHOD(RunComponentValidationTest)
        TEST_METHOD_PROPERTY(L"Priority", L"2")
        TEST_METHOD_PROPERTY(L"Description", L"Validates individual AIMX components")
    END_TEST_METHOD()
};