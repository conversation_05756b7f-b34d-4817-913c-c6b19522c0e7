/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    WebViewMessageHandlerRag.cpp

Abstract:

    This module implements RAG (Retrieval-Augmented Generation) functionality
    for the WebView message handler. Handles the building and management of
    knowledge bases.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 03/28/2025

--*/

#include "pch.h"
#include "WebViewMessageHandler.h"
#include "RAGBuilder.h"
#include <sstream>
#include <string>

using json = nlohmann::json;

/*++

Routine Description:

    Handles messages related to the RAG builder functionality.

Arguments:

    message - JSON message containing RAG builder command.

Return Value:

    None.

--*/
void WebViewMessageHandler::HandleRagBuilderMessage(_In_ const nlohmann::json& message)
{
    LOGINFO("Handling RAG builder message");

    try
    {
        if (!message.contains("action") || !message["action"].is_string())
        {
            LOGERROR("RAG Builder message missing action field");
            return;
        }

        std::string action = message["action"];
        LOGINFO("RAG Builder action: " + action);

        if (action == "build")
        {
            // Handle build request
            std::string outputDir = "./";
            std::string docsDir = "./";
            int chunkSize = 128;
            std::string ragDatabaseName = "rag_database";

            if (message.contains("outputDir") && message["outputDir"].is_string())
            {
                outputDir = message["outputDir"];
            }

            if (message.contains("docsDir") && message["docsDir"].is_string())
            {
                docsDir = message["docsDir"];
            }
            if (message.contains("databaseName") && message["databaseName"].is_string())
            {
                ragDatabaseName = message["databaseName"];
            }

            if (message.contains("chunkSize") && message["chunkSize"].is_number())
            {
                chunkSize = message["chunkSize"];
            }

            // Start the build process
            StartRagBuild(outputDir, docsDir, ragDatabaseName, chunkSize);
        }
        else if (action == "browse")
        {
            // Get browse mode (folder or files)
            std::string mode = "folder"; // Default mode
            if (message.contains("mode") && message["mode"].is_string())
            {
                mode = message["mode"];
            }

            // Handle browse request based on mode
            if (mode == "folder")
            {
                BrowseForFolder();
            }
            else if (mode == "files")
            {
                BrowseForFiles();
            }
            else
            {
                LOGERROR("Unknown browse mode: " + mode);

                // Send error response
                std::wostringstream responseJson;
                responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"browseResponse\",";
                responseJson << L"\"success\":false,\"message\":\"Invalid browse mode\"}";
                SendMessageToWebView(responseJson.str());
            }
        }
        else if (action == "directorySelected")
        {
            // Handle directory selected by JavaScript UI
            std::string path = "";
            int fileCount = 0;

            if (message.contains("path") && message["path"].is_string())
            {
                path = message["path"];
            }

            if (message.contains("fileCount") && message["fileCount"].is_number())
            {
                fileCount = message["fileCount"];
            }

            LOGINFO("Directory selected via JavaScript: " + path + " containing " + std::to_string(fileCount) + " files");
        }
        else if (action == "status")
        {
            // Send status of current build if any
            bool isBuilding = _ragBuilder != nullptr && _ragBuildInProgress;

            std::wostringstream responseJson;
            responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"statusResponse\",";
            responseJson << L"\"building\":" << (isBuilding ? L"true" : L"false") << L"}";
            SendMessageToWebView(responseJson.str());
        }
        else
        {
            LOGERROR("Unknown RAG Builder action: " + action);
        }
    }
    catch (const std::exception& e)
    {
        LOGERROR("Exception handling RAG builder message: " + std::string(e.what()));
    }
}

/*++

Routine Description:

    Starts the RAG database building process.

Arguments:

    outputDir - Directory where RAG database files will be saved.
    docsDir - Directory containing the documents to process.
    chunkSize - Size of text chunks for processing.

Return Value:

    None.

--*/
void WebViewMessageHandler::StartRagBuild(
    _In_ const std::string& outputDir, _In_ const std::string& docsDir, _In_ const std::string& databaseName, _In_ int chunkSize)
{
    auto ragList = std::unordered_set<std::filesystem::path>();
    auto& appConfig = AppConfig::getInstance();
    std::transform(appConfig.getRagDatabases().begin(), appConfig.getRagDatabases().end(),
                   std::inserter(ragList, ragList.end()),
                   [](const std::string& db)
    {
        std::filesystem::path dbPath = std::filesystem::path("./ragdb").append(db);
                       return dbPath;
                   });

    // Check that we have not already built the database
    auto newDbPath = std::filesystem::path("./ragdb").append(databaseName);
    if (ragList.find(newDbPath) != ragList.end())
    {
        LOGERROR("RAG database already exists in the list: " + databaseName);
        std::wostringstream responseJson;
        responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"buildResponse\",";
        responseJson << L"\"success\":false,\"message\":\"Exception during RAG build. The RAG Database already exists:\"}";
        SendMessageToWebView(responseJson.str());
        SendToastNotification(L"The RAG database exists. Please select a different name for the RAG Database");
        return;
    }

    LOGINFO("Starting RAG build process");
    LOGINFO("Output Directory: " + outputDir);
    LOGINFO("Documents Directory: " + docsDir);
    LOGINFO("Chunk Size: " + std::to_string(chunkSize));

    // Check if there's already a build in progress
    if (_ragBuildInProgress)
    {
        LOGERROR("Cannot start RAG build: another build is already in progress");

        // Notify the UI
        std::wostringstream responseJson;
        responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"buildResponse\",";
        responseJson << L"\"success\":false,\"message\":\"Another build is already in progress\"}";
        SendMessageToWebView(responseJson.str());
        return;
    }

    // Create a new RAG builder instance
    _ragBuilder = std::make_unique<RAGBuilder>();
    _ragBuildInProgress = true;

    // Notify the UI that the build has started
    std::wostringstream startJson;
    startJson << L"{\"type\":\"ragBuilder\",\"action\":\"buildStarted\"}";
    SendMessageToWebView(startJson.str());

    try
    {
        // Report initialization starting
        SendProgressUpdate("Initializing RAG builder...", 0);

        // Initialize the builder with parameters
        model_params params;
        params.model_name = "./models/all-minilm-l6-v2_f32.gguf";
        params.n_ctx = 512;
        params.n_dim = 384; // This depends on the model

        bool success = _ragBuilder->Initialize(params, outputDir, databaseName, chunkSize);
        if (!success)
        {
            LOGERROR("Failed to initialize RAG builder");
            SendProgressUpdate("Failed to initialize RAG builder", 0);

            // Notify the UI of failure
            std::wostringstream responseJson;
            responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"buildResponse\",";
            responseJson << L"\"success\":false,\"message\":\"Failed to initialize RAG builder\"}";
            SendMessageToWebView(responseJson.str());

            _ragBuildInProgress = false;
            return;
        }

        // Process documents - pass the progress callback
        SendProgressUpdate("Processing documents...", 10);
        success = _ragBuilder->ProcessDocumentDirectory(docsDir, [this](const std::string& status, int progressPercent) {
            // Map progress from ProcessDocumentDirectory to 10-30% range
            int adjustedProgress = 10 + (progressPercent * 20) / 100;
            SendProgressUpdate(status, adjustedProgress);
        });

        if (!success)
        {
            LOGERROR("Failed to process documents");

            // Notify the UI of failure
            std::wostringstream responseJson;
            responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"buildResponse\",";
            responseJson << L"\"success\":false,\"message\":\"Failed to process documents\"}";
            SendMessageToWebView(responseJson.str());

            _ragBuildInProgress = false;
            return;
        }

        // Build the database - pass the progress callback
        SendProgressUpdate("Building RAG database...", 30);
        success = _ragBuilder->BuildDatabase([this](const std::string& status, int progressPercent) {
            // Map progress from BuildDatabase to 30-90% range
            int adjustedProgress = 30 + (progressPercent * 60) / 100;
            SendProgressUpdate(status, adjustedProgress);
        });

        if (!success)
        {
            LOGERROR("Failed to build RAG database");

            // Notify the UI of failure
            std::wostringstream responseJson;
            responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"buildResponse\",";
            responseJson << L"\"success\":false,\"message\":\"Failed to build RAG database\"}";
            SendMessageToWebView(responseJson.str());

            _ragBuildInProgress = false;
            return;
        }

        // Report saving progress
        SendProgressUpdate("Saving RAG database...", 90);

        // Get metadata for reporting
        const RagMetadata& metadata = _ragBuilder->GetMetadata();

        // Build a metadata JSON object
        nlohmann::json metadataJson;
        metadataJson["Document Count"] = metadata.documentCount;
        metadataJson["Chunk Count"] = metadata.chunkCount;
        metadataJson["Chunk Size"] = metadata.chunkSize;
        metadataJson["Total Tokens"] = metadata.totalTokens;
        metadataJson["Documents Processed"] = {
            {"DOCX", metadata.docxCount}, {"TXT", metadata.txtCount}, {"LOG", metadata.logCount}, {"PDF", metadata.pdfCount}};
        metadataJson["Vector DB Dimension"] = metadata.dimension;
        metadataJson["Time Taken"] = std::to_string(metadata.embeddingTime) + " seconds";

        // Notify the UI of success with metadata
        std::wostringstream responseJson;
        responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"buildResponse\",";
        responseJson << L"\"success\":true,\"message\":\"RAG database built successfully\",";
        responseJson << L"\"metadata\":" << Utf8ToWide(metadataJson.dump()) << L"}";
        SendMessageToWebView(responseJson.str());

        // Final progress update
        SendProgressUpdate("RAG database built successfully", 100);

        // Try to initialize the RAG database in the LLM service if it's available
        if (_llmService)
        {
            _llmService->InitializeRagDatabase(databaseName);
        }
    }
    catch (const std::exception& e)
    {
        LOGERROR("Exception during RAG build: " + std::string(e.what()));

        // Notify the UI of failure
        std::wostringstream responseJson;
        responseJson << L"{\"type\":\"ragBuilder\",\"action\":\"buildResponse\",";
        responseJson << L"\"success\":false,\"message\":\"Exception during RAG build: " << EscapeJsonStringW(Utf8ToWide(e.what()))
                     << L"\"}";
        SendMessageToWebView(responseJson.str());
    }

    // Reset state
    _ragBuildInProgress = false;
}

/*++

Routine Description:

    Sends a progress update message to the WebView.

Arguments:

    status - Text description of the current status.
    progressPercent - Progress percentage (0-100).

Return Value:

    None.

--*/
void WebViewMessageHandler::SendProgressUpdate(_In_ const std::string& status, _In_ int progressPercent)
{
    LOGINFO("Build progress: " + status + " (" + std::to_string(progressPercent) + "%)");

    std::wostringstream progressJson;
    progressJson << L"{\"type\":\"ragBuilder\",\"action\":\"progress\",";
    progressJson << L"\"status\":\"" << EscapeJsonStringW(Utf8ToWide(status)) << L"\",";
    progressJson << L"\"percent\":" << progressPercent << L"}";
    SendMessageToWebView(progressJson.str());
}

/*++

Routine Description:

    Handles messages related to selecting the RAG database.

Arguments:

    message - JSON message containing RAG selection command.

Return Value:

    None.

--*/
void WebViewMessageHandler::HandleRagSelectionMessage(const nlohmann::json& message)
{
    LOGINFO("Handling RAG selection message");

    try
    {
        if (!message.contains("action") || !message["action"].is_string())
        {
            LOGERROR("RAG selection message missing action field");
            return;
        }

        std::string action = message["action"];
        LOGINFO("RAG selection action: " + action);

        if (action == "toggle")
        {
            if (message.contains("name") && message["name"].is_string())
            {
                auto ragDB = message["name"].get<std::string>();
                LOGINFO("RAG database selected: " + ragDB);
                auto ragListPath = "./configuration/rag.json";

                // Read the current configuration
                std::ifstream jsonFile(ragListPath);
                if (!jsonFile.is_open())
                {
                    LOGERROR("Failed to open rag.json file.");
                    return;
                }
                nlohmann::json configJson;
                jsonFile >> configJson;
                jsonFile.close();

                // Update the defaultDatabase field
                configJson["defaultDatabase"] = ragDB;

                // Save the updated configuration back to the file
                std::ofstream outFile(ragListPath);
                if (!outFile.is_open())
                {
                    LOGERROR("Failed to write to rag.json file.");
                    return;
                }
                outFile << configJson.dump(4); // Pretty print with 4 spaces
                outFile.close();

                // Notify UI with success and updated settings
                AppConfig::getInstance().init();
                _llmService->InitializeRagDatabase(AppConfig::getInstance().getDefaultRagDatabase());
                SendSettingsToWebView();
            }
        }
        else
        {
            LOGERROR("Unknown RAG selection action: " + action);
        }
    }
    catch (const std::exception& e)
    {
        LOGERROR("Exception handling RAG selection message: " + std::string(e.what()));
    }
}
