/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    LlmService.h

Abstract:

    This module define the LLM service class for the AIMX application.
    Provides interface for comunicating with the backend LLM service.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 03/20/2025

--*/

#pragma once

#include "pch.h"

#include "LlmClient.h"
#include "RAGHelper.h"
#include "AppConfig.h"

// Callback type for streaming responses
using StreamingResponseCallback = std::function<void(const std::wstring&, bool)>;

//
// LLM endpoint URL 
// Note: This URL is a placeholder and should be replaced with the actual endpoint URL in production.
// Currently it is set at Rupo's devbox IP address.
//
constexpr const wchar_t* LLM_ENDPOINT_URL = L"http://*************:8080/completion";

class LlmService : public LlmClient
{
public:
    LlmService();
    LlmService(_In_  const AppConfig& config);
    ~LlmService();

    // RAG functionality
    bool
    InitializeRagDatabase(_In_ const std::string& dbName);
    
    bool
    IsRagInitialized() const;
    
    bool
    SendPromptWithRag(
        _In_ const std::wstring& prompt,
        _Out_ std::wstring& response
        );
    
    bool
    StreamPromptWithRag(
        _In_ const std::wstring& prompt,
        _In_ StreamingResponseCallback callback
        );

private:

    // RAG-related methods
    std::wstring
    RetrieveRagContext(
        _In_ const std::wstring& query
        );
    
    std::string m_ragDatabaseName;
    model_params m_embeddingParams;
};
