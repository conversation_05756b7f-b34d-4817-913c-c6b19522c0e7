/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    pch.hxx

Abstract:
    Precompiled header for AIMX service, includes all necessary system and project headers.

Author:

    <PERSON> (SNAKE FIGHTER) (linda<PERSON>p) 06/03/2025

--*/

#pragma once

// Base OS headers
#include <nt.h>
#include <ntrtl.h>
#include <nturtl.h>
#include <ntseapi_x.h>
#include <ntdef.h>
#include <ntintsafe.h>
#include <ntstrsafe.h>
#include <windows.h>
#include <wchar.h>
#include <stdio.h>
#include <stdlib.h>
#include <rpc.h>
#include <rpcdce.h>


// C++ headers and project includes
#include "aimxrpc.h"
#include "aimxrpcclient.h"
#include "memory.h"
#undef WPP_CONTROL_GUIDS
#include "clientwpp.h"
