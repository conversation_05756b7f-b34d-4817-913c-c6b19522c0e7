/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    RagJobManager.cpp

Abstract:

    This module implements the job management class for the RAG service.
    Tracks and manages asynchronous RAG build jobs.

Author:

    <PERSON><PERSON><PERSON> (r<PERSON><PERSON>) 04/12/2025

--*/

#include "RagJobManager.h"
#include "../common/debug.h"
#include <chrono>
#include <sstream>
#include <iomanip>

/*++

Routine Description:

    Converts a job status to JSON.

Arguments:

    None.

Return Value:

    json - JSON representation of the job status.

--*/
json
RagJobStatus::ToJson() const
{
    json docTypes;
    for (const auto& [type, count] : documentTypes)
    {
        docTypes[type] = count;
    }

    return
    {
        {"status", status},
        {"progress", progress},
        {"message", message},
        {"completed", completed},
        {"using_gpu", usingGpu},
        {"document_count", documentCount},
        {"document_types", docTypes},
        {"chunk_count", chunkCount},
        {"total_tokens", totalTokens}
    };
}

/*++

Routine Description:

    Constructor for the RagJobManager class.

Arguments:

    None.

Return Value:

    None.

--*/
RagJobManager::RagJobManager()
{
    LOGINFO("RagJobManager: Initializing");
}

/*++

Routine Description:

    Destructor for the RagJobManager class.

Arguments:

    None.

Return Value:

    None.

--*/
RagJobManager::~RagJobManager()
{
    LOGINFO("RagJobManager: Destroying");
}

/*++

Routine Description:

    Creates a new job and returns its ID.

Arguments:

    None.

Return Value:

    std::string - The ID of the new job.

--*/
std::string
RagJobManager::CreateJob()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    std::string jobId = GenerateJobId();

    // Create and initialize job status
    RagJobStatus status;
    status.status = "created";
    status.progress = 0.0;
    status.message = "Job created";
    status.completed = false;

    // Store the job
    m_jobs[jobId] = status;

    LOGINFO("RagJobManager: Created job with ID:" + jobId);

    return jobId;
}

/*++

Routine Description:

    Updates the status of an existing job.

Arguments:

    jobId - The ID of the job to update.
    status - The new status.

Return Value:

    None.

--*/
void
RagJobManager::UpdateJobStatus(
    const std::string& jobId,
    const RagJobStatus& status
)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = m_jobs.find(jobId);
    if (it == m_jobs.end())
    {
        LOGERROR("RagJobManager: Attempted to update non-existent job: " + jobId);
        return;
    }

    // Update the job status
    m_jobs[jobId] = status;

    LOGINFO("RagJobManager: Updated job " + jobId + " status to " + status.status + ", progress: " + (std::ostringstream() << std::fixed << std::setprecision(2) << status.progress).str());
}

/*++

Routine Description:

    Gets the status of a job.

Arguments:

    jobId - The ID of the job.

Return Value:

    json - JSON representation of the job status.

--*/
json
RagJobManager::GetJobStatus(
    const std::string& jobId
)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = m_jobs.find(jobId);
    if (it == m_jobs.end())
    {
        LOGERROR("RagJobManager: Attempted to get non-existent job: ", jobId.c_str());
        return json::object();
    }

    return it->second.ToJson();
}

/*++

Routine Description:

    Checks if a job exists.

Arguments:

    jobId - The ID of the job.

Return Value:

    bool - True if the job exists, false otherwise.

--*/
bool
RagJobManager::HasJob(
    const std::string& jobId
)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_jobs.find(jobId) != m_jobs.end();
}

/*++

Routine Description:

    Gets all jobs.

Arguments:

    None.

Return Value:

    json - JSON representation of all jobs.

--*/
json
RagJobManager::GetAllJobs()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    json allJobs = json::object();

    for (const auto& [jobId, status] : m_jobs)
    {
        allJobs[jobId] = status.ToJson();
    }

    return allJobs;
}

/*++

Routine Description:

    Gets the count of active jobs.

Arguments:

    None.

Return Value:

    int - The count of active jobs.

--*/
int
RagJobManager::GetActiveJobCount()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    int activeCount = 0;
    for (const auto& [jobId, status] : m_jobs)
    {
        if (!status.completed)
        {
            activeCount++;
        }
    }

    return activeCount;
}

/*++

Routine Description:

    Gets the total count of jobs.

Arguments:

    None.

Return Value:

    int - The total count of jobs.

--*/
int
RagJobManager::GetTotalJobCount()
{
    std::lock_guard<std::mutex> lock(m_mutex);
    return static_cast<int>(m_jobs.size());
}

/*++

Routine Description:

    Generates a unique job ID.

Arguments:

    None.

Return Value:

    std::string - A unique job ID.

--*/
std::string
RagJobManager::GenerateJobId()
{
    // Get current time
    auto now = std::chrono::system_clock::now();
    auto time = std::chrono::system_clock::to_time_t(now);

    // Format job ID as "job_{timestamp}"
    std::stringstream ss;
    ss << "job_" << time;

    return ss.str();
}