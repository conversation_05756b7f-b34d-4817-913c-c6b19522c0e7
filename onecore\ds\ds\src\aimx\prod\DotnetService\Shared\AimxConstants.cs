/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    AimxConstants.cs

Abstract:

    Centralized constants for AIMX services to avoid hardcoded values.
    Contains service endpoints, timeouts, file paths, and configuration values.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/29/2025

Environment:

    User mode only.

--*/

namespace AimxShared.Constants;

/// <summary>
/// Centralized constants for AIMX services to avoid hardcoded values throughout the codebase.
/// This file contains all URLs, endpoints, ports, timeouts, and other configuration constants
/// used across NetRagService and IntentPlanningService projects.
/// </summary>
public static class AimxConstants
{
    #region Service Endpoints and URLs
    
    /// <summary>
    /// Default service endpoints and URLs
    /// </summary>
    public static class ServiceEndpoints
    {
        // Local service hosts
        public const string LocalHost = "localhost";
        
        // NetRag Service
        public const string NetRagServiceBaseUrl = "http://localhost:5000";
        public const int NetRagServicePort = 5000;
        
        // Intent Planning Service
        public const string IntentPlanningServiceBaseUrl = "http://localhost:8082";
        public const int IntentPlanningServicePort = 8082;
        
        // Tool Manager Service
        public const string ToolManagerServiceBaseUrl = "http://localhost:8083";
        public const int ToolManagerServicePort = 8083;
        
        // Workflow Engine Service
        public const string WorkflowEngineServiceBaseUrl = "http://localhost:8084";
        public const int WorkflowEngineServicePort = 8084;
        
        // AIMX Server
        public const string AimxServerBaseUrl = "http://localhost:8080";
        public const int AimxServerPort = 8080;
        
        // LLM Service (Foundry Local)
        public const string LlmServiceBaseUrl = "http://*************:5273";
        public const string LlmServiceV1BaseUrl = "http://localhost:5273/v1";
    }
    
    /// <summary>
    /// API endpoint paths
    /// </summary>
    public static class ApiEndpoints
    {

        public const string PowerShellCommandSearch = "/api/powershellcommand/search";        
        public const string PowerShellExecution = "/api/PowerShellExecution/execute";

        // MCP Tools Service endpoints
        public const string McpToolsHealth = "/api/mcptools/health";
        public const string McpToolsRegister = "/api/mcptools/register";
        public const string McpToolsSearch = "/api/mcptools/search";
        public const string McpToolsStatistics = "/api/mcptools/statistics";

        // LLM Service endpoints
        public const string ChatCompletions = "/v1/chat/completions";

        // Intent Planning Service endpoints
        public const string IntentPlanningAnalyze = "/api/IntentPlanning/analyze";
        public const string IntentPlanningTools = "/api/IntentPlanning/tools";
    }

    /// <summary>
    /// Query parameter names for API endpoints
    /// </summary>
    public static class QueryParameters
    {
        public const string Query = "query";
        public const string Limit = "limit";
        public const string TopK = "topK";
        public const string MinScore = "minScore";
        public const string Category = "category";
        public const string Verb = "verb";
        public const string Complexity = "complexity";
    }

    /// <summary>
    /// Default query values and search terms
    /// </summary>
    public static class DefaultQueries
    {
        public const string PowerShellActiveDirectoryCommand = "PowerShell+Active+Directory+command";
        public const string PowerShellGenericCommand = "PowerShell+command";
    }

    #endregion
    
    #region Timeout and Retry Configuration
    
    /// <summary>
    /// Default timeout values in seconds
    /// </summary>
    public static class Timeouts
    {
        // HTTP client timeouts
        public const int DefaultHttpTimeoutSeconds = 30;
        public const int ShortHttpTimeoutSeconds = 10;
        public const int LongHttpTimeoutSeconds = 60;
        
        // Service-specific timeouts
        public const int NetRagServiceTimeoutSeconds = 15;
        public const int LlmServiceTimeoutSeconds = 30;
        public const int ToolManagerTimeoutSeconds = 30;
        public const int WorkflowEngineTimeoutSeconds = 60;
        
        // Request processing timeouts
        public const int RequestTimeoutSeconds = 30;
        public const int AnalysisTimeoutSeconds = 45;
        
        // Service startup/shutdown timeouts (in milliseconds)
        public const int ServiceStartupTimeoutMs = 30000;
        public const int ServiceShutdownTimeoutMs = 10000;
        public const int HealthCheckTimeoutMs = 5000;
    }
    
    /// <summary>
    /// Retry configuration
    /// </summary>
    public static class RetrySettings
    {
        public const int DefaultMaxRetries = 3;
        public const int NetRagServiceMaxRetries = 2;
        public const int LlmServiceMaxRetries = 2;
        public const int ToolManagerMaxRetries = 3;
        public const int WorkflowEngineMaxRetries = 1;
        
        public const int DefaultRetryDelaySeconds = 2;
        public const int LongRetryDelaySeconds = 5;
    }
    
    #endregion
    
    #region Circuit Breaker Configuration
    
    /// <summary>
    /// Circuit breaker settings
    /// </summary>
    public static class CircuitBreaker
    {
        public const bool DefaultEnableCircuitBreaker = true;
        public const int DefaultFailureThreshold = 5;
        public const int DefaultTimeoutSeconds = 60;
        
        // Service-specific circuit breaker settings
        public const int NetRagServiceFailureThreshold = 5;
        public const int NetRagServiceTimeoutSeconds = 60;
        
        public const int LlmServiceFailureThreshold = 3;
        public const int LlmServiceTimeoutSeconds = 30;
        
        public const int WorkflowEngineFailureThreshold = 2;
        public const int WorkflowEngineTimeoutSeconds = 120;
    }
    
    #endregion
    
    #region File Paths and Directories
    
    /// <summary>
    /// Default file paths and directories
    /// </summary>
    public static class FilePaths
    {
        // Data subfolder for organized file management
        public const string DataFolder = "data";

        // Model and data paths (now in data subfolder)
        public const string DefaultModelPath = @"data\model.onnx";
        public const string DefaultVocabPath = @"data\vocab.txt";
        public const string DefaultDatabasePath = @"data\database.db";

        // System paths
        public const string ProgramDataAimxPath = @"C:\ProgramData\Microsoft\aimx";
        public const string ModelsJinaPath = @"C:\ProgramData\Microsoft\aimx\Models\jina";
        public const string NetRagServicePath = @"C:\ProgramData\Microsoft\AIMX\NetRagService\NetRagService.exe";

        // Model files (fallback paths for system-wide installation)
        public const string JinaModelPath = @"C:\ProgramData\Microsoft\aimx\Models\jina\model.onnx";
        public const string JinaVocabPath = @"C:\ProgramData\Microsoft\aimx\Models\jina\vocab.txt";

        // Data files
        public const string ComprehensiveDatasetPath = @"data\comprehensive_ad_commands_dataset_final.json";
    }
    
    #endregion
    
    #region Model and AI Configuration
    
    /// <summary>
    /// AI model configuration constants
    /// </summary>
    public static class ModelConfig
    {
        // LLM Model names
        public const string DefaultLlmModel = "Phi-3.5-mini-instruct-cuda-gpu";
        public const string AlternateLlmModel = "qwen2.5-0.5b-instruct-generic-gpu";
        public const string CurrentLlmModel = "Phi-4-mini-instruct-cuda-gpu";
        
        // Service IDs
        public const string DefaultServiceId = "phi-3-mini";
        public const string AlternateServiceId = "qwen2.5-0.5b";
        
        // Token limits
        public const int DefaultMaxTokens = 512;
        public const int LargeMaxTokens = 4096;
        public const int SmallMaxTokens = 100;
        
        // Temperature settings
        public const float DefaultTemperature = 0.1f;
        public const float HighTemperature = 0.7f;

        // LLM Request parameters
        public const int TopK = 40;
        public const float TopP = 0.9f;
        
        // Vector and embedding settings
        public const int DefaultVectorSize = 768;
        public const int AlternateVectorSize = 384;
        
        // Confidence thresholds
        public const double DefaultConfidenceThreshold = 0.7;
        public const double HighConfidenceThreshold = 0.95;
        public const double MinConfidenceThreshold = 0.1;
    }
    
    #endregion
    
    #region Search and Query Limits
    
    /// <summary>
    /// Search and query configuration
    /// </summary>
    public static class SearchLimits
    {
        // Default search limits
        public const int DefaultSearchLimit = 5;
        public const int DefaultTopK = 10;
        public const int MaxTopK = 100;
        public const int MaxSearchLimit = 20;
        
        // RAG-specific limits
        public const int RagSearchTopResults = 3;
        public const float DefaultMinScore = 0.0f;
        
        // Document processing
        public const int DefaultChunkSize = 300;
        public const int DefaultChunkOverlap = 60;
        
        // Cache settings
        public const int DefaultCacheCapacity = 1000;
        public const int VectorStoreInitialCapacity = 10000;
        public const int CacheExpirationMinutes = 60;
    }
    
    #endregion
    
    #region Service Configuration
    
    /// <summary>
    /// Service-level configuration constants
    /// </summary>
    public static class ServiceConfig
    {
        // Concurrency settings
        public const int DefaultMaxConcurrentRequests = 100;
        
        // Feature flags
        public const bool DefaultEnableCors = true;
        public const bool DefaultEnableSwagger = true;
        public const bool DefaultEnableCaching = true;
        public const bool DefaultEnableStatistics = true;
        public const bool DefaultEnableRiskAssessment = true;
        
        // Service names
        public const string IntentPlanningServiceName = "IntentPlanningService";
        public const string NetRagServiceName = "NetRagService";
        public const string McpToolsServiceName = "MCP Tools RAG Service";
        
        // Version information
        public const string DefaultServiceVersion = "1.0.0";
        
        // Registry configuration
        public const string ServiceRegistryArgs = "--service";
    }
    
    #endregion
}
