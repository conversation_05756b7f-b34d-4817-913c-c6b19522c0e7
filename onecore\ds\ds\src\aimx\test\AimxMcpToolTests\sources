TARGETNAME=AimxMcpSrvTaef
TARGETTYPE=DYNLINK
TARGET_DESTINATION=retail

TEST_CODE=1

USE_MSVCRT=1
USE_UNICRT=1
USE_STL=1
STL_VER=STL_VER_CURRENT

C_DEFINES=$(C_DEFINES) -DWIN32 -D_WIN32 -DUNICODE -D_UNICODE -DADMCP_TEST_BUILD

# Disable these effing warnings for string conversion issues and enable exception handling
MSC_WARNING_LEVEL=/W3 /EHsc /wd4244 /wd4267

PRECOMPILED_INCLUDE = pch.hxx
COMPILE_CXX = 1

# Enable C++ exception handling with proper unwind semantics
USE_NATIVE_EH=1

DLLENTRY=_DllMainCRTStartup

SOURCES=\
    AimxMcpToolTests.cpp \

INCLUDES=\
    ..\..\prod\common; \
    ..\..\prod\common\nlohmann; \
    ..\..\prod\common\httplib; \
    ..\..\prod\admcpsrv; \
    $(OBJ_PATH); \
    $(OBJ_PATH)\$(O); \
    $(BASE_INC_PATH); \
    $(INTERNAL_SDK_INC_PATH); \
    $(ONECORE_INTERNAL_SDK_INC_PATH); \
    $(ONECORE_INTERNAL_PRIV_SDK_INC_PATH_L); \
    $(ONECORESDKTOOLS_INTERNAL_INC_PATH_L)\wextest\cue; \
    $(MINWIN_INTERNAL_PRIV_SDK_INC_PATH_L); \
    $(ONECORE_EXTERNAL_SDK_INC_PATH_L); \
    $(PROJECT_INTERNAL_SDK_METADATA_PATH)\cppwinrt; \
    $(ONECOREUAPWINDOWS_RESTRICTED_INC_PATH_L); \
    $(OBJ_PATH)\..\..\prod\aimxsrv\idl\$(O); \

TARGETLIBS_PROJECT=\
    $(OBJ_PATH)\..\..\prod\admcpsrv\lib\$(O)\admcpsrv.lib \
    $(OBJ_PATH)\..\..\prod\llmclientlib\$(O)\llmclientlib.lib \

TARGETLIBS=\
    $(TARGETLIBS_PROJECT) \
    $(MINWIN_EXTERNAL_SDK_LIB_PATH_L)\ntdll.lib                            \
    $(MINWIN_EXTERNAL_SDK_LIB_PATH_L)\rpcrt4.lib                           \
    $(MINWIN_EXTERNAL_SDK_LIB_PATH_L)\ws2_32.lib                           \
    $(MINCORE_EXTERNAL_SDK_LIB_VPATH_L)\mincore.lib                        \
    $(ONECORESDKTOOLS_INTERNAL_LIB_PATH_L)\WexTest\Cue\Wex.Common.lib      \
    $(ONECORESDKTOOLS_INTERNAL_LIB_PATH_L)\WexTest\Cue\Wex.Logger.lib      \
    $(ONECORESDKTOOLS_INTERNAL_LIB_PATH_L)\WexTest\Cue\Te.Common.lib       \

MUI_VERIFY_NO_LOC_RESOURCE=1
