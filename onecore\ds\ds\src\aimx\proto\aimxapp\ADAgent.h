/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    ADAgent.h

Abstract:

    Header file for ADAI Agent.

    aimx will pass the prompt to the ADAI Agent.
    The ADAI Agent will process the prompt and try to determine if any action is needed by mapping the prompt to a functional task.
    If an action is needed, it will return the name of the function to execute.
    If no action is needed, it will return none.
    The ADAI agent will then execute the function.
    The function will run a command, call API's and/or perform any other action needed to complete the task.
    The function will return the result to the ADAI Agent.
    The agent will then process the result and return it to aimx.
    The ADAI Agent will then return the result to aimx.

Author:

    <PERSON> (SNAKE FIGHTER) (lindakup) 03-28-2025

--*/
#pragma once

#include "llmService.h"

    // A list of possible actions that the ADAI Agent can take
    enum class ActionType {
        None = 0,
        CheckDCHealth,
        GetDCInfo,
        GetDomainInfo,
        ListDomainControllers,
        ActionInvalid,
        // Add more actions as needed
    };

class ADAgent
{
private:

    
public:
    ADAgent(/* args */);
    ~ADAgent();

    bool Initialize();

    bool 
    ProcessPromptStreaming(
        _In_ const std::wstring& prompt,
        _In_ bool useRag,
        _In_ StreamingResponseCallback streamCallback
    );

    bool
    ProcessPrompt(
        _In_ const std::wstring& wstrPrompt,
        _Out_ std::wstring& wstrResponse
    );

    void
    CheckPromptForAction(
        _In_ const std::wstring& wstrPrompt,
        _Out_ ActionType* action
    );

    bool
    TakeAction(
        _In_ const ActionType action,
        _In_ const std::wstring& wstrParameters,
        _Out_ std::wstring& wstrActionResult
    );
};
