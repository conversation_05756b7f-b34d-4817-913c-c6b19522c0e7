/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    ADAgent.cpp

Abstract:

    This module implements a simple ADAI Agent for the AIMX application.

Author:

    <PERSON> (SNAKE FIGHTER) (lindakup) 03-28-2025

--*/
#include "pch.h"
#include "LlmService.h"
#include "WebViewMessageHandler.h"

ADAgent::ADAgent(/* args */)
{
}

ADAgent::~ADAgent()
{
}


bool
ADAgent::Initialize()
/*++

Routine Description:

    Initializes the ADAI Agent. This function is called when the agent is created.

Arguments:

    none

Return Value:

    true if successful, false otherwise.

--*/
{
    // For now, we will just return true to indicate successful initialization
    // SNAKE_TODO: Add initialization logic here and call it during startup.
    return true;
}



bool
ADAgent::ProcessPrompt(
    _In_ const std::wstring& wstrPrompt,
    _Out_ std::wstring& wstrResponse
    )
/*++

Routine Description:

    Processes a prompt and determines if any action is needed.
    If an action is needed, it will take the action and return the result.

Arguments:

    wstrPrompt - The prompt to process.
    wstrResponse - The response to return to the caller.

Return Value:

    true if successful, false otherwise.

--*/
{
    bool result = false;
    std::wstring ActionResult;
    ActionType action = ActionType::None;

    if (wstrPrompt.empty())
    {
        LOGINFO(L"Prompt is empty.");
        // return the same prompt as the response
        wstrResponse = wstrPrompt;
        goto Exit;
    }

    // Check if the prompt requires any action.
    // Note that once we have function calling, this will be different and the agent will need to
    // update the prompt to include the tools for the LLM function calling.
    // The agent will also need to check the LLM response to see if it contains a function call and execute it.
    // For now, we will just check if the prompt contains specific keywords and do the execution up front.
    // This is a temporary solution until we have function calling working.
    CheckPromptForAction(wstrPrompt, &action);
    if (ActionType::None == action) // No action needed
    {
        LOGINFO(L"No action needed for prompt: " + wstrPrompt);
        // no-op for the agent.
        wstrResponse = wstrPrompt;
        result = true;
        goto Exit;
    }

    // If an action is needed, take the action and get the result
    if (!TakeAction(action, wstrPrompt, ActionResult))
    {
        // cast action to a ulong
        // Log the error
        LOGERROR(L"Failed to take action: " + std::to_wstring(static_cast<unsigned long>(action)));
        wstrResponse = L"Summarise the following sentence. The call failed.";
        goto Exit;
    }
    else
    {
        LOGINFO(L"Action taken successfully: " + std::to_wstring(static_cast<unsigned long>(action)));
        // Set the response to the result of the action taken
        wstrResponse = (L"Summarise the following information in a nice table." + ActionResult);
        LOGINFO(L"Action taken:, response: " + wstrResponse);
        result = true;
    }

Exit:
    return result;
}

void
ADAgent::CheckPromptForAction(
    _In_ const std::wstring& wstrPrompt,
    _Out_ ActionType* action 
    )
/*++

Routine Description:

    Maps the prompt to an action that the ADAI Agent can take.
    This is a simple function that checks the prompt for specific keywords.
    In the future it can be extended to check for fn calling.AddAuditAccessObjectAce

Arguments:

    wstrPrompt - The prompt to check for keywords.
    action - returns the action to take.

Return Value:

    none. This is guaranteed to return an action.
    If the action is not found, it will return None.

--*/
{
    *action = ActionType::None;
    if (wstrPrompt.empty())
    {
        LOGINFO(L"ADAgent::CheckPromptForAction: Prompt is empty.");
        goto Exit;
    }

    //
    // For now, we will just check if the prompt contains specific keywords
    // and set the action accordingly. This can be improved with more complex logic.
    // Ideally function calling will do this for us eventually. This is just a prototype.
    //
    if (FindStringCaseInsensitive(wstrPrompt, L"health") &&
        (FindStringCaseInsensitive(wstrPrompt, L"DCName:")))
    {
        *action = ActionType::CheckDCHealth;
        goto Exit;
    }

    // List domain controllers in domain
    if (FindStringCaseInsensitive(wstrPrompt, L"list") &&
        FindStringCaseInsensitive(wstrPrompt, L"domain controllers") &&
        FindStringCaseInsensitive(wstrPrompt, L"Domain:"))
    {
        *action = ActionType::ListDomainControllers;
        goto Exit;
    }

    // Domain Controller information
    if (FindStringCaseInsensitive(wstrPrompt, L"DCName:") &&
        (FindStringCaseInsensitive(wstrPrompt, L"information")))
    {
        *action = ActionType::GetDCInfo;
        goto Exit;
    }


Exit:
    LOGINFO(L"ADAgent::CheckPromptForAction: Action determined " + std::to_wstring(static_cast<unsigned long>(*action)));
}

bool
ADAgent::TakeAction(
    _In_ const ActionType action,
    _In_ const std::wstring& wstrParameters,
    _Out_ std::wstring& wstrActionResult
    )
/*++

Routine Description:

    A function that calls the appropriate function based on the action provided.

Arguments:

    action - The action to take.
    wstrParameters - The parameters to pass to the function (This is currently the entire prompt)
    wstrActionResult - The result of the action taken.

Return Value:

    true if successful, false otherwise.

--*/
{
    bool result = false;
    wstrActionResult.clear();
    std::unique_ptr<ADAgentTools> tools = std::make_unique<ADAgentTools>();

    switch (action)
    {
    case ActionType::None:
        result = true;
        goto Exit;

    case ActionType::CheckDCHealth:
        {
            // Call the function to check domain controller health
            std::wstring healthStatus;
            if (!tools->CheckDomainControllerHealth(wstrParameters, healthStatus))
            {
                LOGERROR(L"Failed to check domain controller health.");
                // Set the result to indicate failure
                wstrActionResult = L"Failed to check domain controller health.";
                return false;
            }
            wstrActionResult = healthStatus;
        }
        break;

    case ActionType::GetDCInfo:
        {
            // Call the function to get domain controller information
            std::wstring dcInfo;
            if (!tools->GetDomainControllerInformation(wstrParameters, dcInfo))
            {
                // Set the result to indicate failure
                wstrActionResult = L"Failed to get domain controller information.";
                LOGERROR(L"Failed to get domain controller information.");
                return false;
            }
            wstrActionResult = dcInfo;
        }
        break;

    case ActionType::ListDomainControllers:
        {
            // Call the function to list domain controllers in the domain
            std::wstring dcListString;
            if (!tools->ListDomainControllersInDomain(wstrParameters, dcListString))
            {
                // Set the result to indicate failure
                wstrActionResult = L"Failed to list domain controllers.";
                LOGERROR(L"Failed to list domain controllers.");
                return false;
            }
            wstrActionResult = dcListString;
        }
        break;

    default:
        LOGERROR(L"Unknown action:" + std::to_wstring(static_cast<unsigned long>(action)));
        goto Exit;
    }

    // Success.
    result = true;

Exit:
    return result;

}