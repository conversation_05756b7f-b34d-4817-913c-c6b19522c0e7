/**
 * styles.css - Main stylesheet for aimx WebView interface
 * 
 * This file contains all styles for the aimx WebView chat interface,
 * including message containers, animations, typing indicators, and
 * general layout styling.
 * 
 * @version 1.0
 */

:root {
    --primary-bg: #202020;
    --secondary-bg: #2D2D2D;
    --tertiary-bg: #3D3D3D;
    --primary-text: #FFFFFF;
    --secondary-text: #AAAAAA;
    --accent-color: #0078D7;
    --user-message-bg: #0078D7;
    --bot-message-bg: #3D3D3D;
    --border-radius: 8px;
    --citation-background: rgba(0, 120, 215, 0.1);
    --citation-border: rgba(0, 120, 215, 0.3);
    --nav-bg: #1A1A1A;
    --nav-hover: #333333;
    --nav-active: #0078D7;
    --nav-active-bg: rgba(0, 120, 215, 0.1);
    --card-bg: #2D2D2D;
}

/* Global styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #1e1e1e;
    color: #f0f0f0;
    height: 100vh;
    overflow: hidden;
}

/* Main layout */
.app-container {
    display: flex;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
}

.sidebar {
    width: 250px;
    background-color: #252526;
    border-right: 1px solid #333;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 15px;
    text-align: center;
    border-bottom: 1px solid #333;
}

.app-title {
    font-size: 24px;
    color: #0078d4;
}

.nav-container {
    flex: 1;
    padding: 10px;
}

.nav-item {
    padding: 12px 15px;
    border-radius: 6px;
    margin-bottom: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: background-color 0.2s;
}

.nav-item:hover {
    background-color: #333;
}

.nav-item.active {
    background-color: #0078d4;
}

.nav-icon {
    margin-right: 10px;
    font-size: 18px;
}

.nav-label {
    font-size: 16px;
}

.sidebar-footer {
    padding: 15px;
    border-top: 1px solid #333;
}

.toggle-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.toggle-label {
    font-size: 14px;
}

.endpoint-info {
    font-size: 12px;
    color: #999;
    word-break: break-all;
}

/* Switch toggle */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #444;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: #0078d4;
}

input:focus + .slider {
    box-shadow: 0 0 1px #0078d4;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 24px;
}

.slider.round:before {
    border-radius: 50%;
}

/* Main content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
}

.page-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    flex-direction: column;
}

.page-container.active {
    display: flex;
}

/* Chat UI */
.chat-area {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.input-area {
    padding: 15px;
    background-color: #252526;
    border-top: 1px solid #333;
    display: flex;
}

textarea {
    flex: 1;
    padding: 12px;
    border-radius: 6px 0 0 6px;
    background-color: #333;
    color: #f0f0f0;
    border: 1px solid #444;
    resize: none;
    font-family: inherit;
    font-size: 15px;
}

textarea:focus {
    outline: none;
    border-color: #0078d4;
}

#send-button {
    padding: 0 15px;
    background-color: #0078d4;
    color: white;
    border: none;
    border-radius: 0 6px 6px 0;
    cursor: pointer;
    font-weight: bold;
}

#send-button:hover {
    background-color: #0063ad;
}

/* Message styling */
.message-container {
    margin-bottom: 20px;
    display: flex;
}

.message-container.user {
    justify-content: flex-end;
}

.message {
    padding: 12px 16px;
    border-radius: 10px;
    max-width: 80%;
    word-wrap: break-word;
}

.user .message {
    background-color: #0078d4;
    color: white;
    border-radius: 18px 18px 0 18px;
}

.assistant .message {
    background-color: #333;
    color: #f0f0f0;
    border-radius: 18px 18px 18px 0;
}

.error .message {
    background-color: #d32f2f;
    color: white;
    border-radius: 10px;
    text-align: center;
    max-width: 100%;
    margin: 0 auto;
}

/* Code formatting */
.message code {
    background-color: #1e1e1e;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: Consolas, monospace;
    font-size: 0.9em;
}

/* Settings page */
.settings-section {
    background-color: #292929;
    border-radius: 8px;
    padding: 15px;
    margin: 15px;
}

.settings-section h3 {
    margin-bottom: 15px;
    color: #0078d4;
}

.setting-item {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.setting-item label {
    width: 120px;
}

.setting-item select, .setting-item input {
    padding: 8px;
    background-color: #333;
    color: #f0f0f0;
    border: 1px solid #444;
    border-radius: 4px;
}

/* Debug/RAG Builder pages */
#debug-container, #rag-builder-container {
    width: 100%;
    height: 100%;
    padding: 0;
    overflow: auto;
}

#debugFrame {
    border: none;
    width: 100%;
    height: 100%;
}

.loading-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    font-size: 18px;
    color: #999;
}

.error-message {
    color: #ff6b6b;
    text-align: center;
    padding: 20px;
}

/* Custom styling when rag-builder is loaded inline */
#rag-builder-container .container {
    padding: 20px;
    max-width: 100%;
}

#rag-builder-container h1 {
    color: #0078d4;
    margin-bottom: 20px;
}

#rag-builder-container .section {
    background-color: #292929;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

#rag-builder-container .form-group {
    margin-bottom: 15px;
}

#rag-builder-container label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

#rag-builder-container input[type="text"], 
#rag-builder-container input[type="number"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #444;
    border-radius: 4px;
    background-color: #333;
    color: #fff;
}

#rag-builder-container button {
    padding: 10px 15px;
    background-color: #0078d4;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 8px;
    margin-bottom: 8px;
}

#rag-builder-container button:hover {
    background-color: #0063ad;
}

#rag-builder-container button:disabled {
    background-color: #666;
    cursor: not-allowed;
}

/* Navigation Pane */
.nav-pane {
    width: 250px;
    background-color: var(--nav-bg);
    height: 100%;
    overflow-y: auto;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.2);
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
}

.nav-header {
    padding: 16px 20px;
    background-color: var(--accent-color);
    color: white;
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.nav-items {
    list-style: none;
    padding: 10px 0;
    flex-grow: 1;
}

/* Bottom navigation items container */
.nav-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.nav-item {
    padding: 10px 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    border-left: 4px solid transparent;
    transition: all 0.2s ease;
    height: 44px;
}

.nav-item:hover {
    background-color: var(--nav-hover);
}

.nav-item.active {
    background-color: var(--nav-active-bg);
    border-left-color: var(--nav-active);
}

.nav-item-icon {
    margin-right: 12px;
    width: 18px;
    text-align: center;
    font-size: 16px;
}

.nav-item-text {
    font-size: 14px;
    font-weight: 500;
}

/* Content Container and Pages */
.content-container {
    flex: 1;
    overflow: hidden;
    position: relative;
}

.page {
    height: 100%;
    width: 100%;
    display: none;
    overflow-y: auto;
}

.page.active {
    display: block;
}

.page-container {
    padding: 20px;
    height: 100%;
    overflow-y: auto;
}

.page-header {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--tertiary-bg);
    font-size: 24px;
    font-weight: 500;
}

.page-content {
    height: calc(100% - 60px);
}

/* Health Dashboard Page */
.dashboard-container {
    padding: 20px;
    height: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    padding-top: 10px;
}

.dashboard-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 20px;
    min-height: 150px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
}

.dashboard-card h3 {
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 500;
    color: var(--primary-text);
}

.metrics-placeholder, 
.activity-placeholder, 
.resource-placeholder {
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--tertiary-bg);
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    color: var(--secondary-text);
}

/* Existing Chat UI Styles */
.container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chat-container {
    flex-grow: 1;
    padding: 1.5rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.welcome-message {
    margin: auto;
    text-align: center;
    padding: 2rem;
    max-width: 600px;
}

.welcome-message h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    background: linear-gradient(90deg, #0078D7, #00BCF2);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 600;
}

.welcome-message p {
    font-size: 1.2rem;
    color: var(--secondary-text);
}

.messages-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
}

.message {
    display: flex;
    flex-direction: column;
    max-width: 80%;
    animation: fadeIn 0.3s ease-in-out;
}

.user-message {
    align-self: flex-end;
    background-color: var(--user-message-bg);
    border-radius: var(--border-radius);
    padding: 1rem;
}

.bot-message {
    align-self: flex-start;
    background-color: var(--bot-message-bg);
    border-radius: var(--border-radius);
    padding: 1rem;
}

.message-content {
    line-height: 1.5;
}

.bot-message .message-content {
    white-space: pre-wrap;
}

.input-container {
    background-color: var(--secondary-bg);
    padding: 1.5rem;
}

.input-wrapper {
    display: flex;
    align-items: center;
    background-color: var(--tertiary-bg);
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    margin: 0 auto;
    max-width: 800px;
}

textarea {
    flex-grow: 1;
    background: transparent;
    border: none;
    outline: none;
    resize: none;
    color: var(--primary-text);
    font-size: 1rem;
    padding: 0.5rem 0;
    max-height: 120px;
    overflow-y: auto;
}

textarea::placeholder {
    color: var(--secondary-text);
}

.send-button {
    background-color: transparent;
    border: none;
    cursor: pointer;
    color: var(--accent-color);
    padding: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.send-button:hover {
    background-color: rgba(0, 120, 215, 0.1);
}

.disclaimer {
    text-align: center;
    font-size: 0.8rem;
    color: var(--secondary-text);
    margin-top: 1rem;
    max-width: 800px;
    margin: 1rem auto 0;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    padding: 1rem;
    background-color: var(--bot-message-bg);
    border-radius: var(--border-radius);
    align-self: flex-start;
}

.typing-dot {
    width: 8px;
    height: 8px;
    background-color: var(--secondary-text);
    border-radius: 50%;
    animation: typingAnimation 1.5s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
    animation-delay: 0s;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.3s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.6s;
}

@keyframes typingAnimation {
    0%, 100% {
        transform: translateY(0);
        opacity: 0.5;
    }
    50% {
        transform: translateY(-5px);
        opacity: 1;
    }
}

.error-message {
    color: #ff9999;
    white-space: pre-wrap;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 0.9em;
}

.error-label {
    color: #ff6666;
    font-weight: bold;
}

.diagnostics-label {
    color: #66aaff;
    font-weight: bold;
    display: block;
    margin-top: 10px;
    margin-bottom: 5px;
}

.streaming-content {
    position: relative;
}

.typing-cursor {
    display: inline-block;
    width: 0.5em;
    height: 1.2em;
    background-color: var(--primary-text);
    margin-left: 2px;
    vertical-align: bottom;
    animation: blink 1s step-end infinite;
}

@keyframes blink {
    from, to { opacity: 1; }
    50% { opacity: 0; }
}

/* RAG citation styling */
.citation-info {
    font-size: 0.8em;
    margin-top: 8px;
    color: var(--secondary-text);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 8px;
}

.citation-source {
    display: inline-block;
    background-color: var(--citation-background);
    border: 1px solid var(--citation-border);
    border-radius: 4px;
    padding: 2px 6px;
    margin: 2px;
    font-size: 0.9em;
}

/* Notification container */
#notificationContainer {
    position: absolute;
    top: 50px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    pointer-events: none;
    z-index: 1000;
}

.notification {
    background-color: var(--accent-color);
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    pointer-events: auto;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.spinner {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid white;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

/* Modal Dialog */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    align-items: center;
    justify-content: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background-color: var(--secondary-bg);
    margin: auto;
    width: 80%;
    max-width: 800px;
    max-height: 90vh;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: var(--tertiary-bg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
}

.close-button {
    background: transparent;
    border: none;
    color: var(--primary-text);
    font-size: 24px;
    cursor: pointer;
    line-height: 1;
}

.modal-tabs {
    display: flex;
    background-color: var(--tertiary-bg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-button {
    background: transparent;
    border: none;
    color: var(--primary-text);
    padding: 12px 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    border-bottom: 3px solid transparent;
}

.tab-button:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.tab-button.active {
    border-bottom-color: var(--accent-color);
}

.tab-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.tab-pane h3 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 500;
}

.settings-section {
    margin-bottom: 25px;
    background-color: var(--tertiary-bg);
    border-radius: 6px;
    padding: 15px;
}

.settings-section h4 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 500;
}

.settings-option {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.settings-option label {
    margin-left: 8px;
    font-weight: 500;
}

.settings-field-group {
    margin-left: 25px;
    padding-top: 5px;
}

.settings-field {
    display: flex;
    flex-direction: column;
    margin-bottom: 12px;
}

.settings-field label {
    margin-bottom: 5px;
    font-size: 14px;
}

.settings-field input,
.settings-field select {
    padding: 8px 10px;
    background-color: var(--secondary-bg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    color: var(--primary-text);
    font-size: 14px;
}

.settings-field input:focus,
.settings-field select:focus {
    border-color: var(--accent-color);
    outline: none;
}

.modal-footer {
    padding: 15px 20px;
    background-color: var(--tertiary-bg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.primary-button {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
}

.primary-button:hover {
    background-color: #0063b1;
}

.secondary-button {
    background-color: transparent;
    color: var(--primary-text);
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.secondary-button:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Input with status indicator */
.input-with-status {
    display: flex;
    flex-direction: column;
}

.endpoint-status {
    display: flex;
    align-items: center;
    margin-top: 8px;
    font-size: 13px;
}

.status-indicator-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
    background-color: #888;
}

.status-indicator-dot.online {
    background-color: #4CAF50;
    box-shadow: 0 0 5px #4CAF50;
}

.status-indicator-dot.offline {
    background-color: #F44336;
    box-shadow: 0 0 5px #F44336;
}

.status-text {
    color: var(--secondary-text);
}

/* Model info styling */
.model-info {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    padding: 12px;
    margin-top: 12px;
    font-size: 13px;
}

.model-info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.model-info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    color: var(--secondary-text);
}

.info-value {
    font-weight: 500;
}

/* RAG Toggle Button Styles */
.input-controls {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.rag-toggle {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
    font-size: 14px;
}

.rag-toggle input {
    margin-right: 8px;
}

.toggle-label {
    color: #444;
}

/* Fix blurry text issues */
button, input, textarea, select, .message-content {
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
    font-size: 14px;
}

/* Improve text readability */
p, .message-content, .welcome-message p {
    line-height: 1.6;
    font-weight: 400;
    letter-spacing: 0.015em;
}

/* RAG Builder specific styles */
.rag-builder-content {
    width: 100%;
    height: 100%;
    overflow: auto;
    display: block !important; /* Force display */
    background-color: var(--primary-bg);
    position: relative;
}

#rag-builder-page {
    display: none;
    height: 100%;
    width: 100%;
    position: relative;
}

#rag-builder-page.active {
    display: block;
}

/* Fix the container sizing issue */
#rag-builder-container .container {
    height: auto;
    min-height: 100%;
    width: 100%;
    max-width: none;
    padding: 20px;
}

/* Make sure section headings are visible */
#rag-builder-container h1, 
#rag-builder-container h2 {
    color: var(--primary-text);
    margin-bottom: 20px;
    display: block;
}

/* Make section backgrounds more visible */
#rag-builder-container .section {
    background-color: var(--secondary-bg);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    display: block;
}

/* Debug outline to see container boundaries */
#rag-builder-container {
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Add these styles to support dynamic content loading */

.dynamic-content {
    width: 100%;
    height: 100%;
    overflow: auto;
    display: flex;
    flex-direction: column;
}

/* Settings specific styles for when loaded dynamically */
.settings-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: none;
}

.settings-container.active {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Fix for dynamic loading - ensure containers fill space */
#chat-content-container {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Ensure the chat UI fills the container when loaded */
#chat-content-container .container {
    flex: 1;
    display: flex;
    flex-direction: column;
}
