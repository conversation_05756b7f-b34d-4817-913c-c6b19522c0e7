/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    AppConfig.h

Abstract:

    This module defines the application configuration class for AIMX.
    Allows settings for logging, RAG support, prompt length, and output directory to be globally accessible.

Author:

    <PERSON><PERSON> (solaadekunle) 04/24/2025

--*/

#pragma once
#include <string>
#include <vector>
#include <nlohmann/json.hpp>

class AppConfig
{
private:
    bool enableLogging;
    bool enableRagSupport;
    int maxPromptLength;
    std::string defaultRagDatabase;
    std::vector<std::string> ragDatabases;

    AppConfig();

public:
    AppConfig(const AppConfig&) = delete;
    AppConfig& operator=(const AppConfig&) = delete;

    static AppConfig& getInstance();

    void init();

    bool isLoggingEnabled() const;
    bool isRagSupportEnabled() const;
    int getMaxPromptLength() const;
    const std::string& getDefaultRagDatabase() const;
    const std::vector<std::string>& getRagDatabases() const;
};