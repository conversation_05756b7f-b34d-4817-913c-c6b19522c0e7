/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    ADReplicationToolAgentTests.h

Abstract:
    TAEF test class declaration for ADReplicationToolAgent component tests.

Author:
    <PERSON><PERSON><PERSON><PERSON> (pumathur) 06/18/2025

--*/
#pragma once

#include "WexTestClass.h"

using namespace WEX::Logging;
using namespace WEX::Common;
using namespace WEX::TestExecution;

class ADReplicationToolAgentTests : public WEX::TestClass<ADReplicationToolAgentTests>
{
public:
    BEGIN_TEST_CLASS(ADReplicationToolAgentTests)
        TEST_CLASS_PROPERTY(L"TestClass", L"ADReplicationToolAgent")
        TEST_CLASS_PROPERTY(L"Description", L"Tests for the ADReplicationToolAgent component")
    END_TEST_CLASS()
    
    // Test singleton instance creation
    BEGIN_TEST_METHOD(TestSingletonInstance)
        TEST_METHOD_PROPERTY(L"Priority", L"1")
        TEST_METHOD_PROPERTY(L"Description", L"Tests singleton pattern implementation")
    END_TEST_METHOD()

    // Test JSON parsing and action handling
    BEGIN_TEST_METHOD(TestJsonProcessing)
        TEST_METHOD_PROPERTY(L"Priority", L"1")
        TEST_METHOD_PROPERTY(L"Description", L"Tests JSON input/output processing")
    END_TEST_METHOD()

    // Test replication status retrieval for individual DC
    BEGIN_TEST_METHOD(TestGetReplicationStatusForIndividualDC)
        TEST_METHOD_PROPERTY(L"Priority", L"1")
        TEST_METHOD_PROPERTY(L"Description", L"Tests replication status retrieval for individual DC")
    END_TEST_METHOD()

    // Test replication status retrieval for all DCs
    BEGIN_TEST_METHOD(TestGetReplicationStatusForAllDCs)
        TEST_METHOD_PROPERTY(L"Priority", L"2")
        TEST_METHOD_PROPERTY(L"Description", L"Tests replication status retrieval for all DCs in domain")
    END_TEST_METHOD()

    // Test error handling
    BEGIN_TEST_METHOD(TestErrorHandling)
        TEST_METHOD_PROPERTY(L"Priority", L"3")
        TEST_METHOD_PROPERTY(L"Description", L"Tests error handling in replication tools")
    END_TEST_METHOD()
    
    // Test performance characteristics
    BEGIN_TEST_METHOD(TestPerformance)
        TEST_METHOD_PROPERTY(L"Priority", L"3")
        TEST_METHOD_PROPERTY(L"Description", L"Tests performance of replication information retrieval")
    END_TEST_METHOD()    // Test edge cases and input validation
    BEGIN_TEST_METHOD(TestInputValidation)
        TEST_METHOD_PROPERTY(L"Priority", L"2")
        TEST_METHOD_PROPERTY(L"Description", L"Tests input validation and edge cases")
    END_TEST_METHOD()    // Test thread safety with concurrent requests
    BEGIN_TEST_METHOD(TestConcurrentAccess)
        TEST_METHOD_PROPERTY(L"Priority", L"3")
        TEST_METHOD_PROPERTY(L"Description", L"Tests thread safety with concurrent access to the singleton")
    END_TEST_METHOD()
    
    // Test getting DC name and running repadmin command
    BEGIN_TEST_METHOD(TestGetDCNameAndRunRepadmin)
        TEST_METHOD_PROPERTY(L"Priority", L"2")
        TEST_METHOD_PROPERTY(L"Description", L"Tests getting a DC name and running repadmin command using RunCommand")
    END_TEST_METHOD()

    // Test memory management and leak detection
    BEGIN_TEST_METHOD(TestMemoryManagement)
        TEST_METHOD_PROPERTY(L"Priority", L"2")
        TEST_METHOD_PROPERTY(L"Description", L"Tests proper memory management and checks for memory leaks")
    END_TEST_METHOD()

    // Test timeout handling
    BEGIN_TEST_METHOD(TestTimeoutHandling)
        TEST_METHOD_PROPERTY(L"Priority", L"3")
        TEST_METHOD_PROPERTY(L"Description", L"Tests handling of operations that may time out")
    END_TEST_METHOD()

    // Test integration with ADToolAgent
    BEGIN_TEST_METHOD(TestIntegrationWithADToolAgent)
        TEST_METHOD_PROPERTY(L"Priority", L"1")
        TEST_METHOD_PROPERTY(L"Description", L"Tests integration between ADToolAgent and ADReplicationToolAgent")
    END_TEST_METHOD()

    // Test data consistency across methods
    BEGIN_TEST_METHOD(TestDataConsistency)
        TEST_METHOD_PROPERTY(L"Priority", L"2")
        TEST_METHOD_PROPERTY(L"Description", L"Tests consistency of data between different methods of retrieval")
    END_TEST_METHOD()

    // Helper method to check if machine is domain-joined
    bool IsDomainJoined(ADReplicationToolAgent& agent) 
    {
        std::wstring domainCheckOutput;
        
        // Use RunCommand to check domain membership with PowerShell
        bool domainCheckResult = agent.RunCommand(
            L"powershell.exe -Command \"(Get-WmiObject -Class Win32_ComputerSystem).PartOfDomain\"",
            domainCheckOutput);
        
        // Trim whitespace
        domainCheckOutput.erase(0, domainCheckOutput.find_first_not_of(L" \t\n\r\f\v"));
        domainCheckOutput.erase(domainCheckOutput.find_last_not_of(L" \t\n\r\f\v") + 1);
        
        bool isInDomain = domainCheckResult && !domainCheckOutput.empty() && 
                          domainCheckOutput.find(L"True") != std::wstring::npos;
                          
        Log::Comment(String().Format(L"Domain membership check: %s", isInDomain ? L"Domain-joined" : L"Not domain-joined"));
        
        return isInDomain;
    }
};