// The purpose of this file is to help folks in Windows who are standing up a new win32 service test the service
// before the service component manifest is added to configure the service in the Windows image. This file will
// be referenced by https://www.osgwiki.com/wiki/How_to_implement_a_svchost_hosted_service.
//
// Note:
// This file is an improved version of shellcommon/shell/SharedPC/AccountManager/Selfhost/ServiceInstaller.
// To make the content easier to understand, comments were added, some naming mistakes were corrected, the #defines
// were grouped and more.

#include <nt.h>
#include <ntrtl.h>
#include <nturtl.h>
#include <windows.h>
#include <wrl.h>
#include <stdio.h>
#include <wil\resource.h>

#define SERVICE_NAME L"AIMXSrv"
#define SERVICE_DLL_PATH L"%systemroot%\\system32\\AIMXSrv.dll"
#define SERVICE_REGISTRY_KEY L"SYSTEM\\CurrentControlSet\\Services\\" SERVICE_NAME "\\Parameters"

#define SVCHOST_GROUPS_REG_KEY L"SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\SvcHost"
// Just use the service name as the group name for testing.
// The last \0 is important, as it's used in the REG_MULTI_SZ format
#define SERVICE_GROUP_VALUE SERVICE_NAME L"\0"

#define SERVICE_GROUP_REGISTRY_KEY L"SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\SvcHost\\" SERVICE_NAME

#define SVCHOST_PATH L"%SystemRoot%\\system32\\svchost.exe -k " SERVICE_NAME

#define SERVICE_MAIN_FUNCTION_NAME L"AimxServiceMain"

// Note that this function doesn't set some registry values like Start type in SERVICE_REGISTRY_KEY. If you want something other than the default
// for these, you'll need to set them. For example, for the Start registry value, the default is 2 (SERVICE_AUTO_START) (corresponds to Automatic
// in services.msc. If you want Manual, you need to set the data to 3 (SERVICE_DEMAND_START). To find out the set of registry values you can
// set to configure to your needs, see the registry keys for an existing service such as cdpsvc.
void ConfigureRegKeysPostServiceCreation()
{
    // Service registry key things.
    
    // Create the service registry key if it doesn't already exist.
    wil::unique_hkey adpSvcRegKey;
    ::RegCreateKeyExW(HKEY_LOCAL_MACHINE, SERVICE_REGISTRY_KEY, 0, nullptr, 0, KEY_ALL_ACCESS, nullptr, &adpSvcRegKey, nullptr);

    // Set the path to the DLL.
    RegSetValueEx(adpSvcRegKey.get(), L"ServiceDLL", 0, REG_EXPAND_SZ, reinterpret_cast<const BYTE*>(SERVICE_DLL_PATH), sizeof(SERVICE_DLL_PATH));

    // Set the name of the ServiceMain function.

    // Set the Start value to SERVICE_DEMAND_START for manual startup
    HKEY hServiceKey = nullptr;
    if (RegOpenKeyExW(HKEY_LOCAL_MACHINE, L"SYSTEM\\CurrentControlSet\\Services\\AIMXSrv", 0, KEY_SET_VALUE, &hServiceKey) == ERROR_SUCCESS)
    {
        DWORD dwStartType = SERVICE_DEMAND_START;
        RegSetValueExW(hServiceKey, L"Start", 0, REG_DWORD, reinterpret_cast<const BYTE*>(&dwStartType), sizeof(dwStartType));
        RegCloseKey(hServiceKey);
    }
    RegSetValueEx(adpSvcRegKey.get(), L"ServiceMain", 0, REG_SZ, reinterpret_cast<const BYTE*>(SERVICE_MAIN_FUNCTION_NAME), sizeof(SERVICE_MAIN_FUNCTION_NAME));

    // Set the value of ServiceDllUnloadOnStop to 1 to indicate that the service DLL can be unloaded on stop.
    DWORD value = 1;
    RegSetValueEx(adpSvcRegKey.get(), L"ServiceDllUnloadOnStop", 0, REG_DWORD, reinterpret_cast<const BYTE *>(&value), sizeof(value));

    // Service groups things

    // Get the handle to the registry key where all the svchost.exe groups are stored.
    wil::unique_hkey svchostGroupsRegKey;
    ::RegCreateKeyExW(HKEY_LOCAL_MACHINE, SVCHOST_GROUPS_REG_KEY, 0, nullptr, 0, KEY_ALL_ACCESS, nullptr, &svchostGroupsRegKey, nullptr);

    // Add our group name, which is just the service name, and for the description, just use the group name again except with the null character added at the end.
    RegSetValueEx(svchostGroupsRegKey.get(), SERVICE_NAME, 0, REG_MULTI_SZ, reinterpret_cast<const BYTE*>(SERVICE_GROUP_VALUE), sizeof(SERVICE_GROUP_VALUE));

    // Create a registry key under the above registry key for our group.
    wil::unique_hkey adpSvcGroupRegistryKey;
    ::RegCreateKeyExW(HKEY_LOCAL_MACHINE, SERVICE_GROUP_REGISTRY_KEY, 0, nullptr, 0, KEY_ALL_ACCESS, nullptr, &adpSvcGroupRegistryKey, nullptr);

    // Set the value of CoInitializeSecurityParam to 1.
    value = 1;
    RegSetValueEx(adpSvcGroupRegistryKey.get(), L"CoInitializeSecurityParam", 0, REG_DWORD, reinterpret_cast<const BYTE *>(&value), sizeof(value));
}

int __cdecl wmain(int /*argc*/, PCWSTR * /*argv*/)
{
    SC_HANDLE scm = OpenSCManager(NULL, NULL, SC_MANAGER_ALL_ACCESS);
    if (scm == nullptr)
    {
        wprintf(L"Unable to get a handle on the SCM\n");
        return 1;
    }

    // Get the fully qualified path to the service program (svchost.exe in our case) along with the command-line arguments.
    wchar_t serviceProgram[MAX_PATH+1];
    ExpandEnvironmentStrings(SVCHOST_PATH, serviceProgram, ARRAYSIZE(serviceProgram));

    // Create the service - note that this isn't done yet, as we still need to
    // configure registry to have pointers to our DLL (and also the group as well)
    SC_HANDLE service = CreateService( 
        scm,                         // SCM database 
        SERVICE_NAME,                // name of service 
        SERVICE_NAME,                // service name to display 
        SERVICE_ALL_ACCESS,          // desired access 
        SERVICE_WIN32_SHARE_PROCESS, // service type 
        SERVICE_DEMAND_START,        // start type 
        SERVICE_ERROR_NORMAL,        // error control type 
        serviceProgram,                 // service program with arguments
        nullptr,                        // no load ordering group 
        nullptr,                        // no tag identifier
        nullptr,                        // no dependencies 
        nullptr,                        // LocalSystem account (default)
        nullptr);                       // no password

    // If we try to do this again, we'll get ERROR_SERVICE_EXISTS, but that's okay.
    if (service == nullptr && (GetLastError() != ERROR_SERVICE_EXISTS))
    {
        wprintf(L"Unable to create the service\n");
    }
    else
    {
        ConfigureRegKeysPostServiceCreation();
    }

    if (service)
    {
        CloseServiceHandle(service);
    }
    CloseServiceHandle(scm);
    
    return 0;
}
