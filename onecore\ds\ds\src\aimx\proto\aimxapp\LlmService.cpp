/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    LlmService.cpp

Abstract:

    This module implements the LLM service communication for AIMX.
    Handles HTTP communication with the LLM backend service.

Revisions:

    <PERSON><PERSON><PERSON> (rizhang) 03/20/2025 - Created the module.
    <PERSON><PERSON><PERSON> (rizhang) 04/20/2025 - Splitting the common LLM commucation code into a separate library.

--*/

#include "pch.h"
#include "LlmService.h"
#include "RAGHelper.h"

#include <codecvt>
#include <locale>
#include <sstream>
#include <filesystem>

/*++

Routine Description:

    Constructor for LlmService class. Initializes the LLM service.

Arguments:

    None.

Return Value:

    None.

--*/
LlmService::LlmService()
    : LlmClient(LLM_ENDPOINT_URL, L"Phi-4-Mini", 0.7f, 40, 0.9f),
      m_ragDatabaseName("")
{
    SetEndpointUrl(LLM_ENDPOINT_URL);
    
    // Try to initialize the RAG database if it exists
    InitializeRagDatabase(AppConfig::getInstance().getDefaultRagDatabase());
}

/*++

Routine Description:

    Constructor for LlmService class with AppConfig. Initializes the LLM service
    with configuration from AppConfig.

Arguments:

    config - Application configuration object.

Return Value:

    None.

--*/
LlmService::LlmService(_In_ const AppConfig& config)
    : LlmClient(LLM_ENDPOINT_URL, L"Phi-4-Mini", 0.7f, 40, 0.9f),
      m_ragDatabaseName("")
{
    // Try to initialize the RAG database from config
    InitializeRagDatabase(config.getDefaultRagDatabase());
}

/*++

Routine Description:

    Destructor for LlmService class. Performs cleanup.

Arguments:

    None.

Return Value:

    None.

--*/
LlmService::~LlmService()
{
    // Call the cleanup function from RAGHelper.cpp
    CleanupRagResources();
}

/*++

Routine Description:

    Initializes the RAG database for context retrieval.

Arguments:

    None.

Return Value:

    bool indicating success or failure of the initialization.

--*/
bool
LlmService::InitializeRagDatabase(_In_ const std::string& dbName)
{
    LOGINFO("Initializing RAG database " + dbName);
    
    if (dbName == m_ragDatabaseName)
    {
        LOGINFO("RAG database already initialized " + dbName);
        return true;
    }
    
    try
    {
        // Check if the necessary files exist in the ragdb directory
        std::filesystem::path ragIndexPath =  "./ragdb" / std::filesystem::path(dbName) / "rag_index.bin";
        std::filesystem::path chunksJsonPath =  "./ragdb" / std::filesystem::path(dbName) / "chunks.json";
        
        if (!std::filesystem::exists(ragIndexPath) || !std::filesystem::exists(chunksJsonPath))
        {
            LOGERROR("RAG database files not found: " + 
                          WideToUtf8(ragIndexPath.wstring()) + " or " + 
                          WideToUtf8(chunksJsonPath.wstring()));
            return false;
        }
        
        // Initialize embedding model parameters
        m_embeddingParams.model_name = "./models/all-minilm-l6-v2_f32.gguf";
        m_embeddingParams.n_ctx = 512;
        
        // Initialize the RAG database using the RAGHelper function
        if (!RAG_initialize(m_embeddingParams))
        {
            LOGERROR("Failed to initialize RAG database");
            return false;
        }
        
        m_ragDatabaseName = dbName;
        LOGINFO("RAG database initialized successfully " + dbName);
        return true;
    }
    catch (const std::exception& e)
    {
        LOGERROR("Exception during RAG initialization: " + std::string(e.what()));
        return false;
    }
}

/*++

Routine Description:

    Checks if the RAG database is initialized.

Arguments:

    None.

Return Value:

    bool indicating whether the RAG database is initialized.

--*/
bool
LlmService::IsRagInitialized() const
{
    return m_ragDatabaseName == AppConfig::getInstance().getDefaultRagDatabase();
}

/*++

Routine Description:

    Retrieves relevant context from the RAG database based on the query.

Arguments:

    query - The query to use for context retrieval.

Return Value:

    std::wstring containing the retrieved context.

--*/
std::wstring
LlmService::RetrieveRagContext(
    _In_ const std::wstring& query
    )
{
    LOGINFO("Retrieving RAG context for query: " + WideToUtf8(query));
    
    if (!IsRagInitialized())
    {
        LOGERROR("Cannot retrieve RAG context: RAG database not initialized");
        return L"";
    }
    
    try
    {
        // Convert query to UTF-8
        std::string queryUtf8 = WideToUtf8(query);
        
        // Get context from the RAG database using the RAGHelper function
        std::vector<rag_entry> chunks = retrieve_chunks(m_embeddingParams, queryUtf8, 3);
        
        if (chunks.empty())
        {
            LOGINFO("No chunks retrieved for query");
            return L"";
        }
        
        LOGINFO("Retrieved " + std::to_string(chunks.size()) + " chunks");
        
        // Combine chunks into a single context
        std::ostringstream contextStream;
        for (const auto& chunk : chunks)
        {
            contextStream << chunk.textdata << "\n\n";
            LOGVERBOSE("Chunk #" + std::to_string(chunk.id) + " from " + chunk.filename + ": " + 
                     chunk.textdata.substr(0, 100) + (chunk.textdata.length() > 100 ? "..." : ""));
        }
        
        return Utf8ToWide(contextStream.str());
    }
    catch (const std::exception& e)
    {
        LOGERROR("Exception during RAG context retrieval: " + std::string(e.what()));
        return L"";
    }
}

/*++

Routine Description:

    Sends a prompt with RAG context to the LLM service and receives a response.

Arguments:

    prompt - The user's prompt to send to the LLM service.
    response - Output parameter that will receive the LLM response.

Return Value:

    bool indicating success or failure of the operation.

--*/
bool
LlmService::SendPromptWithRag(
    _In_ const std::wstring& prompt,
    _Out_ std::wstring& response
    )
{
    LOGVERBOSE("SendPromptWithRag: Processing prompt with RAG");
    
    if (!IsRagInitialized())
    {
        LOGERROR("Cannot use RAG: RAG database not initialized");
        return SendPrompt(prompt, response);
    }
    
    try
    {
        // Retrieve context from RAG database
        std::wstring context = RetrieveRagContext(prompt);
        
        if (context.empty())
        {
            LOGVERBOSE("No relevant context found, falling back to standard prompt");
            return SendPrompt(prompt, response);
        }
        
        // Send prompt with context
        return SendPromptWithContext(prompt, context, response);
    }
    catch (const std::exception& e)
    {
        LOGERROR("Exception in SendPromptWithRag: " + std::string(e.what()));
        response = Utf8ToWide(std::string("Error: ") + e.what());
        return false;
    }
}

/*++

Routine Description:

    Streams a prompt with RAG context to the LLM service.

Arguments:

    prompt - The user's prompt to send to the LLM service.
    callback - Callback function to process streaming responses.

Return Value:

    bool indicating success or failure of the operation.

--*/
bool
LlmService::StreamPromptWithRag(
    _In_ const std::wstring& prompt,
    _In_ StreamingResponseCallback callback
    )
{
    LOGVERBOSE("StreamPromptWithRag: Processing streaming prompt with RAG");
    
    if (!IsRagInitialized())
    {
        LOGERROR("Cannot use RAG: RAG database not initialized");
        return StreamPrompt(prompt, callback);
    }
    
    try
    {
        // Retrieve context from RAG database
        std::wstring context = RetrieveRagContext(prompt);
        
        if (context.empty())
        {
            LOGVERBOSE("No relevant context found, falling back to standard prompt");
            return StreamPrompt(prompt, callback);
        }
        
        // Stream prompt with context
        return StreamPromptWithContext(prompt, context, callback);
    }
    catch (const std::exception& e)
    {
        LOGERROR("Exception in StreamPromptWithRag: " + std::string(e.what()));
        callback(Utf8ToWide(std::string("Error: ") + e.what()), true);
        return false;
    }
}

// Function pointer types for the llm-infer.dll functions
typedef bool (*EMBED_INITIALIZE_FUNC)(model_params&);
typedef void (*EMBED_TERMINATE_FUNC)();
typedef bool (*RAG_INITIALIZE_FUNC)(model_params&);
typedef std::vector<rag_entry> (*RETRIEVE_CHUNKS_FUNC)(const model_params&, const std::string&, int);

// Function pointers for dynamic loading
static HMODULE g_hLlmInferDll = NULL;
static EMBED_INITIALIZE_FUNC g_pfnEmbedInitialize = NULL;
static EMBED_TERMINATE_FUNC g_pfnEmbedTerminate = NULL;
static RAG_INITIALIZE_FUNC g_pfnRagInitialize = NULL;
static RETRIEVE_CHUNKS_FUNC g_pfnRetrieveChunks = NULL;
