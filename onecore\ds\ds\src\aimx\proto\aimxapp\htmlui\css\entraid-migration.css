/* filepath: d:\os\src\onecore\ds\ds\src\adai\proto\win32\aimx\htmlui\css\entraid-migration.css */
/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    entraid-migration.css

Abstract:

    Styles for the Entra ID Migration assessment interface.
    Provides modern dark theme styling for charts, interactive elements,
    and assessment results following Microsoft design guidelines.

Author:

    <PERSON><PERSON><PERSON> (r<PERSON><PERSON>) 05/23/2025

--*/

/* Main container */
.migration-container {
    padding: 20px;
    background: #1e1e1e;
    color: #ffffff;
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header */
.migration-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: #2d2d2d;
    border-radius: 8px;
    border-left: 4px solid #0078d4;
}

.migration-header h1 {
    margin: 0;
    color: #ffffff;
    font-size: 24px;
    font-weight: 600;
}

.assessment-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.assessment-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #3c3c3c;
    border-radius: 6px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-indicator.analyzing {
    background: #ff9800;
}

.status-indicator.complete {
    background: #4caf50;
    animation: none;
}

.status-indicator.error {
    background: #f44336;
    animation: none;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-text {
    color: #cccccc;
    font-size: 14px;
}

/* Control buttons */
.control-btn {
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.control-btn.refresh-btn {
    background: #404040;
    color: #ffffff;
}

.control-btn.refresh-btn:hover {
    background: #505050;
}

.control-btn.primary-btn {
    background: #0078d4;
    color: #ffffff;
}

.control-btn.primary-btn:hover {
    background: #106ebe;
}

/* Tab navigation */
.tab-navigation {
    display: flex;
    background: #2d2d2d;
    border-radius: 8px;
    padding: 4px;
    margin-bottom: 20px;
}

.tab-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    color: #cccccc;
    cursor: pointer;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.tab-btn:hover {
    background: #404040;
    color: #ffffff;
}

.tab-btn.active {
    background: #0078d4;
    color: #ffffff;
}

/* Tab content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Overview grid */
.overview-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 20px;
    margin-bottom: 20px;
}

.overview-card {
    background: #2d2d2d;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #404040;
}

.overview-card h3 {
    margin: 0 0 15px 0;
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
}

/* Readiness score card */
.readiness-score .score-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.score-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: conic-gradient(#4caf50 0deg, #3c3c3c 0deg);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.score-circle::before {
    content: '';
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: #2d2d2d;
    position: absolute;
}

.score-value {
    font-size: 28px;
    font-weight: 700;
    color: #ffffff;
    z-index: 1;
}

.score-label {
    font-size: 16px;
    color: #cccccc;
    z-index: 1;
}

.score-status {
    color: #cccccc;
    font-size: 14px;
    text-align: center;
}

/* Quick stats */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: #3c3c3c;
    border-radius: 6px;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #0078d4;
    margin-bottom: 5px;
}

.stat-label {
    color: #cccccc;
    font-size: 12px;
}

/* Chart containers */
.chart-card {
    position: relative;
}

.chart-container {
    height: 250px;
    position: relative;
}

.chart-legend {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 15px;
}

/* Category breakdown */
.category-breakdown {
    grid-column: span 2;
}

.category-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.category-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #3c3c3c;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 4px solid transparent;
}

.category-item:hover {
    background: #454545;
}

.category-item.ready {
    border-left-color: #4caf50;
}

.category-item.warning {
    border-left-color: #ff9800;
}

.category-item.critical {
    border-left-color: #f44336;
}

.category-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.category-name {
    font-weight: 500;
    color: #ffffff;
}

.category-score {
    font-weight: 700;
    color: #0078d4;
}

.category-details {
    display: flex;
    align-items: center;
    gap: 15px;
}

.category-issues {
    color: #cccccc;
    font-size: 14px;
}

.category-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.category-status.ready {
    background: rgba(76, 175, 80, 0.2);
    color: #4caf50;
}

.category-status.warning {
    background: rgba(255, 152, 0, 0.2);
    color: #ff9800;
}

.category-status.critical {
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
}

/* Protocol analysis */
.protocols-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    grid-template-rows: auto auto;
    gap: 20px;
}

.protocol-card {
    background: #2d2d2d;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #404040;
}

.protocol-card h3 {
    margin: 0 0 15px 0;
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
}

.protocol-card.full-width {
    grid-column: span 2;
}

.protocol-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.protocol-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: #3c3c3c;
    border-radius: 6px;
}

.protocol-item.unsupported {
    border-left: 4px solid #f44336;
}

.protocol-name {
    font-weight: 500;
    color: #ffffff;
}

.protocol-impact {
    color: #f44336;
    font-size: 14px;
}

.view-details-btn {
    padding: 6px 12px;
    background: #0078d4;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.view-details-btn:hover {
    background: #106ebe;
}

/* Security policies */
.security-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 20px;
}

.security-card {
    background: #2d2d2d;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #404040;
}

.security-card h3 {
    margin: 0 0 15px 0;
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
}

.security-card.full-width {
    grid-column: span 2;
}

.policy-issues {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.policy-issue {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: #3c3c3c;
    border-radius: 6px;
    border-left: 4px solid transparent;
}

.policy-issue.high {
    border-left-color: #ff9800;
}

.policy-issue.critical {
    border-left-color: #f44336;
}

.issue-name {
    font-weight: 500;
    color: #ffffff;
}

.issue-compliance {
    color: #0078d4;
    font-weight: 500;
}

.issue-severity {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.issue-severity.high {
    background: rgba(255, 152, 0, 0.2);
    color: #ff9800;
}

.issue-severity.critical {
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
}

/* Actionable items */
.actionable-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 20px;
}

.action-card {
    background: #2d2d2d;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #404040;
}

.action-card h3 {
    margin: 0 0 15px 0;
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
}

.action-card.full-width {
    grid-column: span 2;
}

/* Progress tracker */
.progress-display {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.progress-bar-container {
    width: 100%;
    height: 8px;
    background: #3c3c3c;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #4caf50, #8bc34a);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-stats {
    color: #cccccc;
    font-size: 14px;
    text-align: center;
}

/* Action filters */
.actions-filter {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 8px 16px;
    border: 1px solid #404040;
    background: transparent;
    color: #cccccc;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.filter-btn:hover {
    background: #404040;
    color: #ffffff;
}

.filter-btn.active {
    background: #0078d4;
    color: #ffffff;
    border-color: #0078d4;
}

/* Action items list */
.action-items-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.action-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 20px;
    background: #3c3c3c;
    border-radius: 8px;
    border-left: 4px solid transparent;
    transition: all 0.2s ease;
}

.action-item:hover {
    background: #454545;
}

.action-item.critical {
    border-left-color: #f44336;
}

.action-item.high {
    border-left-color: #ff9800;
}

.action-item.medium {
    border-left-color: #2196f3;
}

.action-item.completed {
    opacity: 0.7;
    border-left-color: #4caf50;
}

.action-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.action-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 15px;
}

.action-title {
    font-weight: 600;
    color: #ffffff;
    font-size: 16px;
}

.action-category {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    white-space: nowrap;
}

.action-category.critical {
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
}

.action-category.high {
    background: rgba(255, 152, 0, 0.2);
    color: #ff9800;
}

.action-category.medium {
    background: rgba(33, 150, 243, 0.2);
    color: #2196f3;
}

.action-description {
    color: #cccccc;
    font-size: 14px;
    line-height: 1.4;
}

.action-details {
    display: flex;
    gap: 20px;
    color: #999999;
    font-size: 13px;
}

.action-controls {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: flex-end;
}

.view-action-btn,
.complete-action-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.view-action-btn {
    background: #404040;
    color: #ffffff;
}

.view-action-btn:hover {
    background: #505050;
}

.complete-action-btn {
    background: #4caf50;
    color: #ffffff;
}

.complete-action-btn:hover {
    background: #45a049;
}

.completed-badge {
    color: #4caf50;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: #2d2d2d;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid #404040;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #404040;
}

.modal-header h2 {
    margin: 0;
    color: #ffffff;
    font-size: 20px;
}

.close-button {
    background: none;
    border: none;
    color: #cccccc;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.close-button:hover {
    background: #404040;
    color: #ffffff;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #404040;
}

.modal-footer button {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.primary-btn {
    background: #0078d4;
    color: #ffffff;
}

.primary-btn:hover {
    background: #106ebe;
}

.secondary-btn {
    background: #404040;
    color: #ffffff;
}

.secondary-btn:hover {
    background: #505050;
}

.cancel-btn {
    background: transparent;
    color: #cccccc;
    border: 1px solid #404040;
}

.cancel-btn:hover {
    background: #404040;
    color: #ffffff;
}

/* Action detail sections */
.action-detail-section {
    margin-bottom: 20px;
}

.action-detail-section h4 {
    margin: 0 0 10px 0;
    color: #0078d4;
    font-size: 16px;
}

.action-detail-section p {
    color: #cccccc;
    line-height: 1.5;
    margin: 0;
}

.action-detail-section ol {
    color: #cccccc;
    margin: 0;
    padding-left: 20px;
}

.action-detail-section ol li {
    margin-bottom: 5px;
    line-height: 1.4;
}

/* Responsive design */
@media (max-width: 1200px) {
    .overview-grid,
    .protocols-grid,
    .security-grid,
    .actionable-grid {
        grid-template-columns: 1fr;
    }
    
    .protocol-card.full-width,
    .security-card.full-width,
    .action-card.full-width {
        grid-column: span 1;
    }
}

@media (max-width: 768px) {
    .migration-container {
        padding: 15px;
    }
    
    .migration-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .assessment-controls {
        width: 100%;
        justify-content: space-between;
    }
    
    .tab-navigation {
        flex-direction: column;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .action-item {
        flex-direction: column;
        gap: 15px;
    }
    
    .action-header {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }
    
    .action-controls {
        flex-direction: row;
        width: 100%;
        justify-content: flex-end;
    }
    
    .actions-filter {
        justify-content: center;
    }
    
    .filter-btn {
        font-size: 12px;
        padding: 6px 12px;
    }
}