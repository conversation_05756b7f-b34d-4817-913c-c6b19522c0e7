/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    ProtocolValidationTests.cpp

Abstract:
    Additional test suite focused on AIMX protocol validation and edge cases.
    Validates JSON request/response parsing and protocol compliance.


--*/

#include "pch.hxx"
#include "aimxrpcclient.h"
#include "ProtocolValidationTests.h"
#include <nlohmann/json.hpp>

void ProtocolValidationTests::TestRequestValidation()
{
    GUID contextId = {};
    HRESULT hr = S_OK;
    AimxRpcClient* rpcClient = new AimxRpcClient();
    
    Log::Comment(L"Starting TestRequestValidation");
    
    VERIFY_IS_NOT_NULL(rpcClient, L"Failed to create AimxRpcClient instance");

    // Connect to server
    hr = rpcClient->AimxConnect(&contextId);
    VERIFY_SUCCEEDED(hr, L"AimxConnect should succeed");

    // Test various invalid request scenarios
    struct TestCase {
        std::string name;
        nlohmann::json request;
        bool shouldSucceed;
    };

    std::vector<TestCase> testCases = {
        // Valid chatbot query
        {
            "Valid chatbot query",
            nlohmann::json{
                {"requestType", 1},
                {"query", "Test query"},
                {"executionMode", 2}
            },
            true
        },
        // Missing requestType
        {
            "Missing requestType",
            nlohmann::json{
                {"query", "Test query"},
                {"executionMode", 2}
            },
            false
        },
        // Invalid requestType
        {
            "Invalid requestType",
            nlohmann::json{
                {"requestType", 999},
                {"query", "Test query"},
                {"executionMode", 2}
            },
            false
        },
        // Missing query for chatbot
        {
            "Missing query for chatbot",
            nlohmann::json{
                {"requestType", 1},
                {"executionMode", 2}
            },
            false
        },
        // Invalid executionMode
        {
            "Invalid executionMode",
            nlohmann::json{
                {"requestType", 1},
                {"query", "Test query"},
                {"executionMode", 999}
            },
            false
        },
        // Valid direct query
        {
            "Valid direct query",
            nlohmann::json{
                {"requestType", 2},
                {"command", "Get-ADUser"},
                {"executionMode", 1}
            },
            true
        },
        // Missing command for direct query
        {
            "Missing command for direct query",
            nlohmann::json{
                {"requestType", 2},
                {"executionMode", 1}
            },
            false
        },
        // Valid status query
        {
            "Valid status query",
            nlohmann::json{
                {"requestType", 3},
                {"operationId", "{12345678-1234-1234-1234-123456789012}"}
            },
            true
        },
        // Missing operationId for status query
        {
            "Missing operationId for status query",
            nlohmann::json{
                {"requestType", 3}
            },
            false
        },
        // Invalid operationId format
        {
            "Invalid operationId format",
            nlohmann::json{
                {"requestType", 3},
                {"operationId", "not-a-guid"}
            },
            false
        }
    };

    for (const auto& testCase : testCases)
    {
        Log::Comment(String().Format(L"Testing: %hs", testCase.name.c_str()));

        std::string requestStr = testCase.request.dump();
        std::wstring requestWStr(requestStr.begin(), requestStr.end());

        LPWSTR response = nullptr;
        hr = rpcClient->AimxProcessPrompt(contextId, requestWStr.c_str(), &response);

        if (testCase.shouldSucceed)
        {
            if (SUCCEEDED(hr) && response)
            {
                std::wstring responseWStr(response);
                std::string responseStr(responseWStr.begin(), responseWStr.end());
                
                // Verify response is valid JSON
                VERIFY_IS_TRUE(nlohmann::json::accept(responseStr), L"Response should be valid JSON");
                
                nlohmann::json responseJson = nlohmann::json::parse(responseStr);
                VERIFY_IS_TRUE(responseJson.contains("success"), L"Response should contain success field");
                
                if (responseJson["success"].get<bool>())
                {
                    Log::Comment(L"✓ Request succeeded as expected");
                }
                else
                {
                    Log::Comment(String().Format(L"✗ Request failed with error: %hs", 
                        responseJson.value("errorMessage", "Unknown error").c_str()));
                }
            }
            else
            {
                Log::Comment(L"✗ Request failed unexpectedly");
            }
        }
        else
        {
            // For invalid requests, we expect either failure or error response
            if (FAILED(hr))
            {
                Log::Comment(L"✓ Request failed as expected (RPC level)");
            }
            else if (response)
            {
                std::wstring responseWStr(response);
                std::string responseStr(responseWStr.begin(), responseWStr.end());
                
                if (nlohmann::json::accept(responseStr))
                {
                    nlohmann::json responseJson = nlohmann::json::parse(responseStr);
                    if (responseJson.contains("success") && !responseJson["success"].get<bool>())
                    {
                        Log::Comment(L"✓ Request failed as expected (application level)");
                    }
                    else
                    {
                        Log::Comment(L"✗ Invalid request succeeded unexpectedly");
                    }
                }
                else
                {
                    Log::Comment(L"✗ Invalid JSON response");
                }
            }
            else
            {
                Log::Comment(L"✗ Unexpected null response");
            }
        }

        if (response)
        {
            MIDL_user_free(response);
        }
    }

    // Cleanup
    hr = rpcClient->AimxClose(contextId);
    VERIFY_SUCCEEDED(hr, L"AimxClose should succeed");
    delete rpcClient;
    Log::Comment(L"TestRequestValidation completed");
}

void ProtocolValidationTests::TestResponseFormat()
{
    GUID contextId = {};
    HRESULT hr = S_OK;
    AimxRpcClient* rpcClient = new AimxRpcClient();
    
    Log::Comment(L"Starting TestResponseFormat");
    
    VERIFY_IS_NOT_NULL(rpcClient, L"Failed to create AimxRpcClient instance");

    // Connect to server
    hr = rpcClient->AimxConnect(&contextId);
    VERIFY_SUCCEEDED(hr, L"AimxConnect should succeed");

    // Test chatbot query response format
    nlohmann::json chatbotRequest;
    chatbotRequest["requestType"] = 1;
    chatbotRequest["query"] = "Test query for response format validation";
    chatbotRequest["executionMode"] = 2;

    std::string requestStr = chatbotRequest.dump();
    std::wstring requestWStr(requestStr.begin(), requestStr.end());

    LPWSTR response = nullptr;
    hr = rpcClient->AimxProcessPrompt(contextId, requestWStr.c_str(), &response);
    VERIFY_SUCCEEDED(hr, L"Chatbot query should succeed");
    VERIFY_IS_NOT_NULL(response, L"Response should not be null");

    // Validate response format
    std::wstring responseWStr(response);
    std::string responseStr(responseWStr.begin(), responseWStr.end());
    
    VERIFY_IS_TRUE(nlohmann::json::accept(responseStr), L"Response should be valid JSON");
    
    nlohmann::json responseJson = nlohmann::json::parse(responseStr);
    
    // Check required fields
    VERIFY_IS_TRUE(responseJson.contains("success"), L"Response should contain 'success' field");
    VERIFY_IS_TRUE(responseJson["success"].is_boolean(), L"'success' should be boolean");
    
    if (responseJson["success"].get<bool>())
    {
        VERIFY_IS_TRUE(responseJson.contains("operationId"), L"Success response should contain 'operationId'");
        VERIFY_IS_TRUE(responseJson["operationId"].is_string(), L"'operationId' should be string");
        
        VERIFY_IS_TRUE(responseJson.contains("status"), L"Success response should contain 'status'");
        VERIFY_IS_TRUE(responseJson["status"].is_string(), L"'status' should be string");
        
        VERIFY_IS_TRUE(responseJson.contains("message"), L"Success response should contain 'message'");
        VERIFY_IS_TRUE(responseJson["message"].is_string(), L"'message' should be string");
        
        // Validate operationId format (should be GUID)
        std::string operationId = responseJson["operationId"].get<std::string>();
        Log::Comment(String().Format(L"Operation ID: %hs", operationId.c_str()));
        
        // Basic GUID format validation (should have brackets and hyphens)
        VERIFY_IS_TRUE(operationId.length() > 30, L"Operation ID should be reasonable length");
        VERIFY_IS_TRUE(operationId.find('{') != std::string::npos, L"Operation ID should contain opening brace");
        VERIFY_IS_TRUE(operationId.find('}') != std::string::npos, L"Operation ID should contain closing brace");
        VERIFY_IS_TRUE(operationId.find('-') != std::string::npos, L"Operation ID should contain hyphens");
    }
    else
    {
        VERIFY_IS_TRUE(responseJson.contains("errorCode"), L"Error response should contain 'errorCode'");
        VERIFY_IS_TRUE(responseJson["errorCode"].is_number(), L"'errorCode' should be number");
        
        VERIFY_IS_TRUE(responseJson.contains("errorMessage"), L"Error response should contain 'errorMessage'");
        VERIFY_IS_TRUE(responseJson["errorMessage"].is_string(), L"'errorMessage' should be string");
    }

    Log::Comment(L"Response format validation passed");
    Log::Comment(String().Format(L"Full response: %ws", response));

    if (response) MIDL_user_free(response);

    // Cleanup
    hr = rpcClient->AimxClose(contextId);
    VERIFY_SUCCEEDED(hr, L"AimxClose should succeed");
    delete rpcClient;
    Log::Comment(L"TestResponseFormat completed");
}

void ProtocolValidationTests::TestStatusTransitions()
{
    GUID contextId = {};
    HRESULT hr = S_OK;
    AimxRpcClient* rpcClient = new AimxRpcClient();
    
    Log::Comment(L"Starting TestStatusTransitions");
    
    VERIFY_IS_NOT_NULL(rpcClient, L"Failed to create AimxRpcClient instance");

    // Connect to server
    hr = rpcClient->AimxConnect(&contextId);
    VERIFY_SUCCEEDED(hr, L"AimxConnect should succeed");

    // Start a chatbot query
    nlohmann::json request;
    request["requestType"] = 1;
    request["query"] = "Test query for status transitions";
    request["executionMode"] = 2;

    std::string requestStr = request.dump();
    std::wstring requestWStr(requestStr.begin(), requestStr.end());

    LPWSTR response = nullptr;
    hr = rpcClient->AimxProcessPrompt(contextId, requestWStr.c_str(), &response);
    VERIFY_SUCCEEDED(hr, L"Initial request should succeed");

    // Get operation ID
    std::wstring responseWStr(response);
    std::string responseStr(responseWStr.begin(), responseWStr.end());
    nlohmann::json responseJson = nlohmann::json::parse(responseStr);
    std::string operationId = responseJson["operationId"].get<std::string>();
    
    if (response) MIDL_user_free(response);

    // Track status transitions
    std::vector<std::string> statusHistory;
    std::string currentStatus = "PLANNING";
    statusHistory.push_back(currentStatus);

    // Poll for status changes
    int pollCount = 0;
    const int maxPolls = 30;
    bool transitionComplete = false;

    while (pollCount < maxPolls && !transitionComplete)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        
        nlohmann::json statusRequest;
        statusRequest["requestType"] = 3;
        statusRequest["operationId"] = operationId;

        std::string statusRequestStr = statusRequest.dump();
        std::wstring statusRequestWStr(statusRequestStr.begin(), statusRequestStr.end());

        response = nullptr;
        hr = rpcClient->AimxProcessPrompt(contextId, statusRequestWStr.c_str(), &response);
        
        if (SUCCEEDED(hr) && response)
        {
            std::wstring statusResponseWStr(response);
            std::string statusResponseStr(statusResponseWStr.begin(), statusResponseWStr.end());
            nlohmann::json statusResponseJson = nlohmann::json::parse(statusResponseStr);

            std::string newStatus = statusResponseJson["status"].get<std::string>();
            
            if (newStatus != currentStatus)
            {
                Log::Comment(String().Format(L"Status transition: %hs -> %hs", 
                    currentStatus.c_str(), newStatus.c_str()));
                
                currentStatus = newStatus;
                statusHistory.push_back(currentStatus);
            }

            if (newStatus == "PLAN_READY" || newStatus == "COMPLETED" || 
                newStatus == "FAILED" || newStatus == "CANCELLED")
            {
                transitionComplete = true;
            }

            MIDL_user_free(response);
        }
        
        pollCount++;
    }

    // Validate status transition sequence
    VERIFY_IS_TRUE(statusHistory.size() >= 2, L"Should have at least 2 status transitions");
    if (0 != strcmp("PLANNING", statusHistory[0].c_str()))
    {
        VERIFY_FAIL(L"First status should be PLANNING");
    }

    Log::Comment(L"Status transition history:");
    for (size_t i = 0; i < statusHistory.size(); i++)
    {
        Log::Comment(String().Format(L"  %d: %hs", static_cast<int>(i), statusHistory[i].c_str()));
    }

    // Validate that we don't have invalid transitions
    for (size_t i = 1; i < statusHistory.size(); i++)
    {
        std::string prevStatus = statusHistory[i-1];
        std::string currStatus = statusHistory[i];
        
        // Define valid transitions
        bool validTransition = false;
        
        if (prevStatus == "PLANNING" && 
            (currStatus == "PLAN_READY" || currStatus == "FAILED" || currStatus == "CANCELLED"))
        {
            validTransition = true;
        }
        else if (prevStatus == "PLAN_READY" && 
                 (currStatus == "EXECUTING" || currStatus == "CANCELLED"))
        {
            validTransition = true;
        }
        else if (prevStatus == "EXECUTING" && 
                 (currStatus == "COMPLETED" || currStatus == "FAILED" || currStatus == "CANCELLED"))
        {
            validTransition = true;
        }
        
        VERIFY_IS_TRUE(validTransition, 
            String().Format(L"Invalid status transition from %hs to %hs", 
                prevStatus.c_str(), currStatus.c_str()));
    }

    // Cleanup
    hr = rpcClient->AimxClose(contextId);
    VERIFY_SUCCEEDED(hr, L"AimxClose should succeed");
    delete rpcClient;
    Log::Comment(L"TestStatusTransitions completed");
}

void ProtocolValidationTests::TestExecutionPlanFormat()
{
    GUID contextId = {};
    HRESULT hr = S_OK;
    AimxRpcClient* rpcClient = new AimxRpcClient();
    
    Log::Comment(L"Starting TestExecutionPlanFormat");
    
    VERIFY_IS_NOT_NULL(rpcClient, L"Failed to create AimxRpcClient instance");

    // Connect to server
    hr = rpcClient->AimxConnect(&contextId);
    VERIFY_SUCCEEDED(hr, L"AimxConnect should succeed");

    // Start a chatbot query to generate an execution plan
    nlohmann::json request;
    request["requestType"] = 1;
    request["query"] = "Get the domain password policy and user account information";
    request["executionMode"] = 2;

    std::string requestStr = request.dump();
    std::wstring requestWStr(requestStr.begin(), requestStr.end());

    LPWSTR response = nullptr;
    hr = rpcClient->AimxProcessPrompt(contextId, requestWStr.c_str(), &response);
    VERIFY_SUCCEEDED(hr, L"Initial request should succeed");

    // Get operation ID
    std::wstring responseWStr(response);
    std::string responseStr(responseWStr.begin(), responseWStr.end());
    nlohmann::json responseJson = nlohmann::json::parse(responseStr);
    std::string operationId = responseJson["operationId"].get<std::string>();
    
    if (response) MIDL_user_free(response);

    // Wait for plan to be ready
    bool planReady = false;
    nlohmann::json planJson;
    
    for (int i = 0; i < 20 && !planReady; i++)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        
        nlohmann::json statusRequest;
        statusRequest["requestType"] = 3;
        statusRequest["operationId"] = operationId;

        std::string statusRequestStr = statusRequest.dump();
        std::wstring statusRequestWStr(statusRequestStr.begin(), statusRequestStr.end());

        response = nullptr;
        hr = rpcClient->AimxProcessPrompt(contextId, statusRequestWStr.c_str(), &response);
        
        if (SUCCEEDED(hr) && response)
        {
            std::wstring statusResponseWStr(response);
            std::string statusResponseStr(statusResponseWStr.begin(), statusResponseWStr.end());
            nlohmann::json statusResponseJson = nlohmann::json::parse(statusResponseStr);

            if (statusResponseJson["status"].get<std::string>() == "PLAN_READY")
            {
                planReady = true;
                
                VERIFY_IS_TRUE(statusResponseJson.contains("executionPlan"), 
                    L"Plan ready response should contain execution plan");
                
                std::string executionPlanStr = statusResponseJson["executionPlan"].get<std::string>();
                
                // Validate execution plan is valid JSON
                VERIFY_IS_TRUE(nlohmann::json::accept(executionPlanStr), 
                    L"Execution plan should be valid JSON");
                
                planJson = nlohmann::json::parse(executionPlanStr);
                
                Log::Comment(L"Execution Plan:");
                Log::Comment(String().Format(L"%hs", executionPlanStr.c_str()));
            }

            MIDL_user_free(response);
        }
    }

    VERIFY_IS_TRUE(planReady, L"Plan should be ready within timeout");

    // Validate execution plan structure
    VERIFY_IS_TRUE(planJson.contains("planType"), L"Plan should contain planType");
    VERIFY_IS_TRUE(planJson["planType"].is_string(), L"planType should be string");
    
    VERIFY_IS_TRUE(planJson.contains("steps"), L"Plan should contain steps");
    VERIFY_IS_TRUE(planJson["steps"].is_array(), L"steps should be array");
    
    VERIFY_IS_TRUE(planJson.contains("requiredServices"), L"Plan should contain requiredServices");
    VERIFY_IS_TRUE(planJson["requiredServices"].is_array(), L"requiredServices should be array");
    
    // Validate steps structure
    const auto& steps = planJson["steps"];
    VERIFY_IS_TRUE(steps.size() > 0, L"Should have at least one step");
    
    for (size_t i = 0; i < steps.size(); i++)
    {
        const auto& step = steps[i];
        
        VERIFY_IS_TRUE(step.contains("stepId"), 
            String().Format(L"Step %d should contain stepId", static_cast<int>(i)));
        VERIFY_IS_TRUE(step["stepId"].is_number_integer(), 
            String().Format(L"Step %d stepId should be integer", static_cast<int>(i)));
        
        VERIFY_IS_TRUE(step.contains("action"), 
            String().Format(L"Step %d should contain action", static_cast<int>(i)));
        VERIFY_IS_TRUE(step["action"].is_string(), 
            String().Format(L"Step %d action should be string", static_cast<int>(i)));
        
        Log::Comment(String().Format(L"Step %d: %hs", 
            static_cast<int>(i), step["action"].get<std::string>().c_str()));
    }

    // Cleanup
    hr = rpcClient->AimxClose(contextId);
    VERIFY_SUCCEEDED(hr, L"AimxClose should succeed");
    delete rpcClient;
    Log::Comment(L"TestExecutionPlanFormat completed");
}