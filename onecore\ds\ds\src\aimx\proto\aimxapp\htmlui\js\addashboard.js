/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    addashboard.js

Abstract:

    This module implements the Active Directory Dashboard front-end functionality.
    It renders real-time metrics, charts, and alerts for Active Directory health monitoring,
    handling user interactions and communicating with the native C++ backend through WebView.

Communication Protocol:
    
    Frontend to Backend:
        1. getData: Request dashboard data with timeframe parameter
           Format: {type: "adDashboard", action: "getData", timeframe: "day|week|month"}
        
        2. generateReport: Request report generation with parameters
           Format: {type: "adDashboard", action: "generateReport", parameters: {...}}
        
        3. drillDown: Request detailed data for a specific view
           Format: {type: "adDashboard", action: "drillDown", category: "string", filter: "string"}
        
        4. chartInteraction: Notify backend about chart interactions
           Format: {type: "adDashboard", action: "chartInteraction", chart: "string", label: "string", value: any}
    
    Backend to Frontend:
        1. updateData: Send dashboard data to the frontend
           Format: {type: "adDashboard", action: "updateData", data: {...}}
        
        2. reportStatus: Report generation status
           Format: {type: "adDashboard", action: "reportStatus", status: "success|error", reportPath?: "string"}
        
        3. alert: Send a new alert to be displayed
           Format: {type: "adDashboard", action: "alert", alert: {...}}
        
        4. error: Send error information
           Format: {type: "adDashboard", action: "error", message: "string"}

Author:

    Rupo Zhang (rizhang) 03/29/2025

--*/

// Create a namespace for the dashboard to avoid polluting global scope
const ADDashboard = (function() {
    // Private variables
    let charts = {};
    let currentTimeframe = 'day';
    let isInitialized = false;
    let resizeTimer;
    
    // Chart configuration and color schemes
    const chartColors = {
        blue: 'rgba(0, 120, 215, 0.7)',
        blueLight: 'rgba(0, 120, 215, 0.3)',
        green: 'rgba(76, 175, 80, 0.7)',
        greenLight: 'rgba(76, 175, 80, 0.3)',
        red: 'rgba(244, 67, 54, 0.7)',
        redLight: 'rgba(244, 67, 54, 0.3)',
        yellow: 'rgba(255, 193, 7, 0.7)',
        yellowLight: 'rgba(255, 193, 7, 0.3)',
        purple: 'rgba(156, 39, 176, 0.7)',
        purpleLight: 'rgba(156, 39, 176, 0.3)',
        gray: 'rgba(158, 158, 158, 0.7)',
        grayLight: 'rgba(158, 158, 158, 0.3)'
    };
    
    // Global chart configuration for dark theme
    Chart.defaults.color = '#AAAAAA';
    Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
    
    const chartConfig = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                labels: {
                    color: '#FFFFFF',
                    font: {
                        size: 12
                    },
                    boxWidth: 12,
                    padding: 10
                },
                position: 'top',
                align: 'center',
                // Limit legend items for smaller screens
                maxItems: window.innerWidth < 768 ? 3 : 6
            },
            tooltip: {
                backgroundColor: 'rgba(61, 61, 61, 0.9)',
                titleColor: '#FFFFFF',
                bodyColor: '#FFFFFF',
                borderColor: 'rgba(255, 255, 255, 0.2)',
                borderWidth: 1,
                // Prevent tooltips from overflowing container
                displayColors: true,
                caretSize: 6
            }
        },
        scales: {
            x: {
                grid: {
                    color: 'rgba(255, 255, 255, 0.1)'
                },
                ticks: {
                    color: '#AAAAAA',
                    // Limit the number of ticks on small screens
                    maxRotation: 45,
                    minRotation: 45,
                    autoSkip: true,
                    autoSkipPadding: 10,
                    maxTicksLimit: window.innerWidth < 768 ? 5 : 10
                }
            },
            y: {
                grid: {
                    color: 'rgba(255, 255, 255, 0.1)'
                },
                ticks: {
                    color: '#AAAAAA',
                    // Add padding to prevent y-axis labels from being cut off
                    padding: 5
                },
                // Ensure values don't get cut off
                beginAtZero: true
            }
        },
        // Ensure proper padding within the canvas
        layout: {
            padding: {
                top: 5,
                right: 15,
                bottom: 5,
                left: 5
            }
        }
    };
    
    // Initialize the dashboard
    function initialize() {
        if (isInitialized) return;
        
        console.log("Initializing AD Dashboard");
        
        // Set up event listeners
        setupEventListeners();
        
        // Initialize charts with placeholder data
        initializeCharts();
        
        // Set placeholder stats
        setPlaceholderStats();
        
        // Register communication with host
        setupHostCommunication();
        
        // Register resize handler to adjust charts when window size changes
        setupResizeHandler();
        
        isInitialized = true;
    }
    
    // Set up event listeners
    function setupEventListeners() {
        // Timeframe selector buttons
        const timeframeBtns = document.querySelectorAll('.timeframe-btn');
        timeframeBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // Remove active class from all buttons
                timeframeBtns.forEach(b => b.classList.remove('active'));
                // Add active class to clicked button
                this.classList.add('active');
                
                // Update current timeframe
                currentTimeframe = this.dataset.timeframe;
                
                // Request data from host for the new timeframe
                requestDataFromHost();
            });
        });
        
        // Refresh button
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', function() {
                this.classList.add('refreshing');
                
                // Request data refresh
                requestDataFromHost();
                
                // Remove refreshing class after a delay
                setTimeout(() => {
                    this.classList.remove('refreshing');
                }, 1000);
            });
        }
        
        // Report button
        const reportBtn = document.getElementById('reportBtn');
        const reportModal = document.getElementById('reportModal');
        
        if (reportBtn && reportModal) {
            reportBtn.addEventListener('click', function() {
                reportModal.classList.add('active');
            });
        }
        
        // Close buttons for modals
        const closeButtons = document.querySelectorAll('.close-button, .cancel-btn');
        closeButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                const modal = this.closest('.modal');
                if (modal) {
                    modal.classList.remove('active');
                }
            });
        });
        
        // Generate report button
        const generateReportBtn = document.getElementById('generateReportBtn');
        if (generateReportBtn) {
            generateReportBtn.addEventListener('click', function() {
                generateReport();
                
                // Close the modal
                const modal = document.getElementById('reportModal');
                if (modal) {
                    modal.classList.remove('active');
                }
            });
        }
        
        // Make charts interactive for drill-down
        setupChartInteractivity();
    }
    
    // Initialize charts with placeholder data
    function initializeCharts() {
        // Server Status Chart (Doughnut)
        const serverStatusCtx = document.getElementById('serverStatusChart')?.getContext('2d');
        if (serverStatusCtx) {
            charts.serverStatus = new Chart(serverStatusCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Online', 'Warning', 'Offline', 'Maintenance'],
                    datasets: [{
                        data: [25, 3, 1, 2], // Sample data
                        backgroundColor: [
                            chartColors.green,
                            chartColors.yellow,
                            chartColors.red,
                            chartColors.gray
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    ...chartConfig,
                    cutout: '60%',
                    plugins: {
                        ...chartConfig.plugins,
                        legend: {
                            ...chartConfig.plugins.legend,
                            position: 'right',
                            align: 'center'
                        }
                    }
                }
            });
        }
        
        // Authentication Activity Chart (Line)
        const authActivityCtx = document.getElementById('authActivityChart')?.getContext('2d');
        if (authActivityCtx) {
            // Initialize with empty data, will be populated by data from host
            charts.authActivity = new Chart(authActivityCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [
                        {
                            label: 'Successful',
                            data: [],
                            borderColor: chartColors.green,
                            backgroundColor: chartColors.greenLight,
                            fill: true,
                            tension: 0.3
                        },
                        {
                            label: 'Failed',
                            data: [],
                            borderColor: chartColors.red,
                            backgroundColor: chartColors.redLight,
                            fill: true,
                            tension: 0.3
                        }
                    ]
                },
                options: {
                    ...chartConfig,
                    elements: {
                        line: {
                            tension: 0.3,
                            borderWidth: 3
                        },
                        point: {
                            radius: 3,
                            hitRadius: 10
                        }
                    }
                }
            });
        }
        
        // Replication Health Chart (Gauge using semi-circle doughnut)
        const replicationCtx = document.getElementById('replicationChart')?.getContext('2d');
        if (replicationCtx) {
            charts.replication = new Chart(replicationCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Healthy', 'Issues'],
                    datasets: [{
                        data: [90, 10], // Initial placeholder data
                        backgroundColor: [
                            chartColors.green,
                            chartColors.redLight
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    ...chartConfig,
                    cutout: '70%',
                    circumference: 180,
                    rotation: -90,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    aspectRatio: 2 // Ensure proper sizing for semi-circle
                }
            });
            
            // Store the original draw method before customizing it
            charts.replication._originalDraw = charts.replication.draw;
            
            // Create a single custom draw function that can be updated
            updateReplicationChartText(90); // Initial value
        }

        // Resource Utilization Chart (Bar)
        const resourceUtilizationCtx = document.getElementById('resourceUtilizationChart')?.getContext('2d');
        if (resourceUtilizationCtx) {
            // Initialize with empty data
            charts.resourceUtilization = new Chart(resourceUtilizationCtx, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [
                        {
                            label: 'CPU (%)',
                            data: [],
                            backgroundColor: chartColors.blue,
                            borderColor: 'rgba(0,0,0,0.1)',
                            borderWidth: 1
                        },
                        {
                            label: 'Memory (%)',
                            data: [],
                            backgroundColor: chartColors.purple,
                            borderColor: 'rgba(0,0,0,0.1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    ...chartConfig,
                    scales: {
                        x: {
                            ...chartConfig.scales.x,
                            stacked: false
                        },
                        y: {
                            ...chartConfig.scales.y,
                            stacked: false,
                            max: 100
                        }
                    },
                    // Better bar sizing
                    barThickness: 20,
                    maxBarThickness: 30,
                    categoryPercentage: 0.8,
                    barPercentage: 0.9
                }
            });
        }
        
        // Security Incidents Chart (Pie)
        const securityIncidentsCtx = document.getElementById('securityIncidentsChart')?.getContext('2d');
        if (securityIncidentsCtx) {
            charts.securityIncidents = new Chart(securityIncidentsCtx, {
                type: 'pie',
                data: {
                    labels: ['Failed Logins', 'Account Lockouts', 'Privilege Changes', 'Others'],
                    datasets: [{
                        data: [45, 15, 10, 30], // Initial placeholder data
                        backgroundColor: [
                            chartColors.red,
                            chartColors.yellow,
                            chartColors.blue,
                            chartColors.gray
                        ]
                    }]
                },
                options: {
                    ...chartConfig,
                    plugins: {
                        ...chartConfig.plugins,
                        legend: {
                            ...chartConfig.plugins.legend,
                            position: 'right',
                            align: 'center'
                        }
                    }
                }
            });
        }
        
        // Initial responsiveness update
        updateChartResponsiveness();
    }
    
    // Helper function to update the replication chart center text
    function updateReplicationChartText(healthyPercent) {
        if (!charts.replication) return;
        
        // Create a new draw function with the current percentage
        charts.replication.draw = function() {
            // First call the original draw method
            charts.replication._originalDraw.apply(this, arguments);
            
            const width = this.width;
            const height = this.height;
            const ctx = this.ctx;
            
            ctx.restore();
            ctx.font = '24px Segoe UI';
            ctx.fillStyle = '#FFFFFF';
            ctx.textBaseline = 'middle';
            ctx.textAlign = 'center';
            // Position text a bit higher for semi-circle
            ctx.fillText(`${healthyPercent}%`, width / 2, height * 0.65);
            
            ctx.font = '14px Segoe UI';
            ctx.fillStyle = '#AAAAAA';
            ctx.fillText('Healthy', width / 2, height * 0.75);
            ctx.save();
        };
    }

    // Set placeholder stats in the overview section
    function setPlaceholderStats() {
        document.getElementById('dcCount').textContent = '--';
        document.getElementById('serverCount').textContent = '--';
        document.getElementById('userCount').textContent = '--';
        
        const healthElement = document.getElementById('overallHealth');
        if (healthElement) {
            healthElement.textContent = '--';
            healthElement.className = 'stat-value health-indicator';
        }
    }
    
    // Set up communication with the WebView2 host
    function setupHostCommunication() {
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.addEventListener('message', function(event) {
                let message = event.data;
                
                // Try to parse if it's a string
                if (typeof message === 'string') {
                    try {
                        message = JSON.parse(message);
                    } catch (e) {
                        console.error('Failed to parse message from host:', e);
                        return;
                    }
                }
                
                // Only process messages with the 'adDashboard' type
                if (message.type !== 'adDashboard') return;
                
                console.log('Received message from host:', message);
                
                // Handle different action types
                switch (message.action) {
                    case 'updateData':
                        updateDashboardData(message.data);
                        break;
                    case 'alert':
                        addAlert(message.alert);
                        break;
                    case 'issue':
                        addIssue(message.issue);
                        break;
                    case 'reportStatus':
                        handleReportStatus(message.status, message.reportPath);
                        break;
                    default:
                        console.log('Unknown action:', message.action);
                }
            });
            
            // Request initial data
            requestDataFromHost();
        } else {
            console.log('WebView2 API not available');
            showNotification('WebView2 API not available. Dashboard will not receive live data.', false);
        }
    }
    
    // Request data from the host application
    function requestDataFromHost() {
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'adDashboard',
                action: 'getData',
                timeframe: currentTimeframe
            });
            
            console.log('Requested data for timeframe:', currentTimeframe);
        } else {
            console.log('WebView2 API not available - cannot request data');
        }
    }
    
    // Update dashboard with received data
    function updateDashboardData(data) {
        if (!data) return;
        
        console.log('Updating dashboard with data:', data);
        
        // Update overview stats
        if (data.overview) {
            const healthElement = document.getElementById('overallHealth');
            
            // Update text content
            document.getElementById('dcCount').textContent = data.overview.dcCount || '--';
            document.getElementById('serverCount').textContent = data.overview.serverCount || '--';
            document.getElementById('userCount').textContent = data.overview.userCount || '--';
            
            if (healthElement) {
                healthElement.textContent = data.overview.health || '--';
                
                // Update health indicator class
                healthElement.className = 'stat-value health-indicator';
                if (data.overview.health === 'Good') {
                    healthElement.classList.add('health-good');
                } else if (data.overview.health === 'Warning') {
                    healthElement.classList.add('health-warning');
                } else if (data.overview.health === 'Critical') {
                    healthElement.classList.add('health-critical');
                }
            }
        }
        
        // Update server status chart
        if (data.serverStatus && charts.serverStatus) {
            charts.serverStatus.data.datasets[0].data = data.serverStatus.data || [0, 0, 0, 0];
            if (data.serverStatus.labels) {
                charts.serverStatus.data.labels = data.serverStatus.labels;
            }
            charts.serverStatus.update();
        }
        
        // Update authentication activity chart
        if (data.authActivity && charts.authActivity) {
            charts.authActivity.data.labels = data.authActivity.labels || [];
            charts.authActivity.data.datasets[0].data = data.authActivity.success || [];
            charts.authActivity.data.datasets[1].data = data.authActivity.failed || [];
            charts.authActivity.update();
        }
        
        // Update replication health chart
        if (data.replication && charts.replication) {
            const replicationData = [
                data.replication.healthy || 0,
                data.replication.issues || 0
            ];
            charts.replication.data.datasets[0].data = replicationData;
            
            // Update the center text with the new percentage
            const healthyPercent = data.replication.healthy || 0;
            updateReplicationChartText(healthyPercent);
            
            charts.replication.update();
        }
        
        // Update resource utilization chart
        if (data.resources && charts.resourceUtilization) {
            charts.resourceUtilization.data.labels = data.resources.servers || [];
            charts.resourceUtilization.data.datasets[0].data = data.resources.cpu || [];
            charts.resourceUtilization.data.datasets[1].data = data.resources.memory || [];
            charts.resourceUtilization.update();
        }
        
        // Update security incidents chart
        if (data.security && charts.securityIncidents) {
            if (data.security.categories) {
                charts.securityIncidents.data.labels = data.security.categories;
            }
            charts.securityIncidents.data.datasets[0].data = data.security.counts || [];
            charts.securityIncidents.update();
        }
        
        // Removed Geographic Map data update
        
        // Update alerts
        if (data.alerts) {
            updateAlertsContainer(data.alerts);
        }
        
        // Update issues
        if (data.issues) {
            updateIssuesContainer(data.issues);
        }
        
        // Update dynamic chart size and layout after data update
        Object.keys(charts).forEach(key => {
            if (charts[key]) {
                charts[key].resize();
            }
        });
    }
    
    // Update alerts container with provided alerts
    function updateAlertsContainer(alerts) {
        const alertsContainer = document.getElementById('alertsContainer');
        if (!alertsContainer) return;
        
        if (!alerts || alerts.length === 0) {
            alertsContainer.innerHTML = '<div class="alert-placeholder">No alerts at this time.</div>';
            return;
        }
        
        alertsContainer.innerHTML = '';
        
        alerts.forEach(alert => {
            const alertElement = document.createElement('div');
            alertElement.className = `alert-item alert-${alert.severity.toLowerCase()}`;
            
            alertElement.innerHTML = `
                <div class="alert-title">${alert.title}</div>
                <div class="alert-details">${alert.message}</div>
                <div class="alert-timestamp">${alert.timestamp}</div>
            `;
            
            alertsContainer.appendChild(alertElement);
        });
    }
    
    // Update issues container with provided issues
    function updateIssuesContainer(issues) {
        const issuesContainer = document.getElementById('issuesContainer');
        if (!issuesContainer) return;
        
        if (!issues || issues.length === 0) {
            issuesContainer.innerHTML = '<div class="issue-placeholder">No critical issues detected.</div>';
            return;
        }
        
        issuesContainer.innerHTML = '';
        
        issues.forEach(issue => {
            const issueElement = document.createElement('div');
            issueElement.className = 'issue-item';
            
            issueElement.innerHTML = `
                <div class="issue-title">${issue.title}</div>
                <div class="issue-details">${issue.description}</div>
                <div class="issue-action">${issue.action}</div>
            `;
            
            issuesContainer.appendChild(issueElement);
        });
    }
    
    // Add a new alert to the container
    function addAlert(alert) {
        const alertsContainer = document.getElementById('alertsContainer');
        if (!alertsContainer) return;
        
        // Remove placeholder if present
        const placeholder = alertsContainer.querySelector('.alert-placeholder');
        if (placeholder) {
            alertsContainer.removeChild(placeholder);
        }
        
        // Create alert element
        const alertElement = document.createElement('div');
        alertElement.className = `alert-item alert-${alert.severity.toLowerCase()}`;
        alertElement.style.opacity = '0';
        alertElement.style.transition = 'opacity 0.5s';
        
        alertElement.innerHTML = `
            <div class="alert-title">${alert.title}</div>
            <div class="alert-details">${alert.message}</div>
            <div class="alert-timestamp">${alert.timestamp || new Date().toLocaleString()}</div>
        `;
        
        // Add to container at the top
        if (alertsContainer.firstChild) {
            alertsContainer.insertBefore(alertElement, alertsContainer.firstChild);
        } else {
            alertsContainer.appendChild(alertElement);
        }
        
        // Fade in
        setTimeout(() => {
            alertElement.style.opacity = '1';
        }, 10);
        
        // Show notification for critical alerts
        if (alert.severity.toLowerCase() === 'critical') {
            showNotification(`Critical Alert: ${alert.title}`);
        }
    }
    
    // Add a new issue to the container
    function addIssue(issue) {
        const issuesContainer = document.getElementById('issuesContainer');
        if (!issuesContainer) return;
        
        // Remove placeholder if present
        const placeholder = issuesContainer.querySelector('.issue-placeholder');
        if (placeholder) {
            issuesContainer.removeChild(placeholder);
        }
        
        // Create issue element
        const issueElement = document.createElement('div');
        issueElement.className = 'issue-item';
        issueElement.style.opacity = '0';
        issueElement.style.transition = 'opacity 0.5s';
        
        issueElement.innerHTML = `
            <div class="issue-title">${issue.title}</div>
            <div class="issue-details">${issue.description}</div>
            <div class="issue-action">${issue.action}</div>
        `;
        
        // Add to container at the top
        if (issuesContainer.firstChild) {
            issuesContainer.insertBefore(issueElement, issuesContainer.firstChild);
        } else {
            issuesContainer.appendChild(issueElement);
        }
        
        // Fade in
        setTimeout(() => {
            issueElement.style.opacity = '1';
        }, 10);
        
        // Show notification for new issues
        showNotification(`New Issue: ${issue.title}`);
    }
    
    // Generate a report based on selected options
    function generateReport() {
        // Get report parameters from form
        const reportType = document.getElementById('reportType').value;
        const reportTimeframe = document.getElementById('reportTimeframe').value;
        const reportFormat = document.getElementById('reportFormat').value;
        
        // Get selected sections
        const sections = [];
        document.querySelectorAll('input[name="sections"]:checked').forEach(checkbox => {
            sections.push(checkbox.value);
        });
        
        console.log('Generating report:', { reportType, reportTimeframe, reportFormat, sections });
        
        // Show notification
        showNotification('Generating report... This may take a moment.', true);
        
        // Send report request to host
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'adDashboard',
                action: 'generateReport',
                parameters: {
                    type: reportType,
                    timeframe: reportTimeframe,
                    format: reportFormat,
                    sections: sections
                }
            });
        } else {
            // Notify the user that WebView is not available
            showNotification('Cannot generate report: WebView API not available');
        }
    }
    
    // Handle report generation status
    function handleReportStatus(status, reportPath) {
        if (status === 'success') {
            showNotification('Report generated successfully!');
            
            if (reportPath && confirm(`Report saved to ${reportPath}. Would you like to open it?`)) {
                // Request host to open the file
                if (window.chrome && window.chrome.webview) {
                    window.chrome.webview.postMessage({
                        type: 'adDashboard',
                        action: 'openFile',
                        path: reportPath
                    });
                }
            }
        } else {
            showNotification('Failed to generate report: ' + (status === 'error' ? reportPath : 'Unknown error'));
        }
    }
    
    // Show a notification message
    function showNotification(message, isProcessing = false) {
        // Create or get notification container
        let notificationContainer = document.getElementById('notificationContainer');
        if (!notificationContainer) {
            notificationContainer = document.createElement('div');
            notificationContainer.id = 'notificationContainer';
            document.body.appendChild(notificationContainer);
        }
        
        // Remove any existing notification
        notificationContainer.innerHTML = '';
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'notification';
        
        if (isProcessing) {
            notification.innerHTML = `${message} <div class="spinner"></div>`;
        } else {
            notification.textContent = message;
        }
        
        notificationContainer.appendChild(notification);
        
        // Auto-hide after delay (unless processing)
        if (!isProcessing) {
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transition = 'opacity 0.5s';
                
                setTimeout(() => {
                    if (notification.parentNode === notificationContainer) {
                        notificationContainer.removeChild(notification);
                    }
                }, 500);
            }, 3000);
        }
    }
    
    // Make charts interactive for drill-down capabilities
    function setupChartInteractivity() {
        // Server status chart click handler
        setupChartClickHandler('serverStatusChart', 'serverStatus', (label, value) => {
            if (window.chrome && window.chrome.webview) {
                window.chrome.webview.postMessage({
                    type: 'adDashboard',
                    action: 'drillDown',
                    category: 'serverStatus',
                    filter: label,
                    value: value
                });
            } else {
                alert(`${label} servers: ${value}\nClick to view server details`);
            }
        });
        
        // Setup click handlers for other charts
        setupChartClickHandler('authActivityChart', 'authActivity');
        setupChartClickHandler('replicationChart', 'replication');
        setupChartClickHandler('resourceUtilizationChart', 'resourceUtilization');
        setupChartClickHandler('securityIncidentsChart', 'securityIncidents');
        
        // Removed Geographic Map click handler
    }
    
    // Helper function to set up chart click handlers
    function setupChartClickHandler(chartId, category, callback) {
        const chartElement = document.getElementById(chartId);
        if (!chartElement || !charts[category]) return;
        
        chartElement.addEventListener('click', function(event) {
            const chart = charts[category];
            const points = chart.getElementsAtEventForMode(event, 'nearest', { intersect: true }, true);
            
            if (points.length) {
                const firstPoint = points[0];
                const datasetIndex = firstPoint.datasetIndex;
                const index = firstPoint.index;
                
                let label, value;
                
                // Handle different chart types
                if (chart.config.type === 'bubble') {
                    // Bubble chart for geo map
                    label = chart.data.datasets[datasetIndex].label;
                    value = chart.data.datasets[datasetIndex].data[index];
                    alert(`${label} at position (${value.x}, ${value.y})`);
                } else if (chart.config.type === 'bar' || chart.config.type === 'line') {
                    // Bar or line charts (resource utilization, auth activity)
                    label = chart.data.labels[index];
                    value = chart.data.datasets[datasetIndex].data[index];
                    alert(`${chart.data.datasets[datasetIndex].label} for ${label}: ${value}`);
                } else {
                    // Pie, doughnut charts (server status, security incidents)
                    label = chart.data.labels[index];
                    value = chart.data.datasets[0].data[index];
                    
                    // Use custom callback if provided
                    if (typeof callback === 'function') {
                        callback(label, value);
                    } else {
                        alert(`${label}: ${value}`);
                    }
                }
                
                // Notify host application about interaction
                if (window.chrome && window.chrome.webview) {
                    window.chrome.webview.postMessage({
                        type: 'adDashboard',
                        action: 'chartInteraction',
                        chart: category,
                        label: label,
                        value: value
                    });
                }
            }
        });
    }
    
    // Add resize handler for responsive charts
    function setupResizeHandler() {
        window.addEventListener('resize', function() {
            // Debounce resize event to prevent excessive redraws
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(function() {
                console.log("Window resized, adjusting charts");
                
                // Update chart configurations
                updateChartResponsiveness();
                
                // Redraw all charts
                Object.keys(charts).forEach(key => {
                    if (charts[key]) {
                        charts[key].resize();
                        charts[key].update();
                    }
                });
            }, 250); // Wait for 250ms after resize ends
        });
    }
    
    // Update chart responsiveness based on screen size
    function updateChartResponsiveness() {
        const isSmallScreen = window.innerWidth < 768;
        
        // Update legend configuration
        chartConfig.plugins.legend.maxItems = isSmallScreen ? 3 : 6;
        
        // Update x-axis ticks
        chartConfig.scales.x.ticks.maxTicksLimit = isSmallScreen ? 5 : 10;
        
        // Adjust chart configurations based on screen size
        if (charts.authActivity) {
            // For line charts, reduce points on small screens
            charts.authActivity.options.elements = {
                line: {
                    tension: 0.3,
                    borderWidth: isSmallScreen ? 2 : 3
                },
                point: {
                    radius: isSmallScreen ? 2 : 3,
                    hitRadius: isSmallScreen ? 5 : 10
                }
            };
        }
        
        if (charts.resourceUtilization) {
            // For bar charts, adjust bar thickness
            charts.resourceUtilization.options.barThickness = isSmallScreen ? 'flex' : 20;
            charts.resourceUtilization.options.maxBarThickness = isSmallScreen ? 15 : 30;
        }
    }
    
    // Public API
    return {
        initialize,
        updateDashboardData,
        showNotification,
        addAlert,
        addIssue
    };
})();

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOMContentLoaded event fired in addashboard.js");
    
    // Check if the dashboard container exists in the DOM
    if (document.querySelector('.dashboard-container')) {
        console.log("Dashboard container found, initializing...");
        ADDashboard.initialize();
    } else {
        console.log("Dashboard container not found yet, will be initialized later");
    }
});

// Expose to window for WebView host access and manual initialization
window.ADDashboard = ADDashboard;

// Allow manual initialization when needed
if (document.readyState === 'complete' || document.readyState === 'interactive') {
    console.log("Document already loaded or interactive when addashboard.js loaded");
    
    // Defer execution slightly to ensure DOM is fully ready
    setTimeout(function() {
        if (document.querySelector('.dashboard-container') && !ADDashboard.isInitialized) {
            console.log("Dashboard container exists but not initialized yet, initializing now");
            ADDashboard.initialize();
        }
    }, 100);
}
