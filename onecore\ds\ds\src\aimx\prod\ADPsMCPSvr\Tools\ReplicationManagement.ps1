<#
.SYNOPSIS
    Active Directory Replication Management Tools for MCP Server
    
.DESCRIPTION
    This module registers MCP tools for Active Directory replication management operations.
    Each tool is a direct wrapper around the corresponding AD PowerShell cmdlet with
    exact parameter passthrough and no output formatting.

.AUTHOR
    Rup<PERSON> Zhang (rizhang)
#>

# Import required modules
Import-Module ActiveDirectory -ErrorAction SilentlyContinue

function Register-ReplicationManagementTools {
    [CmdletBinding()]
    param()

    # Get-ADReplicationSite - Returns a specific Active Directory replication site or a set of replication site objects based on a specified filter
    Register-McpTool -Name "Get-ADReplicationSite" -Description "Gets Active Directory replication sites based on specified criteria." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Filter) { $params.Filter = $Arguments.Filter }
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Properties) { $params.Properties = $Arguments.Properties }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        
        Get-ADReplicationSite @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Filter = @{ type = "string"; description = "PowerShell Expression Language filter string" }
            Identity = @{ type = "string"; description = "Site identity (DN, GUID, or name)" }
            Properties = @{ type = "array"; items = @{ type = "string" }; description = "Additional properties to retrieve" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
    }

    # New-ADReplicationSite - Creates an Active Directory replication site in the directory
    Register-McpTool -Name "New-ADReplicationSite" -Description "Creates a new Active Directory replication site." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Name) { $params.Name = $Arguments.Name }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.AutomaticInterSiteTopologyGenerationEnabled) { $params.AutomaticInterSiteTopologyGenerationEnabled = $Arguments.AutomaticInterSiteTopologyGenerationEnabled }
        if ($Arguments.AutomaticTopologyGenerationEnabled) { $params.AutomaticTopologyGenerationEnabled = $Arguments.AutomaticTopologyGenerationEnabled }
        if ($Arguments.InterSiteTopologyGenerator) { $params.InterSiteTopologyGenerator = $Arguments.InterSiteTopologyGenerator }
        if ($Arguments.ManagedBy) { $params.ManagedBy = $Arguments.ManagedBy }
        if ($Arguments.ProtectedFromAccidentalDeletion) { $params.ProtectedFromAccidentalDeletion = $Arguments.ProtectedFromAccidentalDeletion }
        if ($Arguments.RedundantServerTopologyEnabled) { $params.RedundantServerTopologyEnabled = $Arguments.RedundantServerTopologyEnabled }
        if ($Arguments.ScheduleHashingEnabled) { $params.ScheduleHashingEnabled = $Arguments.ScheduleHashingEnabled }
        if ($Arguments.TopologyCleanupEnabled) { $params.TopologyCleanupEnabled = $Arguments.TopologyCleanupEnabled }
        if ($Arguments.TopologyDetectStaleEnabled) { $params.TopologyDetectStaleEnabled = $Arguments.TopologyDetectStaleEnabled }
        if ($Arguments.TopologyMinimumHopsEnabled) { $params.TopologyMinimumHopsEnabled = $Arguments.TopologyMinimumHopsEnabled }
        if ($Arguments.UniversalGroupCachingEnabled) { $params.UniversalGroupCachingEnabled = $Arguments.UniversalGroupCachingEnabled }
        if ($Arguments.UniversalGroupCachingRefreshSite) { $params.UniversalGroupCachingRefreshSite = $Arguments.UniversalGroupCachingRefreshSite }
        if ($Arguments.WindowsServer2000BridgeheadSelectionMethodEnabled) { $params.WindowsServer2000BridgeheadSelectionMethodEnabled = $Arguments.WindowsServer2000BridgeheadSelectionMethodEnabled }
        if ($Arguments.WindowsServer2000KCCISTGSelectionBehaviorEnabled) { $params.WindowsServer2000KCCISTGSelectionBehaviorEnabled = $Arguments.WindowsServer2000KCCISTGSelectionBehaviorEnabled }
        if ($Arguments.WindowsServer2003KCCBehaviorEnabled) { $params.WindowsServer2003KCCBehaviorEnabled = $Arguments.WindowsServer2003KCCBehaviorEnabled }
        if ($Arguments.WindowsServer2003KCCIgnoreScheduleEnabled) { $params.WindowsServer2003KCCIgnoreScheduleEnabled = $Arguments.WindowsServer2003KCCIgnoreScheduleEnabled }
        if ($Arguments.WindowsServer2003KCCSiteLinkBridgingEnabled) { $params.WindowsServer2003KCCSiteLinkBridgingEnabled = $Arguments.WindowsServer2003KCCSiteLinkBridgingEnabled }
        if ($Arguments.OtherAttributes) { $params.OtherAttributes = $Arguments.OtherAttributes }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        New-ADReplicationSite @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Name = @{ type = "string"; description = "Name of the replication site (required)" }
            Description = @{ type = "string"; description = "Description of the site" }
            AutomaticInterSiteTopologyGenerationEnabled = @{ type = "boolean"; description = "Enable automatic inter-site topology generation" }
            AutomaticTopologyGenerationEnabled = @{ type = "boolean"; description = "Enable automatic topology generation" }
            InterSiteTopologyGenerator = @{ type = "string"; description = "Inter-site topology generator server" }
            ManagedBy = @{ type = "string"; description = "Distinguished name of the site manager" }
            ProtectedFromAccidentalDeletion = @{ type = "boolean"; description = "Protect from accidental deletion" }
            RedundantServerTopologyEnabled = @{ type = "boolean"; description = "Enable redundant server topology" }
            ScheduleHashingEnabled = @{ type = "boolean"; description = "Enable schedule hashing" }
            TopologyCleanupEnabled = @{ type = "boolean"; description = "Enable topology cleanup" }
            TopologyDetectStaleEnabled = @{ type = "boolean"; description = "Enable topology stale detection" }
            TopologyMinimumHopsEnabled = @{ type = "boolean"; description = "Enable topology minimum hops" }
            UniversalGroupCachingEnabled = @{ type = "boolean"; description = "Enable universal group caching" }
            UniversalGroupCachingRefreshSite = @{ type = "string"; description = "Universal group caching refresh site" }
            WindowsServer2000BridgeheadSelectionMethodEnabled = @{ type = "boolean"; description = "Enable Windows Server 2000 bridgehead selection method" }
            WindowsServer2000KCCISTGSelectionBehaviorEnabled = @{ type = "boolean"; description = "Enable Windows Server 2000 KCC ISTG selection behavior" }
            WindowsServer2003KCCBehaviorEnabled = @{ type = "boolean"; description = "Enable Windows Server 2003 KCC behavior" }
            WindowsServer2003KCCIgnoreScheduleEnabled = @{ type = "boolean"; description = "Enable Windows Server 2003 KCC ignore schedule" }
            WindowsServer2003KCCSiteLinkBridgingEnabled = @{ type = "boolean"; description = "Enable Windows Server 2003 KCC site link bridging" }
            OtherAttributes = @{ type = "object"; description = "Additional attributes as hashtable" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the created site object" }
        }
        required = @("Name")
    }

    # Set-ADReplicationSite - Sets the replication properties for an Active Directory site
    Register-McpTool -Name "Set-ADReplicationSite" -Description "Modifies the replication properties for an Active Directory site." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Add) { $params.Add = $Arguments.Add }
        if ($Arguments.Clear) { $params.Clear = $Arguments.Clear }
        if ($Arguments.Remove) { $params.Remove = $Arguments.Remove }
        if ($Arguments.Replace) { $params.Replace = $Arguments.Replace }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.AutomaticInterSiteTopologyGenerationEnabled) { $params.AutomaticInterSiteTopologyGenerationEnabled = $Arguments.AutomaticInterSiteTopologyGenerationEnabled }
        if ($Arguments.AutomaticTopologyGenerationEnabled) { $params.AutomaticTopologyGenerationEnabled = $Arguments.AutomaticTopologyGenerationEnabled }
        if ($Arguments.InterSiteTopologyGenerator) { $params.InterSiteTopologyGenerator = $Arguments.InterSiteTopologyGenerator }
        if ($Arguments.ManagedBy) { $params.ManagedBy = $Arguments.ManagedBy }
        if ($Arguments.ProtectedFromAccidentalDeletion) { $params.ProtectedFromAccidentalDeletion = $Arguments.ProtectedFromAccidentalDeletion }
        if ($Arguments.RedundantServerTopologyEnabled) { $params.RedundantServerTopologyEnabled = $Arguments.RedundantServerTopologyEnabled }
        if ($Arguments.ScheduleHashingEnabled) { $params.ScheduleHashingEnabled = $Arguments.ScheduleHashingEnabled }
        if ($Arguments.TopologyCleanupEnabled) { $params.TopologyCleanupEnabled = $Arguments.TopologyCleanupEnabled }
        if ($Arguments.TopologyDetectStaleEnabled) { $params.TopologyDetectStaleEnabled = $Arguments.TopologyDetectStaleEnabled }
        if ($Arguments.TopologyMinimumHopsEnabled) { $params.TopologyMinimumHopsEnabled = $Arguments.TopologyMinimumHopsEnabled }
        if ($Arguments.UniversalGroupCachingEnabled) { $params.UniversalGroupCachingEnabled = $Arguments.UniversalGroupCachingEnabled }
        if ($Arguments.UniversalGroupCachingRefreshSite) { $params.UniversalGroupCachingRefreshSite = $Arguments.UniversalGroupCachingRefreshSite }
        if ($Arguments.WindowsServer2000BridgeheadSelectionMethodEnabled) { $params.WindowsServer2000BridgeheadSelectionMethodEnabled = $Arguments.WindowsServer2000BridgeheadSelectionMethodEnabled }
        if ($Arguments.WindowsServer2000KCCISTGSelectionBehaviorEnabled) { $params.WindowsServer2000KCCISTGSelectionBehaviorEnabled = $Arguments.WindowsServer2000KCCISTGSelectionBehaviorEnabled }
        if ($Arguments.WindowsServer2003KCCBehaviorEnabled) { $params.WindowsServer2003KCCBehaviorEnabled = $Arguments.WindowsServer2003KCCBehaviorEnabled }
        if ($Arguments.WindowsServer2003KCCIgnoreScheduleEnabled) { $params.WindowsServer2003KCCIgnoreScheduleEnabled = $Arguments.WindowsServer2003KCCIgnoreScheduleEnabled }
        if ($Arguments.WindowsServer2003KCCSiteLinkBridgingEnabled) { $params.WindowsServer2003KCCSiteLinkBridgingEnabled = $Arguments.WindowsServer2003KCCSiteLinkBridgingEnabled }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Set-ADReplicationSite @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Site identity (DN, GUID, or name)" }
            Add = @{ type = "object"; description = "Attributes to add as hashtable" }
            Clear = @{ type = "array"; items = @{ type = "string" }; description = "Attributes to clear" }
            Remove = @{ type = "object"; description = "Attributes to remove as hashtable" }
            Replace = @{ type = "object"; description = "Attributes to replace as hashtable" }
            Description = @{ type = "string"; description = "Description of the site" }
            AutomaticInterSiteTopologyGenerationEnabled = @{ type = "boolean"; description = "Enable automatic inter-site topology generation" }
            AutomaticTopologyGenerationEnabled = @{ type = "boolean"; description = "Enable automatic topology generation" }
            InterSiteTopologyGenerator = @{ type = "string"; description = "Inter-site topology generator server" }
            ManagedBy = @{ type = "string"; description = "Distinguished name of the site manager" }
            ProtectedFromAccidentalDeletion = @{ type = "boolean"; description = "Protect from accidental deletion" }
            RedundantServerTopologyEnabled = @{ type = "boolean"; description = "Enable redundant server topology" }
            ScheduleHashingEnabled = @{ type = "boolean"; description = "Enable schedule hashing" }
            TopologyCleanupEnabled = @{ type = "boolean"; description = "Enable topology cleanup" }
            TopologyDetectStaleEnabled = @{ type = "boolean"; description = "Enable topology stale detection" }
            TopologyMinimumHopsEnabled = @{ type = "boolean"; description = "Enable topology minimum hops" }
            UniversalGroupCachingEnabled = @{ type = "boolean"; description = "Enable universal group caching" }
            UniversalGroupCachingRefreshSite = @{ type = "string"; description = "Universal group caching refresh site" }
            WindowsServer2000BridgeheadSelectionMethodEnabled = @{ type = "boolean"; description = "Enable Windows Server 2000 bridgehead selection method" }
            WindowsServer2000KCCISTGSelectionBehaviorEnabled = @{ type = "boolean"; description = "Enable Windows Server 2000 KCC ISTG selection behavior" }
            WindowsServer2003KCCBehaviorEnabled = @{ type = "boolean"; description = "Enable Windows Server 2003 KCC behavior" }
            WindowsServer2003KCCIgnoreScheduleEnabled = @{ type = "boolean"; description = "Enable Windows Server 2003 KCC ignore schedule" }
            WindowsServer2003KCCSiteLinkBridgingEnabled = @{ type = "boolean"; description = "Enable Windows Server 2003 KCC site link bridging" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified site object" }
        }
        required = @("Identity")
    }

    # Remove-ADReplicationSite - Deletes the specified replication site object from Active Directory
    Register-McpTool -Name "Remove-ADReplicationSite" -Description "Removes an Active Directory replication site." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Confirm) { $params.Confirm = $Arguments.Confirm }
        if ($Arguments.WhatIf) { $params.WhatIf = $Arguments.WhatIf }
        
        Remove-ADReplicationSite @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Site identity (DN, GUID, or name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Confirm = @{ type = "boolean"; description = "Prompt for confirmation before removing" }
            WhatIf = @{ type = "boolean"; description = "Show what would happen without executing" }
        }
        required = @("Identity")
    }

    # Get-ADReplicationSiteLink - Returns a specific Active Directory site link or a set of site links based on a specified filter
    Register-McpTool -Name "Get-ADReplicationSiteLink" -Description "Gets Active Directory replication site links based on specified criteria." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Filter) { $params.Filter = $Arguments.Filter }
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Properties) { $params.Properties = $Arguments.Properties }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        
        Get-ADReplicationSiteLink @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Filter = @{ type = "string"; description = "PowerShell Expression Language filter string" }
            Identity = @{ type = "string"; description = "Site link identity (DN, GUID, or name)" }
            Properties = @{ type = "array"; items = @{ type = "string" }; description = "Additional properties to retrieve" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
    }

    # New-ADReplicationSiteLink - Creates a new Active Directory site link for in managing replication
    Register-McpTool -Name "New-ADReplicationSiteLink" -Description "Creates a new Active Directory replication site link for managing replication." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Name) { $params.Name = $Arguments.Name }
        if ($Arguments.SitesIncluded) { $params.SitesIncluded = $Arguments.SitesIncluded }
        if ($Arguments.InterSiteTransportProtocol) { $params.InterSiteTransportProtocol = $Arguments.InterSiteTransportProtocol }
        if ($Arguments.Cost) { $params.Cost = $Arguments.Cost }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.ReplicationFrequencyInMinutes) { $params.ReplicationFrequencyInMinutes = $Arguments.ReplicationFrequencyInMinutes }
        if ($Arguments.ReplicationSchedule) { $params.ReplicationSchedule = $Arguments.ReplicationSchedule }
        if ($Arguments.OtherAttributes) { $params.OtherAttributes = $Arguments.OtherAttributes }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        New-ADReplicationSiteLink @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Name = @{ type = "string"; description = "Name of the site link (required)" }
            SitesIncluded = @{ type = "array"; items = @{ type = "string" }; description = "Array of site names to include in the link (required)" }
            InterSiteTransportProtocol = @{ type = "string"; enum = @("IP", "SMTP"); description = "Inter-site transport protocol (required)" }
            Cost = @{ type = "integer"; description = "Cost of the site link" }
            Description = @{ type = "string"; description = "Description of the site link" }
            ReplicationFrequencyInMinutes = @{ type = "integer"; description = "Replication frequency in minutes" }
            ReplicationSchedule = @{ type = "object"; description = "Replication schedule object" }
            OtherAttributes = @{ type = "object"; description = "Additional attributes as hashtable" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the created site link object" }
        }
        required = @("Name", "SitesIncluded", "InterSiteTransportProtocol")
    }

    # Set-ADReplicationSiteLink - Sets the properties for an Active Directory site link
    Register-McpTool -Name "Set-ADReplicationSiteLink" -Description "Modifies the properties for an Active Directory site link." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Add) { $params.Add = $Arguments.Add }
        if ($Arguments.Clear) { $params.Clear = $Arguments.Clear }
        if ($Arguments.Remove) { $params.Remove = $Arguments.Remove }
        if ($Arguments.Replace) { $params.Replace = $Arguments.Replace }
        if ($Arguments.Cost) { $params.Cost = $Arguments.Cost }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.ReplicationFrequencyInMinutes) { $params.ReplicationFrequencyInMinutes = $Arguments.ReplicationFrequencyInMinutes }
        if ($Arguments.ReplicationSchedule) { $params.ReplicationSchedule = $Arguments.ReplicationSchedule }
        if ($Arguments.SitesIncluded) { $params.SitesIncluded = $Arguments.SitesIncluded }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        Set-ADReplicationSiteLink @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Site link identity (DN, GUID, or name)" }
            Add = @{ type = "object"; description = "Attributes to add as hashtable" }
            Clear = @{ type = "array"; items = @{ type = "string" }; description = "Attributes to clear" }
            Remove = @{ type = "object"; description = "Attributes to remove as hashtable" }
            Replace = @{ type = "object"; description = "Attributes to replace as hashtable" }
            Cost = @{ type = "integer"; description = "Cost of the site link" }
            Description = @{ type = "string"; description = "Description of the site link" }
            ReplicationFrequencyInMinutes = @{ type = "integer"; description = "Replication frequency in minutes" }
            ReplicationSchedule = @{ type = "object"; description = "Replication schedule object" }
            SitesIncluded = @{ type = "array"; items = @{ type = "string" }; description = "Array of site names to include in the link" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified site link object" }
        }
        required = @("Identity")
    }

    # Remove-ADReplicationSiteLink - Deletes an Active Directory site link used to manage replication
    Register-McpTool -Name "Remove-ADReplicationSiteLink" -Description "Removes an Active Directory replication site link." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Confirm) { $params.Confirm = $Arguments.Confirm }
        if ($Arguments.WhatIf) { $params.WhatIf = $Arguments.WhatIf }

        Remove-ADReplicationSiteLink @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Site link identity (DN, GUID, or name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Confirm = @{ type = "boolean"; description = "Prompt for confirmation before removing" }
            WhatIf = @{ type = "boolean"; description = "Show what would happen without executing" }
        }
        required = @("Identity")
    }

    # Get-ADReplicationSiteLinkBridge - Gets a specific Active Directory site link bridge or a set of site link bridge objects based on a specified filter
    Register-McpTool -Name "Get-ADReplicationSiteLinkBridge" -Description "Gets Active Directory replication site link bridges based on specified criteria." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Filter) { $params.Filter = $Arguments.Filter }
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Properties) { $params.Properties = $Arguments.Properties }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }

        Get-ADReplicationSiteLinkBridge @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Filter = @{ type = "string"; description = "PowerShell Expression Language filter string" }
            Identity = @{ type = "string"; description = "Site link bridge identity (DN, GUID, or name)" }
            Properties = @{ type = "array"; items = @{ type = "string" }; description = "Additional properties to retrieve" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
    }

    # New-ADReplicationSiteLinkBridge - Creates a site link bridge in Active Directory for replication
    Register-McpTool -Name "New-ADReplicationSiteLinkBridge" -Description "Creates a new Active Directory replication site link bridge." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Name) { $params.Name = $Arguments.Name }
        if ($Arguments.SiteLinksIncluded) { $params.SiteLinksIncluded = $Arguments.SiteLinksIncluded }
        if ($Arguments.InterSiteTransportProtocol) { $params.InterSiteTransportProtocol = $Arguments.InterSiteTransportProtocol }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.OtherAttributes) { $params.OtherAttributes = $Arguments.OtherAttributes }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        New-ADReplicationSiteLinkBridge @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Name = @{ type = "string"; description = "Name of the site link bridge (required)" }
            SiteLinksIncluded = @{ type = "array"; items = @{ type = "string" }; description = "Array of site link names to include in the bridge (required)" }
            InterSiteTransportProtocol = @{ type = "string"; enum = @("IP", "SMTP"); description = "Inter-site transport protocol (required)" }
            Description = @{ type = "string"; description = "Description of the site link bridge" }
            OtherAttributes = @{ type = "object"; description = "Additional attributes as hashtable" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the created site link bridge object" }
        }
        required = @("Name", "SiteLinksIncluded", "InterSiteTransportProtocol")
    }

    # Set-ADReplicationSiteLinkBridge - Sets the properties of a replication site link bridge in Active Directory
    Register-McpTool -Name "Set-ADReplicationSiteLinkBridge" -Description "Modifies the properties of a replication site link bridge in Active Directory." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Add) { $params.Add = $Arguments.Add }
        if ($Arguments.Clear) { $params.Clear = $Arguments.Clear }
        if ($Arguments.Remove) { $params.Remove = $Arguments.Remove }
        if ($Arguments.Replace) { $params.Replace = $Arguments.Replace }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.SiteLinksIncluded) { $params.SiteLinksIncluded = $Arguments.SiteLinksIncluded }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        Set-ADReplicationSiteLinkBridge @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Site link bridge identity (DN, GUID, or name)" }
            Add = @{ type = "object"; description = "Attributes to add as hashtable" }
            Clear = @{ type = "array"; items = @{ type = "string" }; description = "Attributes to clear" }
            Remove = @{ type = "object"; description = "Attributes to remove as hashtable" }
            Replace = @{ type = "object"; description = "Attributes to replace as hashtable" }
            Description = @{ type = "string"; description = "Description of the site link bridge" }
            SiteLinksIncluded = @{ type = "array"; items = @{ type = "string" }; description = "Array of site link names to include in the bridge" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified site link bridge object" }
        }
        required = @("Identity")
    }

    # Remove-ADReplicationSiteLinkBridge - Deletes a replication site link bridge from Active Directory
    Register-McpTool -Name "Remove-ADReplicationSiteLinkBridge" -Description "Removes a replication site link bridge from Active Directory." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Confirm) { $params.Confirm = $Arguments.Confirm }
        if ($Arguments.WhatIf) { $params.WhatIf = $Arguments.WhatIf }

        Remove-ADReplicationSiteLinkBridge @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Site link bridge identity (DN, GUID, or name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Confirm = @{ type = "boolean"; description = "Prompt for confirmation before removing" }
            WhatIf = @{ type = "boolean"; description = "Show what would happen without executing" }
        }
        required = @("Identity")
    }

    # Get-ADReplicationAttributeMetadata - Gets the replication metadata for one or more Active Directory replication partners
    Register-McpTool -Name "Get-ADReplicationAttributeMetadata" -Description "Gets the replication metadata for one or more Active Directory replication partners." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Object) { $params.Object = $Arguments.Object }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.Properties) { $params.Properties = $Arguments.Properties }
        if ($Arguments.ShowAllLinkedValues) { $params.ShowAllLinkedValues = $Arguments.ShowAllLinkedValues }
        if ($Arguments.IncludeDeletedObjects) { $params.IncludeDeletedObjects = $Arguments.IncludeDeletedObjects }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }

        Get-ADReplicationAttributeMetadata @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Object = @{ type = "string"; description = "Object identity (DN, GUID, or name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            Properties = @{ type = "array"; items = @{ type = "string" }; description = "Specific properties to get metadata for" }
            ShowAllLinkedValues = @{ type = "boolean"; description = "Show all linked values" }
            IncludeDeletedObjects = @{ type = "boolean"; description = "Include deleted objects" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
        required = @("Object")
    }

    # Get-ADReplicationConnection - Returns a specific Active Directory replication connection or a set of AD replication connection objects based on a specified filter
    Register-McpTool -Name "Get-ADReplicationConnection" -Description "Returns a specific Active Directory replication connection or a set of AD replication connection objects based on a specified filter." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Filter) { $params.Filter = $Arguments.Filter }
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Properties) { $params.Properties = $Arguments.Properties }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }

        Get-ADReplicationConnection @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Filter = @{ type = "string"; description = "PowerShell Expression Language filter string" }
            Identity = @{ type = "string"; description = "Replication connection identity (DN, GUID, or name)" }
            Properties = @{ type = "array"; items = @{ type = "string" }; description = "Additional properties to retrieve" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
    }

    # Set-ADReplicationConnection - Sets properties on Active Directory replication connections
    Register-McpTool -Name "Set-ADReplicationConnection" -Description "Sets properties on Active Directory replication connections." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Add) { $params.Add = $Arguments.Add }
        if ($Arguments.Clear) { $params.Clear = $Arguments.Clear }
        if ($Arguments.Remove) { $params.Remove = $Arguments.Remove }
        if ($Arguments.Replace) { $params.Replace = $Arguments.Replace }
        if ($Arguments.ReplicateFromDirectoryServer) { $params.ReplicateFromDirectoryServer = $Arguments.ReplicateFromDirectoryServer }
        if ($Arguments.ReplicationSchedule) { $params.ReplicationSchedule = $Arguments.ReplicationSchedule }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        Set-ADReplicationConnection @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Replication connection identity (DN, GUID, or name)" }
            Add = @{ type = "object"; description = "Attributes to add as hashtable" }
            Clear = @{ type = "array"; items = @{ type = "string" }; description = "Attributes to clear" }
            Remove = @{ type = "object"; description = "Attributes to remove as hashtable" }
            Replace = @{ type = "object"; description = "Attributes to replace as hashtable" }
            ReplicateFromDirectoryServer = @{ type = "string"; description = "Source directory server for replication" }
            ReplicationSchedule = @{ type = "object"; description = "Replication schedule object" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified replication connection object" }
        }
        required = @("Identity")
    }

    # Get-ADReplicationFailure - Returns a collection of data describing an Active Directory replication failure
    Register-McpTool -Name "Get-ADReplicationFailure" -Description "Returns a collection of data describing an Active Directory replication failure." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Target) { $params.Target = $Arguments.Target }
        if ($Arguments.Scope) { $params.Scope = $Arguments.Scope }
        if ($Arguments.EnumerationServer) { $params.EnumerationServer = $Arguments.EnumerationServer }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }

        Get-ADReplicationFailure @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Target = @{ type = "array"; items = @{ type = "string" }; description = "Target domain controllers or sites" }
            Scope = @{ type = "string"; enum = @("Domain", "Forest", "Site"); description = "Scope of the failure check" }
            EnumerationServer = @{ type = "string"; description = "Server to use for enumeration" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
        required = @("Target")
    }

    # Get-ADReplicationPartnerMetadata - Returns the replication metadata for a set of one or more replication partners
    Register-McpTool -Name "Get-ADReplicationPartnerMetadata" -Description "Returns the replication metadata for a set of one or more replication partners." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Target) { $params.Target = $Arguments.Target }
        if ($Arguments.Scope) { $params.Scope = $Arguments.Scope }
        if ($Arguments.PartnerType) { $params.PartnerType = $Arguments.PartnerType }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.EnumerationServer) { $params.EnumerationServer = $Arguments.EnumerationServer }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }

        Get-ADReplicationPartnerMetadata @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Target = @{ type = "array"; items = @{ type = "string" }; description = "Target domain controllers or sites" }
            Scope = @{ type = "string"; enum = @("Domain", "Forest", "Site"); description = "Scope of the metadata check" }
            PartnerType = @{ type = "string"; enum = @("Inbound", "Outbound", "Both"); description = "Type of replication partners" }
            Partition = @{ type = "array"; items = @{ type = "string" }; description = "Specific partitions to check" }
            EnumerationServer = @{ type = "string"; description = "Server to use for enumeration" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
        required = @("Target")
    }

    # Get-ADReplicationQueueOperation - Returns the contents of the replication queue for a specified server
    Register-McpTool -Name "Get-ADReplicationQueueOperation" -Description "Returns the contents of the replication queue for a specified server." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.Filter) { $params.Filter = $Arguments.Filter }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }

        Get-ADReplicationQueueOperation @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Server = @{ type = "string"; description = "Domain controller to get queue operations from" }
            Filter = @{ type = "string"; description = "Filter for queue operations" }
            Partition = @{ type = "array"; items = @{ type = "string" }; description = "Specific partitions to check" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
        required = @("Server")
    }

    # Get-ADReplicationUpToDatenessVectorTable - Displays the highest Update Sequence Number (USN) for the specified domain controller
    Register-McpTool -Name "Get-ADReplicationUpToDatenessVectorTable" -Description "Displays the highest Update Sequence Number (USN) for the specified domain controller." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Target) { $params.Target = $Arguments.Target }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.EnumerationServer) { $params.EnumerationServer = $Arguments.EnumerationServer }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }

        Get-ADReplicationUpToDatenessVectorTable @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Target = @{ type = "array"; items = @{ type = "string" }; description = "Target domain controllers" }
            Partition = @{ type = "array"; items = @{ type = "string" }; description = "Specific partitions to check" }
            EnumerationServer = @{ type = "string"; description = "Server to use for enumeration" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
        required = @("Target")
    }
}

# Function is available after dot-sourcing
