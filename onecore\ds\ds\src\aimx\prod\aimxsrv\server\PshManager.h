/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    PshManager.h

Abstract:

    PowerShell manager for execution and command search via NetRag service.

--*/

#pragma once

#include <windows.h>
#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <shared_mutex>
#include "nlohmann/json.hpp"

// PowerShell execution result
struct POWERSHELL_EXECUTION_RESULT
{
    bool success;
    nlohmann::json result;
    std::wstring errorMessage;
};

// PowerShell command search result with full context
struct POWERSHELL_COMMAND_SEARCH_RESULT
{
    std::wstring commandName;
    std::wstring fullText;
    std::wstring parameterNames;
    std::wstring id;
    int parameterCount;
    int exampleCount;
    double score;
    nlohmann::json metadata;
};

/*
JSON Format Explanation:
- "CommandName": The name of the PowerShell cmdlet (e.g., "Get-ADUser").
- "Syntax": A list of valid syntax patterns the cmdlet can follow. Each string represents one possible usage pattern.
- "Parameters": A list of parameter definitions. Each item contains:
    - "Name": The name of the parameter (e.g., "Filter", "Identity").
    - "Description": A plain-text explanation of what the parameter does.
    - "Details": Metadata about the parameter, including:
        - "Required": Whether this parameter is mandatory.
        - "Type": The data type of the parameter (e.g., "String", "PSCredential").
        - "AcceptPipelineInput": Whether the cmdlet can accept this parameter from pipeline input.
        - "Default": The default value if not specified.
        - "Position": Whether this is a positional or named parameter.
- "Examples": A list of example usages of the cmdlet. Each example includes:
    - "Code": A PowerShell command line using the cmdlet.
    - "Description": A short explanation of what the command does.

Sample JSON:
 {
        "CommandName":  "Add-ADCentralAccessPolicyMember",
        "Syntax":  [
                       "Add-ADCentralAccessPolicyMember\n    [-WhatIf]\n    [-Confirm]\n    [-AuthType ]\n    [-Credential ]\n    [-Identity] \n    [-Members] \n    [-PassThru]\n    [-Server ]\n    []"
                   ],
        "Parameters":  [
                           {
                               "Name":  "AuthType",
                               "Description":  "The default authentication method is Negotiate. Negotiate or 0, Basic or 1",
                               "Details":  {
                                               "AcceptPipelineInput":  "False",
                                               "Required":  "False",
                                               "AcceptWildcardCharacters":  "False",
                                               "Type":  "Microsoft.ActiveDirectory.Management.ADAuthType",
                                               "Aliases":  "",
                                               "Default":  "None",
                                               "Position":  "Named"
                                           }
                           },
                           {
                               "Name":  "Confirm",
                               "Description":  "Prompts you for confirmation before running the cmdlet.",
                               "Details":  {
                                               "AcceptPipelineInput":  "False",
                                               "Required":  "False",
                                               "AcceptWildcardCharacters":  "False",
                                               "Type":  "SwitchParameter",
                                               "Aliases":  "cf",
                                               "Default":  "False",
                                               "Position":  "Named"
                                           }
                           },
                           {
                               "Name":  "Credential",
                               "Description":  "",
                               "Details":  {
                                               "AcceptPipelineInput":  "False",
                                               "Required":  "False",
                                               "AcceptWildcardCharacters":  "False",
                                               "Type":  "PSCredential",
                                               "Aliases":  "",
                                               "Default":  "None",
                                               "Position":  "Named"
                                           }
                           },
                           {
                               "Name":  "Identity",
                               "Description":  "",
                               "Details":  {
                                               "AcceptPipelineInput":  "True",
                                               "Required":  "True",
                                               "AcceptWildcardCharacters":  "False",
                                               "Type":  "Microsoft.ActiveDirectory.Management.ADCentralAccessPolicy",
                                               "Aliases":  "",
                                               "Default":  "None",
                                               "Position":  "0"
                                           }
                           },
                           {
                               "Name":  "Members",
                               "Description":  "Note Name, A distinguished name, GUID (objectGUID)",
                               "Details":  {
                                               "AcceptPipelineInput":  "False",
                                               "Required":  "True",
                                               "AcceptWildcardCharacters":  "False",
                                               "Type":  "Microsoft.ActiveDirectory.Management.ADCentralAccessRule[]",
                                               "Aliases":  "",
                                               "Default":  "None",
                                               "Position":  "1"
                                           }
                           },
                           {
                               "Name":  "PassThru",
                               "Description":  "",
                               "Details":  {
                                               "AcceptPipelineInput":  "False",
                                               "Required":  "False",
                                               "AcceptWildcardCharacters":  "False",
                                               "Type":  "SwitchParameter",
                                               "Aliases":  "",
                                               "Default":  "None",
                                               "Position":  "Named"
                                           }
                           },
                           {
                               "Name":  "Server",
                               "Description":  "Specify the Active Directory Domain Services instance in one of the following ways: Fully qualified domain name, NetBIOS name Fully qualified directory server name, NetBIOS name, Fully qualified directory server name and port By using the Server value from objects passed through the pipeline, By using the domain of the computer running Windows PowerShell",
                               "Details":  {
                                               "AcceptPipelineInput":  "False",
                                               "Required":  "False",
                                               "AcceptWildcardCharacters":  "False",
                                               "Type":  "String",
                                               "Aliases":  "",
                                               "Default":  "None",
                                               "Position":  "Named"
                                           }
                           },
                           {
                               "Name":  "WhatIf",
                               "Description":  "Shows what would happen if the cmdlet runs. The cmdlet isn\u0027t run.",
                               "Details":  {
                                               "AcceptPipelineInput":  "False",
                                               "Required":  "False",
                                               "AcceptWildcardCharacters":  "False",
                                               "Type":  "SwitchParameter",
                                               "Aliases":  "wi",
                                               "Default":  "False",
                                               "Position":  "Named"
                                           }
                           },
                           {
                               "Name":  "CommonParameters",
                               "Description":  "This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable,\n-InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable,\n-ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see\nabout_CommonParameters.",
                               "Details":  {
                                               "Type":  "CommonParameters",
                                               "Position":  "Named",
                                               "Default":  "",
                                               "Required":  "False",
                                               "AcceptPipelineInput":  "False",
                                               "AcceptWildcardCharacters":  "False",
                                               "Aliases":  ""
                                           }
                           }
                       ],
        "Examples":  [
                         {
                             "Code":  "$params = @{\n    Identity = \u0027Finance Policy\u0027\n    Member = \u0027Finance Documents Rule\u0027, \u0027Corporate Documents Rule\u0027\n}\nAdd-ADCentralAccessPolicyMember @params",
                             "Description":  "This command adds the central access rules Finance Documents Rule and Corporate Documents Rule\nto the central access policy Finance Policy."
                         },
                         {
                             "Code":  "Get-ADCentralAccessPolicy -Filter \"Name -like \u0027Corporate*\u0027\" |\n    Add-ADCentralAccessPolicyMember -Members \u0027Corporate Documents Rule\u0027",
                             "Description":  "This command gets all central access policies that have a name that starts with Corporate and then\npasses this information to Add-ADCentralAccessPolicyMember by using the pipeline operator. The\nAdd-ADCentralAccessPolicyMember cmdlet then adds the central access rule with the name\nCorporate Documents Rule to it."
                         }
                     ]
    }

*/

// PowerShell command parameter details
struct POWERSHELL_PARAMETER_DETAILS
{
    std::wstring name;
    std::wstring description;
    std::wstring type;
    bool required;
    bool acceptPipelineInput;
    std::wstring defaultValue;
    std::wstring position;
    std::wstring aliases;
};

// PowerShell command example
struct POWERSHELL_COMMAND_EXAMPLE
{
    std::wstring code;
    std::wstring description;
};

// Comprehensive PowerShell command context
struct POWERSHELL_COMMAND_CONTEXT
{
    std::wstring commandName;
    std::vector<std::wstring> syntax;
    std::vector<POWERSHELL_PARAMETER_DETAILS> parameters;
    std::vector<POWERSHELL_COMMAND_EXAMPLE> examples;
};

class PshManager
{
public:
    // Execute PowerShell tool via HTTP to NetRag service
    static HRESULT ExecuteTool(
        _In_ const std::wstring& toolName,
        _In_ const nlohmann::json& parameters,
        _Out_ POWERSHELL_EXECUTION_RESULT& result
        );

    // Search for PowerShell commands with full context using semantic similarity
    static HRESULT SearchPowerShellCommands(
        _In_ const std::wstring& query,
        _In_ int topK,
        _Out_ std::vector<POWERSHELL_COMMAND_SEARCH_RESULT>& results
        );

    // PowerShell context management (using raw JSON)
    static HRESULT LoadPowerShellContextData();
    static HRESULT GetPowerShellCommandContextJson(
        _In_ const std::wstring& commandName,
        _Out_ nlohmann::json& contextJson
        );
    static std::wstring FormatPowerShellCommandJsonForPrompt(
        _In_ const nlohmann::json& contextJson,
        _In_ const POWERSHELL_COMMAND_SEARCH_RESULT& searchResult
        );

    // Command selection using LLM
    static HRESULT SelectBestPowerShellCommand(
        _In_ const std::wstring& userQuery,
        _In_ const std::vector<POWERSHELL_COMMAND_SEARCH_RESULT>& searchResults,
        _Out_ std::wstring& selectedCommandName,
        _Out_ std::wstring& selectionReasoning
        );

private:
    // Build PowerShell command from tool name and parameters
    static HRESULT BuildPowerShellCommand(
        _In_ const std::wstring& toolName,
        _In_ const nlohmann::json& parameters,
        _Out_ std::wstring& command
        );

    // Parse NetRag HTTP response
    static HRESULT ParseNetRagResponse(
        _In_ const nlohmann::json& httpResponse,
        _Out_ POWERSHELL_EXECUTION_RESULT& result
        );

    // Execute PowerShell command via HTTP to NetRag service
    static HRESULT ExecutePowerShellViaHttp(
        _In_ const std::wstring& command,
        _In_ int timeoutSeconds,
        _Out_ nlohmann::json& response
        );

    // Send HTTP GET request to NetRag service
    static HRESULT SendHttpGetRequest(
        _In_ const std::wstring& endpoint,
        _Out_ nlohmann::json& response
        );

    // Send HTTP POST request to NetRag service
    static HRESULT SendHttpPostRequest(
        _In_ const std::wstring& endpoint,
        _In_ const nlohmann::json& requestBody,
        _Out_ nlohmann::json& response
        );

    // PowerShell context data cache (static members) - storing raw JSON
    static std::unordered_map<std::wstring, nlohmann::json> s_powerShellContextCache;
    static std::shared_mutex s_contextCacheMutex;
    static bool s_contextDataLoaded;
};
