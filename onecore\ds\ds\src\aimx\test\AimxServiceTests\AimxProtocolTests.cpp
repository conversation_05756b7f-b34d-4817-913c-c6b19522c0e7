/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    AimxProtocolTests.cpp

Abstract:
    Comprehensive TAEF test suite for AIMX RPC Server protocol testing.
    Tests the complete workflow including <PERSON><PERSON><PERSON><PERSON><PERSON>, Planner, and <PERSON><PERSON>.

--*/

#include "pch.hxx"
#include "aimxrpcclient.h"
#include "AimxProtocolTests.h"
#include <thread>
#include <chrono>
#include <nlohmann/json.hpp>

void AimxProtocolTests::TestChatbotQueryWorkflow()
{
    GUID contextId = {};
    HRESULT hr = S_OK;
    AimxRpcClient* rpcClient = new AimxRpcClient();
    
    Log::Comment(L"Starting TestChatbotQueryWorkflow");
    
    VERIFY_IS_NOT_NULL(rpcClient, L"Failed to create AimxRpcClient instance");

    // Step 1: Connect to server
    hr = rpcClient->AimxConnect(&contextId);
    VERIFY_SUCCEEDED(hr, L"AimxConnect should succeed");
    VERIFY_IS_FALSE(IsEqualGUID(contextId, GUID{}), L"ContextId should not be GUID_NULL after connect");

    // Step 2: Send chatbot query request
    nlohmann::json request;
    request["requestType"] = 1; // AIMX_CHATBOT_QUERY
    request["query"] = "What is the current DC password policy";
    request["executionMode"] = 2; // AIMX_MODE_INTERACTIVE

    std::string requestStr = request.dump();
    std::wstring requestWStr(requestStr.begin(), requestStr.end());

    LPWSTR response = nullptr;
    hr = rpcClient->AimxProcessPrompt(contextId, requestWStr.c_str(), &response);
    VERIFY_SUCCEEDED(hr, L"AimxProcessPrompt should succeed for chatbot query");
    VERIFY_IS_NOT_NULL(response, L"Response should not be null");

    // Parse response
    std::wstring responseWStr(response);
    std::string responseStr(responseWStr.begin(), responseWStr.end());
    nlohmann::json responseJson = nlohmann::json::parse(responseStr);

    Log::Comment(L"Chatbot Query Response:");
    Log::Comment(String().Format(L"%ws", response));

    VERIFY_IS_TRUE(responseJson["success"].get<bool>(), L"Response should indicate success");
    VERIFY_IS_TRUE(responseJson.contains("operationId"), L"Response should contain operationId");

    // extract the contents of status field into a local variable for verification
    std::string status = responseJson["status"].get<std::string>();
    Log::Comment(String().Format(L"Initial Status: %hs", status.c_str()));
    if (0 != strcmp("PLANNING", status.c_str())) {
        VERIFY_FAIL();
    }

    std::string operationId = responseJson["operationId"].get<std::string>();
    if (response) MIDL_user_free(response);

    // Step 3: Poll for planning completion
    bool planReady = false;
    int pollCount = 0;
    const int maxPolls = 30; // 30 seconds timeout

    while (!planReady && pollCount < maxPolls)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        
        nlohmann::json statusRequest;
        statusRequest["requestType"] = 3; // AIMX_PLAN_STATUS
        statusRequest["operationId"] = operationId;

        std::string statusRequestStr = statusRequest.dump();
        std::wstring statusRequestWStr(statusRequestStr.begin(), statusRequestStr.end());

        response = nullptr;
        hr = rpcClient->AimxProcessPrompt(contextId, statusRequestWStr.c_str(), &response);
        VERIFY_SUCCEEDED(hr, L"Plan status query should succeed");
        VERIFY_IS_NOT_NULL(response, L"Status response should not be null");

        std::wstring statusResponseWStr(response);
        std::string statusResponseStr(statusResponseWStr.begin(), statusResponseWStr.end());
        nlohmann::json statusResponseJson = nlohmann::json::parse(statusResponseStr);

        Log::Comment(String().Format(L"Poll #%d Status: %hs", 
            pollCount + 1, 
            statusResponseJson["status"].get<std::string>().c_str()));

        if (0 == strcmp("PLAN_READY", statusResponseJson["status"].get<std::string>().c_str()))
        {
            planReady = true;
            VERIFY_IS_TRUE(statusResponseJson.contains("executionPlan"), L"Plan ready response should contain execution plan");

            // Parse execution plan as JSON and validate structure
            nlohmann::json planJson;
            try {
                planJson = nlohmann::json::parse(statusResponseJson["executionPlan"].get<std::string>());
            } catch (...) {
                VERIFY_FAIL(L"Execution plan is not valid JSON");
            }
            VERIFY_IS_TRUE(planJson.contains("planType"), L"Execution plan should contain planType");
            VERIFY_IS_TRUE(planJson.contains("steps"), L"Execution plan should contain steps");
            VERIFY_IS_TRUE(planJson["steps"].is_array(), L"Execution plan steps should be an array");

            Log::Comment(L"Execution Plan (parsed JSON):");
            Log::Comment(String().Format(L"%hs", planJson.dump(2).c_str()));
        }

        if (response) MIDL_user_free(response);
        pollCount++;
    }

    VERIFY_IS_TRUE(planReady, L"Plan should be ready within timeout period");

    // Step 4: Execute the plan
    nlohmann::json executeRequest;
    executeRequest["requestType"] = 4; // AIMX_EXECUTE_PLAN
    executeRequest["operationId"] = operationId;

    std::string executeRequestStr = executeRequest.dump();
    std::wstring executeRequestWStr(executeRequestStr.begin(), executeRequestStr.end());

    response = nullptr;
    hr = rpcClient->AimxProcessPrompt(contextId, executeRequestWStr.c_str(), &response);
    VERIFY_SUCCEEDED(hr, L"Execute plan should succeed");
    VERIFY_IS_NOT_NULL(response, L"Execute response should not be null");

    std::wstring executeResponseWStr(response);
    std::string executeResponseStr(executeResponseWStr.begin(), executeResponseWStr.end());
    nlohmann::json executeResponseJson = nlohmann::json::parse(executeResponseStr);

    Log::Comment(L"Execute Plan Response:");
    Log::Comment(String().Format(L"%ws", response));

    VERIFY_IS_TRUE(executeResponseJson["success"].get<bool>(), L"Execute response should indicate success");
    if (0 != strcmp("EXECUTING", executeResponseJson["status"].get<std::string>().c_str())) {
        VERIFY_FAIL(L"Expected status EXECUTING");
    }
   Log::Comment(L"Status is EXECUTING as expected");

    if (response) MIDL_user_free(response);

    // Step 5: Poll for execution completion
    bool executionComplete = false;
    pollCount = 0;
    const int maxExecutionPolls = 60; // 60 seconds timeout for execution

    while (!executionComplete && pollCount < maxExecutionPolls)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        
        nlohmann::json statusRequest;
        statusRequest["requestType"] = 3; // AIMX_PLAN_STATUS
        statusRequest["operationId"] = operationId;

        std::string statusRequestStr = statusRequest.dump();
        std::wstring statusRequestWStr(statusRequestStr.begin(), statusRequestStr.end());

        response = nullptr;
        hr = rpcClient->AimxProcessPrompt(contextId, statusRequestWStr.c_str(), &response);
        VERIFY_SUCCEEDED(hr, L"Execution status query should succeed");
        VERIFY_IS_NOT_NULL(response, L"Execution status response should not be null");

        std::wstring statusResponseWStr(response);
        std::string statusResponseStr(statusResponseWStr.begin(), statusResponseWStr.end());
        nlohmann::json statusResponseJson = nlohmann::json::parse(statusResponseStr);

        std::string currentStatus = statusResponseJson["status"].get<std::string>();
        Log::Comment(String().Format(L"Execution Poll #%d Status: %hs", pollCount + 1, currentStatus.c_str()));

        if (0 == strcmp("EXECUTING", currentStatus.c_str()))
        {
            Log::Comment(L"Execution is still in progress...");
        }
        else if (0 == strcmp("PLANNING", currentStatus.c_str()))
        {
            Log::Comment(L"Execution is still planning...");
        }
        else if (0 == strcmp("COMPLETED", currentStatus.c_str()) || (0 == strcmp("FAILED", currentStatus.c_str())))
        {
            executionComplete = true;

            if (0 == strcmp("COMPLETED", currentStatus.c_str()))
            {
                VERIFY_IS_TRUE(statusResponseJson.contains("result"), L"Completed response should contain result");
                Log::Comment(L"Execution Result:");
                Log::Comment(String().Format(L"%hs", statusResponseJson["result"].get<std::string>().c_str()));
            }
        }

        if (response) MIDL_user_free(response);
        pollCount++;
    }

    VERIFY_IS_TRUE(executionComplete, L"Execution completed within timeout period");

    // Cleanup
    hr = rpcClient->AimxClose(contextId);
    VERIFY_SUCCEEDED(hr, L"AimxClose succeeded");

    delete rpcClient;
    Log::Comment(L"TestChatbotQueryWorkflow completed successfully");
}

void AimxProtocolTests::TestDirectQueryAutomatedMode()
{
    GUID contextId = {};
    HRESULT hr = S_OK;
    AimxRpcClient* rpcClient = new AimxRpcClient();
    
    Log::Comment(L"Starting TestDirectQueryAutomatedMode");
    
    VERIFY_IS_NOT_NULL(rpcClient, L"Failed to create AimxRpcClient instance");

    // Connect to server
    hr = rpcClient->AimxConnect(&contextId);
    VERIFY_SUCCEEDED(hr, L"AimxConnect should succeed");
    VERIFY_IS_FALSE(IsEqualGUID(contextId, GUID{}), L"ContextId should not be GUID_NULL after connect");

    // Send direct query in automated mode
    nlohmann::json request;
    request["requestType"] = 2; // AIMX_DIRECT_QUERY
    request["command"] = "Get-ADDefaultDomainPasswordPolicy";
    request["executionMode"] = 1; // AIMX_MODE_AUTOMATED

    std::string requestStr = request.dump();
    std::wstring requestWStr(requestStr.begin(), requestStr.end());

    LPWSTR response = nullptr;
    hr = rpcClient->AimxProcessPrompt(contextId, requestWStr.c_str(), &response);
    VERIFY_SUCCEEDED(hr, L"AimxProcessPrompt should succeed for direct query");
    VERIFY_IS_NOT_NULL(response, L"Response should not be null");

    // Parse response
    std::wstring responseWStr(response);
    std::string responseStr(responseWStr.begin(), responseWStr.end());
    nlohmann::json responseJson = nlohmann::json::parse(responseStr);

    Log::Comment(L"Direct Query Automated Response:");
    Log::Comment(String().Format(L"%ws", response));

    VERIFY_IS_TRUE(responseJson["success"].get<bool>(), L"Response should indicate success");
    VERIFY_IS_TRUE(responseJson.contains("operationId"), L"Response should contain operationId");
    if (0 != strcmp("EXECUTING", responseJson["status"].get<std::string>().c_str()))
    {
        VERIFY_FAIL(L"Automated mode should start with EXECUTING status");
    }
    std::string operationId = responseJson["operationId"].get<std::string>();
    if (response) MIDL_user_free(response);

    // Poll for completion
    bool executionComplete = false;
    int pollCount = 0;
    const int maxPolls = 45; // 45 seconds timeout

    while (!executionComplete && pollCount < maxPolls)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        
        nlohmann::json statusRequest;
        statusRequest["requestType"] = 3; // AIMX_PLAN_STATUS
        statusRequest["operationId"] = operationId;

        std::string statusRequestStr = statusRequest.dump();
        std::wstring statusRequestWStr(statusRequestStr.begin(), statusRequestStr.end());

        response = nullptr;
        hr = rpcClient->AimxProcessPrompt(contextId, statusRequestWStr.c_str(), &response);
        VERIFY_SUCCEEDED(hr, L"Status query should succeed");

        if (response)
        {
            std::wstring statusResponseWStr(response);
            std::string statusResponseStr(statusResponseWStr.begin(), statusResponseWStr.end());
            nlohmann::json statusResponseJson = nlohmann::json::parse(statusResponseStr);

            std::string currentStatus = statusResponseJson["status"].get<std::string>();
            Log::Comment(String().Format(L"Direct Query Poll #%d Status: %hs", pollCount + 1, currentStatus.c_str()));

            if (0 == strcmp("EXECUTING", currentStatus.c_str()))
            {
                Log::Comment(L"Direct query is still executing...");
            }
            else if (0 == strcmp("PLANNING", currentStatus.c_str()))
            {
                Log::Comment(L"Direct query is still planning...");
            }
            else if (0 == strcmp("COMPLETED", currentStatus.c_str()) || 0 == strcmp("FAILED", currentStatus.c_str()))
            {
                Log::Comment(String().Format(L"Direct Query completed with status: %hs", currentStatus.c_str()));
                executionComplete = true;

                if (0 == strcmp("COMPLETED", currentStatus.c_str()) && (statusResponseJson.contains("result")))
                {
                    Log::Comment(L"Direct Query Result:");
                    Log::Comment(String().Format(L"%hs", statusResponseJson["result"].get<std::string>().c_str()));
                }
            }

            MIDL_user_free(response);
        }
        pollCount++;
    }

    VERIFY_IS_TRUE(executionComplete, L"Direct query execution should complete within timeout");

    // Cleanup
    hr = rpcClient->AimxClose(contextId);
    VERIFY_SUCCEEDED(hr, L"AimxClose should succeed");
    delete rpcClient;
    Log::Comment(L"TestDirectQueryAutomatedMode completed successfully");
}

void AimxProtocolTests::TestDirectQueryInteractiveMode()
{
    GUID contextId = {};
    HRESULT hr = S_OK;
    AimxRpcClient* rpcClient = new AimxRpcClient();
    
    Log::Comment(L"Starting TestDirectQueryInteractiveMode");
    
    VERIFY_IS_NOT_NULL(rpcClient, L"Failed to create AimxRpcClient instance");

    // Connect to server
    hr = rpcClient->AimxConnect(&contextId);
    VERIFY_SUCCEEDED(hr, L"AimxConnect should succeed");

    // Send direct query in interactive mode
    nlohmann::json request;
    request["requestType"] = 2; // AIMX_DIRECT_QUERY
    request["command"] = "Get-ADUser -Filter *";
    request["executionMode"] = 2; // AIMX_MODE_INTERACTIVE

    std::string requestStr = request.dump();
    std::wstring requestWStr(requestStr.begin(), requestStr.end());

    LPWSTR response = nullptr;
    hr = rpcClient->AimxProcessPrompt(contextId, requestWStr.c_str(), &response);
    VERIFY_SUCCEEDED(hr, L"AimxProcessPrompt should succeed for interactive direct query");

    // Parse response
    std::wstring responseWStr(response);
    std::string responseStr(responseWStr.begin(), responseWStr.end());
    nlohmann::json responseJson = nlohmann::json::parse(responseStr);

    Log::Comment(L"Direct Query Interactive Response:");
    Log::Comment(String().Format(L"%ws", response));

    VERIFY_IS_TRUE(responseJson["success"].get<bool>(), L"Response should indicate success");
    if (0 != strcmp("PLANNING", responseJson["status"].get<std::string>().c_str()))
    {
        VERIFY_FAIL(L"Interactive mode should start with planning");
    }

    std::string operationId = responseJson["operationId"].get<std::string>();
    if (response) MIDL_user_free(response);

    // Wait for plan to be ready, then execute it
    bool planReady = false;
    int pollCount = 0;

    while (!planReady && pollCount < 20)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        
        nlohmann::json statusRequest;
        statusRequest["requestType"] = 3; // AIMX_PLAN_STATUS
        statusRequest["operationId"] = operationId;

        std::string statusRequestStr = statusRequest.dump();
        std::wstring statusRequestWStr(statusRequestStr.begin(), statusRequestStr.end());

        response = nullptr;
        hr = rpcClient->AimxProcessPrompt(contextId, statusRequestWStr.c_str(), &response);
        VERIFY_SUCCEEDED(hr, L"Plan status query should succeed");

        if (response)
        {
            std::wstring statusResponseWStr(response);
            std::string statusResponseStr(statusResponseWStr.begin(), statusResponseWStr.end());
            nlohmann::json statusResponseJson = nlohmann::json::parse(statusResponseStr);

            if (0 == strcmp("PLAN_READY", statusResponseJson["status"].get<std::string>().c_str()))
            {
                planReady = true;
                Log::Comment(L"Plan is ready for interactive direct query");
            }

            MIDL_user_free(response);
        }
        pollCount++;
    }

    VERIFY_IS_TRUE(planReady, L"Plan should be ready for interactive mode");

    // Cleanup
    hr = rpcClient->AimxClose(contextId);
    VERIFY_SUCCEEDED(hr, L"AimxClose should succeed");
    delete rpcClient;
    Log::Comment(L"TestDirectQueryInteractiveMode completed successfully");
}

void AimxProtocolTests::TestOperationCancellation()
{
    GUID contextId = {};
    HRESULT hr = S_OK;
    AimxRpcClient* rpcClient = new AimxRpcClient();
    
    Log::Comment(L"Starting TestOperationCancellation");
    
    VERIFY_IS_NOT_NULL(rpcClient, L"Failed to create AimxRpcClient instance");

    // Connect to server
    hr = rpcClient->AimxConnect(&contextId);
    VERIFY_SUCCEEDED(hr, L"AimxConnect should succeed");

    // Start a chatbot query
    nlohmann::json request;
    request["requestType"] = 1; // AIMX_CHATBOT_QUERY
    request["query"] = "Get me the AD Password Policy";
    request["executionMode"] = 2; // AIMX_MODE_INTERACTIVE

    std::string requestStr = request.dump();
    std::wstring requestWStr(requestStr.begin(), requestStr.end());

    LPWSTR response = nullptr;
    hr = rpcClient->AimxProcessPrompt(contextId, requestWStr.c_str(), &response);
    VERIFY_SUCCEEDED(hr, L"AimxProcessPrompt should succeed");

    // Parse response to get operation ID
    std::wstring responseWStr(response);
    std::string responseStr(responseWStr.begin(), responseWStr.end());
    nlohmann::json responseJson = nlohmann::json::parse(responseStr);

    std::string operationId = responseJson["operationId"].get<std::string>();
    if (response) MIDL_user_free(response);

    // Wait a moment to let planning start
    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    // Cancel the operation
    nlohmann::json cancelRequest;
    cancelRequest["requestType"] = 5; // AIMX_CANCEL_OPERATION
    cancelRequest["operationId"] = operationId;

    std::string cancelRequestStr = cancelRequest.dump();
    std::wstring cancelRequestWStr(cancelRequestStr.begin(), cancelRequestStr.end());

    response = nullptr;
    hr = rpcClient->AimxProcessPrompt(contextId, cancelRequestWStr.c_str(), &response);
    VERIFY_SUCCEEDED(hr, L"Cancel operation should succeed");

    // Parse cancel response
    std::wstring cancelResponseWStr(response);
    std::string cancelResponseStr(cancelResponseWStr.begin(), cancelResponseWStr.end());
    nlohmann::json cancelResponseJson = nlohmann::json::parse(cancelResponseStr);

    Log::Comment(L"Cancel Operation Response:");
    Log::Comment(String().Format(L"%ws", response));

    VERIFY_IS_TRUE(cancelResponseJson["success"].get<bool>(), L"Cancel response should indicate success");
    if (response) MIDL_user_free(response);

    // Verify operation is cancelled
    nlohmann::json statusRequest;
    statusRequest["requestType"] = 3; // AIMX_PLAN_STATUS
    statusRequest["operationId"] = operationId;

    std::string statusRequestStr = statusRequest.dump();
    std::wstring statusRequestWStr(statusRequestStr.begin(), statusRequestStr.end());

    response = nullptr;
    hr = rpcClient->AimxProcessPrompt(contextId, statusRequestWStr.c_str(), &response);
    VERIFY_SUCCEEDED(hr, L"Status query after cancellation should succeed");

    std::wstring finalStatusWStr(response);
    std::string finalStatusStr(finalStatusWStr.begin(), finalStatusWStr.end());
    nlohmann::json finalStatusJson = nlohmann::json::parse(finalStatusStr);

    Log::Comment(String().Format(L"Final status after cancellation: %hs", 
        finalStatusJson["status"].get<std::string>().c_str()));

    if (0 != strcmp("CANCELLED", finalStatusJson["status"].get<std::string>().c_str()))
    {
        VERIFY_FAIL(L"Expected status after cancellation to be CANCELLED");
    }

    if (response) MIDL_user_free(response);

    // Cleanup
    hr = rpcClient->AimxClose(contextId);
    VERIFY_SUCCEEDED(hr, L"AimxClose should succeed");
    delete rpcClient;
    Log::Comment(L"TestOperationCancellation completed successfully");
}

void AimxProtocolTests::TestErrorHandling()
{
    GUID contextId = {};
    HRESULT hr = S_OK;
    AimxRpcClient* rpcClient = new AimxRpcClient();
    
    Log::Comment(L"Starting TestErrorHandling");
    
    VERIFY_IS_NOT_NULL(rpcClient, L"Failed to create AimxRpcClient instance");

    // Connect to server
    hr = rpcClient->AimxConnect(&contextId);
    VERIFY_SUCCEEDED(hr, L"AimxConnect should succeed");

    // Test 1: Invalid JSON
    const wchar_t* invalidJson = L"{ invalid json }";
    LPWSTR response = nullptr;
    hr = rpcClient->AimxProcessPrompt(contextId, invalidJson, &response);
    VERIFY_FAILED(hr, L"Invalid JSON should fail");

    // Test 2: Missing required fields
    nlohmann::json incompleteRequest;
    incompleteRequest["requestType"] = 1;
    // Missing query and executionMode

    std::string incompleteStr = incompleteRequest.dump();
    std::wstring incompleteWStr(incompleteStr.begin(), incompleteStr.end());

    response = nullptr;
    hr = rpcClient->AimxProcessPrompt(contextId, incompleteWStr.c_str(), &response);
    
    if (SUCCEEDED(hr) && response)
    {
        // If the call succeeds, check that the response indicates an error
        std::wstring responseWStr(response);
        std::string responseStr(responseWStr.begin(), responseWStr.end());
        nlohmann::json responseJson = nlohmann::json::parse(responseStr);
        
        VERIFY_IS_FALSE(responseJson["success"].get<bool>(), L"Incomplete request should return error response");
        Log::Comment(String().Format(L"Error response: %hs", responseJson["errorMessage"].get<std::string>().c_str()));
        
        MIDL_user_free(response);
    }

    // Test 3: Invalid operation ID for status query
    nlohmann::json invalidIdRequest;
    invalidIdRequest["requestType"] = 3; // AIMX_PLAN_STATUS
    invalidIdRequest["operationId"] = "invalid-guid-format";

    std::string invalidIdStr = invalidIdRequest.dump();
    std::wstring invalidIdWStr(invalidIdStr.begin(), invalidIdStr.end());

    response = nullptr;
    hr = rpcClient->AimxProcessPrompt(contextId, invalidIdWStr.c_str(), &response);
    
    if (SUCCEEDED(hr) && response)
    {
        std::wstring responseWStr(response);
        std::string responseStr(responseWStr.begin(), responseWStr.end());
        nlohmann::json responseJson = nlohmann::json::parse(responseStr);
        
        VERIFY_IS_FALSE(responseJson["success"].get<bool>(), L"Invalid operation ID should return error response");
        MIDL_user_free(response);
    }

    // Cleanup
    hr = rpcClient->AimxClose(contextId);
    VERIFY_SUCCEEDED(hr, L"AimxClose should succeed");
    delete rpcClient;
    Log::Comment(L"TestErrorHandling completed successfully");
}

void AimxProtocolTests::TestMultipleOperations()
{
GUID contextId = {};
HRESULT hr = S_OK;
AimxRpcClient* rpcClient = new AimxRpcClient();

Log::Comment(L"Starting TestMultipleOperations");

VERIFY_IS_NOT_NULL(rpcClient, L"Failed to create AimxRpcClient instance");

// Connect to server
hr = rpcClient->AimxConnect(&contextId);
VERIFY_SUCCEEDED(hr, L"AimxConnect should succeed");

std::vector<std::string> operationIds;

// Start multiple operations
for (int i = 0; i < 3; i++)
{
    nlohmann::json request;
    request["requestType"] = 2; // AIMX_DIRECT_QUERY
    request["command"] = "Get-ADUser -Identity user" + std::to_string(i + 1);
    request["executionMode"] = 1; // AIMX_MODE_AUTOMATED

    std::string requestStr = request.dump();
    std::wstring requestWStr(requestStr.begin(), requestStr.end());

    LPWSTR response = nullptr;
    hr = rpcClient->AimxProcessPrompt(contextId, requestWStr.c_str(), &response);
    VERIFY_SUCCEEDED(hr, L"Multiple operation requests should succeed");

    if (response)
    {
        std::wstring responseWStr(response);
        std::string responseStr(responseWStr.begin(), responseWStr.end());
        nlohmann::json responseJson = nlohmann::json::parse(responseStr);

        if (responseJson["success"].get<bool>())
        {
            operationIds.push_back(responseJson["operationId"].get<std::string>());
            Log::Comment(String().Format(L"Started operation %d: %hs", 
                i + 1, responseJson["operationId"].get<std::string>().c_str()));
        }

        MIDL_user_free(response);
    }
}

VERIFY_ARE_EQUAL(3, static_cast<int>(operationIds.size()), L"Should have started 3 operations");

// Check status of all operations
for (size_t i = 0; i < operationIds.size(); i++)
{
    nlohmann::json statusRequest;
    statusRequest["requestType"] = 3; // AIMX_PLAN_STATUS
    statusRequest["operationId"] = operationIds[i];

    std::string statusRequestStr = statusRequest.dump();
    std::wstring statusRequestWStr(statusRequestStr.begin(), statusRequestStr.end());

    LPWSTR response = nullptr;
    hr = rpcClient->AimxProcessPrompt(contextId, statusRequestWStr.c_str(), &response);
    VERIFY_SUCCEEDED(hr, L"Status queries should succeed");

    if (response)
    {
        std::wstring responseWStr(response);
        std::string responseStr(responseWStr.begin(), responseWStr.end());
        nlohmann::json responseJson = nlohmann::json::parse(responseStr);

        Log::Comment(String().Format(L"Operation %d status: %hs", 
            static_cast<int>(i) + 1, responseJson["status"].get<std::string>().c_str()));

        MIDL_user_free(response);
    }
}

// Cleanup
hr = rpcClient->AimxClose(contextId);
VERIFY_SUCCEEDED(hr, L"AimxClose should succeed");
delete rpcClient;
Log::Comment(L"TestMultipleOperations completed successfully");
}