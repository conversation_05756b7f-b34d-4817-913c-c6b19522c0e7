/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    WebViewMessageHandlerADHealth.cpp

Abstract:

    This module implements Active Directory dashboard functionality 
    for the WebView message handler. Provides health status information and
    performance metrics for domain controllers and member servers.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 03/29/2025

--*/

#include "pch.h"
#include "WebViewMessageHandler.h"
#include <sstream>
#include <string>
#include <algorithm>
#include <ctime>
#include <iomanip>
#include "Debug.h"
#include <random>
#include <chrono>

// Random data generation for AD health dashboard
namespace {
    // Random number generator
    std::random_device rd;
    std::mt19937 gen(rd());

    // Generate random integer between min and max (inclusive)
    INT
    RandomInt(
        _In_ INT nMin,
        _In_ INT nMax
        )
    {
        std::uniform_int_distribution<> dis(nMin, nMax);
        return dis(gen);
    }

    // Generate random float between min and max
    FLOAT
    RandomFloat(
        _In_ FLOAT fMin,
        _In_ FLOAT fMax
        )
    {
        std::uniform_real_distribution<> dis(fMin, fMax);
        return static_cast<FLOAT>(dis(gen));
    }

    // Generate time labels for the specified hours (returns array of HH:00 format strings)
    std::vector<std::string>
    GenerateTimeLabels(
        _In_ INT nHours
        )
    {
        std::vector<std::string> labels;
        auto now = std::chrono::system_clock::now();
        
        for (INT i = nHours - 1; i >= 0; i--)
        {
            auto timePoint = now - std::chrono::hours(i);
            auto time_value = std::chrono::system_clock::to_time_t(timePoint);
            
            std::tm tmStruct;
            localtime_s(&tmStruct, reinterpret_cast<const time_t*>(&time_value));
            
            CHAR szBuffer[6];
            sprintf_s(szBuffer, "%d:00", tmStruct.tm_hour);
            labels.push_back(szBuffer);
        }
        
        return labels;
    }

    // Generate vector of random integers
    std::vector<INT>
    GenerateRandomData(
        _In_ INT nCount,
        _In_ INT nMin,
        _In_ INT nMax
        )
    {
        std::vector<INT> result;
        result.reserve(nCount);
        
        for (INT i = 0; i < nCount; i++)
        {
            result.push_back(RandomInt(nMin, nMax));
        }
        
        return result;
    }

    // Generate AD dashboard data based on timeframe
    nlohmann::json
    GenerateDashboardData(
        _In_ const std::string& strTimeframe
        )
    {
        nlohmann::json data;
        
        // Overview stats
        data["overview"] = {
            {"dcCount", 4},
            {"serverCount", RandomInt(30, 35)},
            {"userCount", std::to_string(RandomInt(1200, 1300))},
            {"health", "Good"}
        };
        
        // Server status data
        data["serverStatus"] = {
            {"data", nlohmann::json::array({RandomInt(22, 28), RandomInt(1, 5), RandomInt(0, 2), RandomInt(1, 3)})},
            {"labels", nlohmann::json::array({"Online", "Warning", "Offline", "Maintenance"})}
        };
        
        // Authentication activity
        INT nHours = 24;
        if (strTimeframe == "week") nHours = 24*7;
        else if (strTimeframe == "month") nHours = 24*30;
        
        std::vector<std::string> timeLabels = GenerateTimeLabels(nHours);
        std::vector<INT> successData = GenerateRandomData(nHours, 80, 120);
        std::vector<INT> failedData = GenerateRandomData(nHours, 0, 20);
        
        data["authActivity"] = {
            {"labels", timeLabels},
            {"success", successData},
            {"failed", failedData}
        };
        
        // Replication health
        INT nHealthy = RandomInt(85, 98);
        data["replication"] = {
            {"healthy", nHealthy},
            {"issues", 100 - nHealthy}
        };
        
        // Resources utilization
        std::vector<std::string> servers = {"DC1", "DC2", "DC3", "DC4", "FS1", "FS2"};
        
        data["resources"] = {
            {"servers", servers},
            {"cpu", GenerateRandomData(servers.size(), 10, 80)},
            {"memory", GenerateRandomData(servers.size(), 20, 90)}
        };
        
        // Security incidents
        data["security"] = {
            {"categories", nlohmann::json::array({"Failed Logins", "Account Lockouts", "Privilege Changes", "Others"})},
            {"counts", nlohmann::json::array({RandomInt(40, 50), RandomInt(10, 20), RandomInt(5, 15), RandomInt(25, 35)})}
        };
        
        // Geographic map data (simplified for demo)
        nlohmann::json points = nlohmann::json::array();
        for (INT i = 0; i < 8; i++)
        {
            points.push_back({
                {"x", RandomInt(10, 90)},
                {"y", RandomInt(10, 45)},
                {"r", RandomInt(5, 15)}
            });
        }
        
        data["geoMap"] = {
            {"points", points}
        };
        
        // Generate alerts
        nlohmann::json alerts = nlohmann::json::array();
        
        // Current time to generate timestamps
        auto now = std::chrono::system_clock::now();
        auto now_time_value = std::chrono::system_clock::to_time_t(now);
        
        // Critical alert
        std::tm tmStruct;
        localtime_s(&tmStruct, reinterpret_cast<const time_t*>(&now_time_value));
        CHAR szTimestamp1[100];
        strftime(szTimestamp1, sizeof(szTimestamp1), "%m/%d/%Y %H:%M:%S", &tmStruct);
        
        alerts.push_back({
            {"severity", "critical"},
            {"title", "Replication Failure"},
            {"message", "Replication between DC1 and DC2 has failed. Check network connectivity."},
            {"timestamp", szTimestamp1}
        });
        
        // Warning alert (1 hour ago)
        auto warning_time = now - std::chrono::hours(1);
        auto warning_time_value = std::chrono::system_clock::to_time_t(warning_time);
        localtime_s(&tmStruct, reinterpret_cast<const time_t*>(&warning_time_value));
        CHAR szTimestamp2[100];
        strftime(szTimestamp2, sizeof(szTimestamp2), "%m/%d/%Y %H:%M:%S", &tmStruct);
        
        alerts.push_back({
            {"severity", "warning"},
            {"title", "High CPU Usage"},
            {"message", "Server FS1 is experiencing high CPU usage (" + std::to_string(RandomInt(85, 95)) + "%)."},
            {"timestamp", szTimestamp2}
        });
        
        // Info alert (2 hours ago)
        auto info_time = now - std::chrono::hours(2);
        auto info_time_value = std::chrono::system_clock::to_time_t(info_time);
        localtime_s(&tmStruct, reinterpret_cast<const time_t*>(&info_time_value));
        CHAR szTimestamp3[100];
        strftime(szTimestamp3, sizeof(szTimestamp3), "%m/%d/%Y %H:%M:%S", &tmStruct);
        
        alerts.push_back({
            {"severity", "info"},
            {"title", "Maintenance Complete"},
            {"message", "Scheduled maintenance on DC3 completed successfully."},
            {"timestamp", szTimestamp3}
        });
        
        data["alerts"] = alerts;
        
        // Issues
        nlohmann::json issues = nlohmann::json::array();
        issues.push_back({
            {"title", "FSMO Role Holder Offline"},
            {"description", "The PDC Emulator role holder (DC2) is currently offline."},
            {"action", "Transfer FSMO roles to an available domain controller."}
        });
        
        issues.push_back({
            {"title", "DNS Service Stopped"},
            {"description", "The DNS Server service on DC1 is not running."},
            {"action", "Start the DNS Server service on DC1."}
        });
        
        return data;
    }
}

/*++

Routine Description:

    Handles messages for AD Dashboard sent from the WebView.

Arguments:

    message - The JSON message containing the action and parameters.

Return Value:

    None.

--*/
VOID
WebViewMessageHandler::HandleADDashboardMessage(
    _In_ const nlohmann::json& message
    )
{
    // Verify message contains the action field and it's a string
    if (!message.contains("action") || !message["action"].is_string())
    {
        LOGERROR("AD dashboard message missing action field or not a string");
        return;
    }

    // Extract the action as a string
    std::string strAction = message["action"];
    LOGVERBOSE("Processing AD dashboard action: " + strAction);

    // Process based on action type
    if (strAction == "getData")
    {
        LOGVERBOSE("Received request for AD dashboard data");
        
        std::string strTimeframe = "day";  // Default timeframe
        
        // Get timeframe if provided
        if (message.contains("timeframe") && message["timeframe"].is_string())
        {
            strTimeframe = message["timeframe"];
        }
        
        try
        {
            // Generate data based on timeframe
            nlohmann::json dashboardData = GenerateDashboardData(strTimeframe);
            
            // Send back to WebView
            nlohmann::json response = {
                {"type", "adDashboard"},
                {"action", "updateData"},
                {"data", dashboardData}
            };
            
            SendMessageToWebView(Utf8ToWide(response.dump()));
        }
        catch (const std::exception& e)
        {
            LOGERROR("Exception generating AD dashboard data: " + std::string(e.what()));
            
            // Send error response to WebView
            nlohmann::json errorResponse = {
                {"type", "adDashboard"},
                {"action", "error"},
                {"message", "Failed to generate dashboard data: " + std::string(e.what())}
            };
            
            SendMessageToWebView(Utf8ToWide(errorResponse.dump()), true);
        }
    }
    else if (strAction == "generateReport")
    {
        LOGVERBOSE("Received request to generate AD dashboard report");
        
        // In a real implementation, this would generate a report file
        // For now, we'll just simulate success
        
        // Simulate a report path
        std::string strReportPath = "C:\\Reports\\ADHealthReport_" + 
            std::to_string(std::chrono::system_clock::to_time_t(std::chrono::system_clock::now())) + 
            ".pdf";
        
        // Send response
        nlohmann::json response = {
            {"type", "adDashboard"},
            {"action", "reportStatus"},
            {"status", "success"},
            {"reportPath", strReportPath}
        };
        
        SendMessageToWebView(Utf8ToWide(response.dump()));
    }
    else if (strAction == "drillDown")
    {
        LOGVERBOSE("Received drill-down request for AD dashboard");
        
        // Extract drill-down parameters
        std::string strCategory = message.contains("category") && message["category"].is_string() ? 
            message["category"] : "";
        std::string strFilter = message.contains("filter") && message["filter"].is_string() ? 
            message["filter"] : "";
        
        // In a real implementation, this would fetch detailed data for the specified category and filter
        // For now, we just log it
        LOGVERBOSE("Drill-down request: category=" + strCategory + ", filter=" + strFilter);
        
        // No response needed for now, but in a real implementation would return detailed data
    }
    else
    {
        LOGINFO("Unknown AD dashboard action: " + strAction);
        
        // Send error response for unknown action
        nlohmann::json errorResponse = {
            {"type", "adDashboard"},
            {"action", "error"},
            {"message", "Unknown action: " + strAction}
        };
        
        SendMessageToWebView(Utf8ToWide(errorResponse.dump()));
    }
}
