<#
.SYNOPSIS
    Active Directory Object Management Tools for MCP Server
    
.DESCRIPTION
    This module registers MCP tools for Active Directory general object management operations.
    Each tool is a direct wrapper around the corresponding AD PowerShell cmdlet with
    exact parameter passthrough and no output formatting.

.AUTHOR
    Rupo Zhang (rizhang)
#>

# Import required modules
Import-Module ActiveDirectory -ErrorAction SilentlyContinue

function Register-ObjectManagementTools {
    [CmdletBinding()]
    param()

    # Get-ADObject - Gets one or more Active Directory objects
    Register-McpTool -Name "Get-ADObject" -Description "Gets one or more Active Directory objects. Supports filtering by various criteria and retrieving specific properties." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Filter) { $params.Filter = $Arguments.Filter }
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.LDAPFilter) { $params.LDAPFilter = $Arguments.LDAPFilter }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Properties) { $params.Properties = $Arguments.Properties }
        if ($Arguments.ResultPageSize) { $params.ResultPageSize = $Arguments.ResultPageSize }
        if ($Arguments.ResultSetSize) { $params.ResultSetSize = $Arguments.ResultSetSize }
        if ($Arguments.SearchBase) { $params.SearchBase = $Arguments.SearchBase }
        if ($Arguments.SearchScope) { $params.SearchScope = $Arguments.SearchScope }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.IncludeDeletedObjects) { $params.IncludeDeletedObjects = $Arguments.IncludeDeletedObjects }
        
        Get-ADObject @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Filter = @{ type = "string"; description = "PowerShell Expression Language filter string" }
            Identity = @{ type = "string"; description = "Object identity (DN, GUID, or distinguished name)" }
            LDAPFilter = @{ type = "string"; description = "LDAP query string for filtering" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Properties = @{ type = "array"; items = @{ type = "string" }; description = "Additional properties to retrieve" }
            ResultPageSize = @{ type = "integer"; description = "Number of objects per page" }
            ResultSetSize = @{ type = "integer"; description = "Maximum number of objects to return" }
            SearchBase = @{ type = "string"; description = "Active Directory path to search under" }
            SearchScope = @{ type = "string"; enum = @("Base", "OneLevel", "Subtree"); description = "Scope of the search" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            IncludeDeletedObjects = @{ type = "boolean"; description = "Include deleted objects in results" }
        }
    }

    # New-ADObject - Creates an Active Directory object
    Register-McpTool -Name "New-ADObject" -Description "Creates a new Active Directory object with specified properties and attributes." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Name) { $params.Name = $Arguments.Name }
        if ($Arguments.Type) { $params.Type = $Arguments.Type }
        if ($Arguments.Path) { $params.Path = $Arguments.Path }
        if ($Arguments.DisplayName) { $params.DisplayName = $Arguments.DisplayName }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.OtherAttributes) { $params.OtherAttributes = $Arguments.OtherAttributes }
        if ($Arguments.ProtectedFromAccidentalDeletion) { $params.ProtectedFromAccidentalDeletion = $Arguments.ProtectedFromAccidentalDeletion }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        New-ADObject @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Name = @{ type = "string"; description = "Name of the object (required)" }
            Type = @{ type = "string"; description = "Object class type (required)" }
            Path = @{ type = "string"; description = "Distinguished name of the parent container" }
            DisplayName = @{ type = "string"; description = "Display name for the object" }
            Description = @{ type = "string"; description = "Description of the object" }
            OtherAttributes = @{ type = "object"; description = "Additional attributes as hashtable" }
            ProtectedFromAccidentalDeletion = @{ type = "boolean"; description = "Protect from accidental deletion" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the created object" }
        }
        required = @("Name", "Type")
    }

    # Set-ADObject - Modifies an Active Directory object
    Register-McpTool -Name "Set-ADObject" -Description "Modifies properties of an existing Active Directory object." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Add) { $params.Add = $Arguments.Add }
        if ($Arguments.Clear) { $params.Clear = $Arguments.Clear }
        if ($Arguments.Remove) { $params.Remove = $Arguments.Remove }
        if ($Arguments.Replace) { $params.Replace = $Arguments.Replace }
        if ($Arguments.DisplayName) { $params.DisplayName = $Arguments.DisplayName }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.ProtectedFromAccidentalDeletion) { $params.ProtectedFromAccidentalDeletion = $Arguments.ProtectedFromAccidentalDeletion }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        
        Set-ADObject @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Object identity (DN, GUID, or distinguished name)" }
            Add = @{ type = "object"; description = "Attributes to add as hashtable" }
            Clear = @{ type = "array"; items = @{ type = "string" }; description = "Attributes to clear" }
            Remove = @{ type = "object"; description = "Attributes to remove as hashtable" }
            Replace = @{ type = "object"; description = "Attributes to replace as hashtable" }
            DisplayName = @{ type = "string"; description = "Display name for the object" }
            Description = @{ type = "string"; description = "Description of the object" }
            ProtectedFromAccidentalDeletion = @{ type = "boolean"; description = "Protect from accidental deletion" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified object" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
        }
        required = @("Identity")
    }

    # Remove-ADObject - Removes an Active Directory object
    Register-McpTool -Name "Remove-ADObject" -Description "Removes an Active Directory object from the directory." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Recursive) { $params.Recursive = $Arguments.Recursive }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.Confirm) { $params.Confirm = $Arguments.Confirm }
        if ($Arguments.WhatIf) { $params.WhatIf = $Arguments.WhatIf }
        
        Remove-ADObject @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Object identity (DN, GUID, or distinguished name)" }
            Recursive = @{ type = "boolean"; description = "Remove object and all child objects recursively" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            Confirm = @{ type = "boolean"; description = "Prompt for confirmation before removing" }
            WhatIf = @{ type = "boolean"; description = "Show what would happen without executing" }
        }
        required = @("Identity")
    }

    # Move-ADObject - Moves an Active Directory object or a container of objects to a different container or domain
    Register-McpTool -Name "Move-ADObject" -Description "Moves an Active Directory object or container of objects to a different container or domain." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.TargetPath) { $params.TargetPath = $Arguments.TargetPath }
        if ($Arguments.TargetServer) { $params.TargetServer = $Arguments.TargetServer }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Move-ADObject @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Object identity (DN, GUID, or distinguished name)" }
            TargetPath = @{ type = "string"; description = "Distinguished name of the target container" }
            TargetServer = @{ type = "string"; description = "Target domain controller for cross-domain moves" }
            Server = @{ type = "string"; description = "Source domain controller" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            PassThru = @{ type = "boolean"; description = "Return the moved object" }
        }
        required = @("Identity", "TargetPath")
    }

    # Rename-ADObject - Changes the name of an Active Directory object
    Register-McpTool -Name "Rename-ADObject" -Description "Changes the name of an Active Directory object." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.NewName) { $params.NewName = $Arguments.NewName }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Rename-ADObject @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Object identity (DN, GUID, or distinguished name)" }
            NewName = @{ type = "string"; description = "New name for the object" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            PassThru = @{ type = "boolean"; description = "Return the renamed object" }
        }
        required = @("Identity", "NewName")
    }

    # Restore-ADObject - Restores an Active Directory object
    Register-McpTool -Name "Restore-ADObject" -Description "Restores a deleted Active Directory object from the Deleted Objects container." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.NewName) { $params.NewName = $Arguments.NewName }
        if ($Arguments.TargetPath) { $params.TargetPath = $Arguments.TargetPath }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Restore-ADObject @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Deleted object identity (DN, GUID, or distinguished name)" }
            NewName = @{ type = "string"; description = "New name for the restored object" }
            TargetPath = @{ type = "string"; description = "Distinguished name of the target container" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            PassThru = @{ type = "boolean"; description = "Return the restored object" }
        }
        required = @("Identity")
    }

    # Sync-ADObject - Replicates a single object between any two domain controllers that have partitions in common
    Register-McpTool -Name "Sync-ADObject" -Description "Replicates a single Active Directory object between domain controllers that have partitions in common." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Object) { $params.Object = $Arguments.Object }
        if ($Arguments.Source) { $params.Source = $Arguments.Source }
        if ($Arguments.Destination) { $params.Destination = $Arguments.Destination }
        if ($Arguments.PasswordOnly) { $params.PasswordOnly = $Arguments.PasswordOnly }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Sync-ADObject @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Object = @{ type = "string"; description = "Object identity to replicate (DN, GUID, or distinguished name)" }
            Source = @{ type = "string"; description = "Source domain controller" }
            Destination = @{ type = "string"; description = "Destination domain controller" }
            PasswordOnly = @{ type = "boolean"; description = "Replicate password information only" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the synchronized object" }
        }
        required = @("Object", "Source", "Destination")
    }
}

# Function is available after dot-sourcing
