/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    RagDatabaseBuilder.h

Abstract:

    This module defines the RAG database builder class.
    Provides functionality to build, process, and manage RAG databases.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 04/13/2025

--*/

#pragma once

#define NOMINMAX

// Suppress specific warnings from hnswlib library
#pragma warning (disable:4100) // unref formal param
#pragma warning (disable:4127) // cond expression is constant
#pragma warning (disable:4242) // conversion from 'int' to 'hnswlib::vl_type'
#pragma warning (disable:4244) // conversion from 'int' to 'hnswlib::vl_type'
#pragma warning (disable:4245) // conversion from 'int' to 'hnswlib::vl_type'
#pragma warning (disable:4267) // conversion from 'int64_t' to 'int'
#pragma warning (disable:4505) // unref function with internal linkage removed

#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <thread>
#include <mutex>
#include "../common/nlohmann/json.hpp"
#include "RagJobManager.h"
#include "RAGDbHelper.h"
#include <filtereg.h>
#include <filterr.h>
#include <searchapi.h>
#include <wrl/client.h>
#include <shlwapi.h>


typedef Microsoft::WRL::ComPtr<IStream> IStream_t;
using json = nlohmann::json;

// Document statistics for metadata
struct DocumentStats {
    std::string filename;
    std::string path;
    std::string fileType; // txt, docx, pdf, etc.
    size_t sizeBytes;
    size_t charCount;
    double extractionTime;
};

// Metadata for the RAG database
struct RagMetadata {
    std::string modelName;
    int dimension;
    int documentCount;
    int docxCount;
    int txtCount;
    int logCount;
    int pdfCount;
    int unsupportedCount;
    int failedCount;
    int chunkCount;
    int chunkSize;
    int totalTokens;
    int indexEfConstruction;
    int indexM;
    int indexEfSearch;
    double embeddingTime;
    std::string documentsPath;
};


class RagDatabaseBuilder
{
public:
    RagDatabaseBuilder();
    ~RagDatabaseBuilder();

    // Initialization
    bool
    Initialize(
        _In_ int chunkSize,
        _In_z_ const std::string& modelName,
        _In_ bool useGpu,
        _In_z_ const std::string& outputDir
        );

    // Database building functions
    bool
    StartBuildAsync(
        _In_z_ const std::string& jobId,
        _In_z_ const std::string& folderPath,
        _In_z_ const std::string& outputDir,
        _In_ int chunkSize,
        _In_ float similarityThreshold,
        _In_z_ const std::string& modelName,
        _In_ bool useGpu,
        _In_ std::unique_ptr<RagJobManager>& jobManager
        );

    // Index testing function
    nlohmann::json
    TestIndex(
        _In_z_ const std::string& indexPath,
        _In_z_ const std::string& query,
        _In_ int k
        );

    // Status functions
    bool
    IsGpuAvailable() const;

    std::string
    GetGpuName() const;

    void
    InitializeGpuDetection();

    bool
    IsBuildInProgress() const;

    bool
    ProcessDocument(
        _In_ const std::string& filePath
    );
private:
    // Private methods
    void
    BuildDatabaseThread(
        _In_z_ const std::string& jobId,
        _In_z_ const std::string& folderPath,
        _In_z_ const std::string& outputDir,
        _In_ int chunkSize,
        _In_ float similarityThreshold,
        _In_z_ const std::string& modelName,
        _In_ bool useGpu,
        _In_ RagJobManager* jobManager
        );

    // Create a temporary directory for storing files
    std::string
    CreateTemporaryDirectory();

    // Extract text from documents in a directory
    bool
    ExtractDocumentContent(
        _In_ const std::string& directoryPath
    );

    // Create chunks from a document using basic or semantic chunking
    std::vector<chunk>
    ChunkDocument(
        _In_ const rag_entry& document,
        _In_ bool useSemanticChunking = false,
        _In_ float similarityThreshold = 0.7f
    );

    // Generate embeddings for chunks
    bool
    GenerateEmbeddings(
        _Inout_ std::vector<chunk>& chunks
    );

    // Generate BuildVectorDatabase for chunks
    bool
    BuildVectorDatabase(
        _In_ const std::vector<chunk>& allChunks
    );

    // Save database files
    bool
    SaveDatabase(
        _In_ const std::vector<chunk>& chunks,
        _In_ hnswlib::HierarchicalNSW<float>* vectorDb
    );

    // Save metadata
    bool
    SaveMetadata(
        _In_ const std::vector<DocumentStats>& documentStats
    );

    // Extract text from different document types
    std::string
    ExtractTextFromFile(
        _In_ const std::string& filePath
    );

    // Extract text for text based file eg. txt, log
    std::string
    ExtractTextBasedFile(
        _In_ const std::string& filePath
    );

    // Extract text for PDF file
    std::string
    ExtractPDFFile(
        _In_ const std::string& filePath
    );

    // Extract text for Docx file
    std::string
    ExtractDocxFile(
        _In_ const std::string& filePath
    );

    // Extract text for pdf file
    HRESULT
    ExtractPdfText(
        _In_ const IStream_t& pdfInputStream,
        _Out_ std::string &content
    );

    // Member variables
    bool m_buildInProgress;
    bool m_gpuAvailable;
    std::string m_gpuName;
    std::thread m_buildThread;
    std::mutex m_mutex;

    // Configuration
    model_params m_params;
    std::string m_outputDirectory;
    int m_chunkSize;
    bool m_initialized;

    // Data
    std::vector<rag_entry> m_documents;
    std::vector<DocumentStats> m_documentStats;
    RagMetadata m_metadata;

    // Paths for output files
    std::string m_indexPath;
    std::string m_chunksPath;
    std::string m_metadataPath;
};