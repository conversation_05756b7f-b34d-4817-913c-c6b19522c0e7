/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    McpStdioClient.cpp

Abstract:

    Implementation of the MCP Stdio Client component that handles JSON-RPC communication
    with MCP servers over stdin/stdout pipes. Provides tool discovery and execution
    capabilities following the Model Context Protocol specification.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/12/2025

--*/

#include "pch.hxx"
#include "McpStdioClient.h"
#include "StringUtils.h"
#include "AimxConstants.h"
#include "McpStdioClient.cpp.tmh"
#include "McpServerUtils.h"

#include <iostream>
#include <sstream>

McpStdioClient::McpStdioClient(const std::string& serverName)
    : m_serverName(serverName)
    , m_requestId(1)
    , m_isConnected(false)
    , m_isHealthy(false)
    , m_hChildStdInRd(nullptr)
    , m_hChildStdInWr(nullptr)
    , m_hChildStdOutRd(nullptr)
    , m_hChildStdOutWr(nullptr)
    , m_hChildProcess(nullptr)
    , m_hChildThread(nullptr)
    , m_processId(0)
{
    // Initialize time structures
    ZeroMemory(&m_lastActivity, sizeof(FILETIME));
    ZeroMemory(&m_connectionTime, sizeof(FILETIME));

    TraceInfo(AimxMcpSvrMgr, "Created MCP stdio client for server: %s", serverName.c_str());
}

McpStdioClient::~McpStdioClient()
{
    Disconnect();
    TraceInfo(AimxMcpSvrMgr, "Destroyed MCP stdio client for server: %s", m_serverName.c_str());
}


HRESULT
McpStdioClient::ConnectPersistent(
    _In_ const std::string& command,
    _In_ const std::vector<std::string>& arguments,
    _In_ const std::map<std::string, std::string>& environment,
    _In_ const std::string& workingDirectory
    )
/*++

Routine Description:
    Enhanced connection method with environment and working directory support.
    Stores connection parameters for potential reconnection scenarios.

Arguments:
    command - Executable command for the MCP server
    arguments - Command line arguments for the MCP server
    environment - Environment variables for the process
    workingDirectory - Working directory for the process

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxMcpSvrMgr, "Connecting persistently to MCP server: %s", command.c_str());

    // Store connection parameters for potential reconnection
    m_command = command;
    m_arguments = arguments;
    m_environment = environment;
    m_workingDirectory = workingDirectory;

    // Use enhanced process creation
    HRESULT hr = CreateServerProcess(command, arguments, environment, workingDirectory);
    if (FAILED(hr))
    {
        TraceErr(AimxMcpSvrMgr, "Failed to create server process: %!HRESULT!", hr);
        return hr;
    }

    m_isConnected = true;
    m_isHealthy = true;
    GetSystemTimeAsFileTime(&m_connectionTime);
    UpdateLastActivity();

    // Perform MCP initialization handshake
    hr = InitializeMcpHandshake();
    if (FAILED(hr))
    {
        TraceErr(AimxMcpSvrMgr, "Failed to complete MCP initialization handshake: %!HRESULT!", hr);
        Disconnect();
        return hr;
    }

    TraceInfo(AimxMcpSvrMgr, "Successfully connected persistently to MCP server with initialization");
    return S_OK;
}

HRESULT
McpStdioClient::ListTools(
    _Out_ std::vector<MCP_TOOL_INFO>& tools
    )
/*++

Routine Description:
    List available tools from the connected MCP server using the tools/list JSON-RPC method.
    Converts the server response to MCP_TOOL_INFO structures.

Arguments:
    tools - Vector to receive the tool information

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (!m_isConnected)
    {
        TraceErr(AimxMcpSvrMgr, "MCP client not connected");
        return E_FAIL;
    }

    tools.clear();

    // Create JSON-RPC request for tools/list using shared library
    nlohmann::json request = McpProtocol::Mcp::CreateListToolsRequest(m_requestId.fetch_add(1));

    nlohmann::json response;
    HRESULT hr = SendJsonRpcRequest(request, response);
    if (FAILED(hr))
    {
        TraceErr(AimxMcpSvrMgr, "Failed to send tools/list request: %!HRESULT!", hr);
        return hr;
    }

    // Parse the response using shared library
    if (McpProtocol::JsonRpc::IsErrorResponse(response))
    {
        auto error = McpProtocol::JsonRpc::GetError(response);
        TraceErr(AimxMcpSvrMgr, "MCP server returned error: %s", error.message.c_str());
        return E_FAIL;
    }

    if (!response.contains(AimxConstants::Protocol::AIMX_FIELD_RESULT))
    {
        TraceErr(AimxMcpSvrMgr, "MCP server response missing result field");
        return E_FAIL;
    }

    auto result = response[AimxConstants::Protocol::AIMX_FIELD_RESULT];
    if (!result.contains(AimxConstants::Protocol::AIMX_FIELD_TOOLS) || !result[AimxConstants::Protocol::AIMX_FIELD_TOOLS].is_array())
    {
        TraceWarn(AimxMcpSvrMgr, "MCP server returned no tools or invalid tools format");
        return S_OK; // Empty tools list is valid
    }

    // Convert tools to MCP_TOOL_INFO structures
    for (const auto& toolJson : result[AimxConstants::Protocol::AIMX_FIELD_TOOLS])
    {
        MCP_TOOL_INFO toolInfo;

        // Extract tool name
        if (toolJson.contains(AimxConstants::Protocol::AIMX_FIELD_NAME) && toolJson[AimxConstants::Protocol::AIMX_FIELD_NAME].is_string())
        {
            std::string toolName = toolJson[AimxConstants::Protocol::AIMX_FIELD_NAME];
            toolInfo.toolName = std::wstring(toolName.begin(), toolName.end());
        }
        else
        {
            TraceWarn(AimxMcpSvrMgr, "Tool missing name field, skipping");
            continue;
        }

        // Extract description
        if (toolJson.contains(AimxConstants::Protocol::AIMX_FIELD_DESCRIPTION) && toolJson[AimxConstants::Protocol::AIMX_FIELD_DESCRIPTION].is_string())
        {
            std::string description = toolJson[AimxConstants::Protocol::AIMX_FIELD_DESCRIPTION];
            toolInfo.description = std::wstring(description.begin(), description.end());
        }

        // Set server name
        toolInfo.serverName = std::wstring(m_serverName.begin(), m_serverName.end());

        // Store input schema
        if (toolJson.contains(AimxConstants::Protocol::AIMX_FIELD_INPUT_SCHEMA))
        {
            toolInfo.inputSchema = toolJson[AimxConstants::Protocol::AIMX_FIELD_INPUT_SCHEMA];
        }
        else
        {
            toolInfo.inputSchema = nlohmann::json::object();
        }

        // Initialize output schema (not typically provided in tools/list)
        toolInfo.outputSchema = nlohmann::json::object();

        toolInfo.isAvailable = true;
        // Note: serverGuid will be set by the caller when adding to server info

        tools.push_back(toolInfo);

        TraceInfo(AimxMcpSvrMgr, "Added tool: %ws - %ws",
                 toolInfo.toolName.c_str(), toolInfo.description.c_str());
    }

    TraceInfo(AimxMcpSvrMgr, "Successfully discovered %d tools from MCP server",
             static_cast<int>(tools.size()));

    return S_OK;
}

HRESULT
McpStdioClient::CallToolWithTimeout(
    _In_ const std::string& toolName,
    _In_ const nlohmann::json& parameters,
    _In_ DWORD timeoutMs,
    _Out_ nlohmann::json& result
    )
/*++

Routine Description:
    Execute a tool on the connected MCP server with timeout support.
    Uses enhanced JSON-RPC communication with timeout handling.

Arguments:
    toolName - Name of the tool to execute
    parameters - Tool parameters as JSON
    timeoutMs - Timeout in milliseconds
    result - Output result from tool execution

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (!m_isConnected)
    {
        TraceErr(AimxMcpSvrMgr, "MCP client not connected");
        m_isHealthy = false;
        return E_FAIL;
    }

    // Create JSON-RPC request for tools/call using shared library
    nlohmann::json request = McpProtocol::Mcp::CreateCallToolRequest(toolName, parameters, m_requestId.fetch_add(1));

    nlohmann::json response;
    HRESULT hr = SendJsonRpcRequestWithTimeout(request, response, timeoutMs);
    if (FAILED(hr))
    {
        TraceErr(AimxMcpSvrMgr, "Failed to send tools/call request with timeout: %!HRESULT!", hr);
        m_isHealthy = false;
        return hr;
    }

    // Check for JSON-RPC error using shared library
    if (McpProtocol::JsonRpc::IsErrorResponse(response))
    {
        auto error = McpProtocol::JsonRpc::GetError(response);
        TraceErr(AimxMcpSvrMgr, "MCP server returned error for tool '%s': %s", toolName.c_str(), error.message.c_str());
        result = response[AimxConstants::Protocol::AIMX_FIELD_ERROR];
        return E_FAIL;
    }

    if (!response.contains(AimxConstants::Protocol::AIMX_FIELD_RESULT))
    {
        TraceErr(AimxMcpSvrMgr, "MCP server response missing result field for tool '%s'", toolName.c_str());
        return E_FAIL;
    }

    result = response[AimxConstants::Protocol::AIMX_FIELD_RESULT];
    UpdateLastActivity();
    TraceInfo(AimxMcpSvrMgr, "Successfully executed tool '%s' with timeout", toolName.c_str());

    return S_OK;
}

void
McpStdioClient::Disconnect()
/*++

Routine Description:
    Disconnect from the MCP server and cleanup all resources including pipes and process handles.
    Terminates the server process gracefully and releases all allocated handles.

Arguments:
    None.

Return Value:
    None.

--*/
{
    m_isConnected = false;

    if (m_hChildStdInWr)
    {
        CloseHandle(m_hChildStdInWr);
        m_hChildStdInWr = nullptr;
    }
    if (m_hChildStdOutRd)
    {
        CloseHandle(m_hChildStdOutRd);
        m_hChildStdOutRd = nullptr;
    }
    if (m_hChildStdInRd)
    {
        CloseHandle(m_hChildStdInRd);
        m_hChildStdInRd = nullptr;
    }
    if (m_hChildStdOutWr)
    {
        CloseHandle(m_hChildStdOutWr);
        m_hChildStdOutWr = nullptr;
    }

    if (m_hChildProcess)
    {
        // Terminate the child process gracefully
        TerminateProcess(m_hChildProcess, 0);
        WaitForSingleObject(m_hChildProcess, 5000); // Wait up to 5 seconds
        CloseHandle(m_hChildProcess);
        m_hChildProcess = nullptr;
    }
    if (m_hChildThread)
    {
        CloseHandle(m_hChildThread);
        m_hChildThread = nullptr;
    }

    TraceInfo(AimxMcpSvrMgr, "Disconnected from MCP server");
}

std::string
McpStdioClient::BuildCommandLine(
    _In_ const std::string& command,
    _In_ const std::vector<std::string>& arguments
    )
/*++

Routine Description:
    Build a properly quoted command line string from command and arguments.
    Ensures proper escaping for Windows process creation.

Arguments:
    command - Executable command path
    arguments - Vector of command line arguments

Return Value:
    Formatted command line string.

--*/
{
    std::ostringstream commandLine;
    commandLine << "\"" << command << "\"";

    for (const auto& arg : arguments)
    {
        commandLine << " \"" << arg << "\"";
    }

    return commandLine.str();
}

HRESULT
McpStdioClient::SendJsonRpcRequest(
    _In_ const nlohmann::json& request,
    _Out_ nlohmann::json& response
    )
/*++

Routine Description:
    Send a JSON-RPC request to the MCP server and read the response.
    Handles the complete request-response cycle over stdio pipes.

Arguments:
    request - JSON-RPC request to send
    response - Output JSON-RPC response from server

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    HRESULT hr = WriteJsonRequest(request);
    if (FAILED(hr))
    {
        return hr;
    }

    return ReadJsonResponse(response);
}

HRESULT
McpStdioClient::WriteJsonRequest(
    _In_ const nlohmann::json& request
    )
/*++

Routine Description:
    Write a JSON-RPC request to the MCP server's stdin pipe.
    Formats the request as line-delimited JSON and flushes the pipe.

Arguments:
    request - JSON-RPC request to write

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (!m_hChildStdInWr)
    {
        TraceErr(AimxMcpSvrMgr, "Child stdin handle is null");
        return E_FAIL;
    }

    std::string requestStr = request.dump() + "\n";
    DWORD bytesWritten;

    TraceInfo(AimxMcpSvrMgr, "Sending JSON-RPC request: %s", requestStr.c_str());

    BOOL success = WriteFile(m_hChildStdInWr, requestStr.c_str(),
                            static_cast<DWORD>(requestStr.length()), &bytesWritten, nullptr);

    if (!success || bytesWritten != requestStr.length())
    {
        DWORD error = GetLastError();
        TraceErr(AimxMcpSvrMgr, "Failed to write to child stdin: %d", error);
        return HRESULT_FROM_WIN32(error);
    }

    // Flush the pipe to ensure the data is sent
    FlushFileBuffers(m_hChildStdInWr);

    return S_OK;
}

HRESULT
McpStdioClient::ReadJsonResponse(
    _Out_ nlohmann::json& response
    )
/*++

Routine Description:
    Read a JSON-RPC response from the MCP server's stdout pipe.
    Reads until a complete line is received and parses the JSON response.

Arguments:
    response - Output JSON-RPC response from server

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (!m_hChildStdOutRd)
    {
        TraceErr(AimxMcpSvrMgr, "Child stdout handle is null");
        return E_FAIL;
    }

    std::string responseStr;
    char buffer[4096];
    DWORD bytesRead;
    bool foundNewline = false;

    // Read until we get a complete JSON line
    while (!foundNewline)
    {
        BOOL success = ReadFile(m_hChildStdOutRd, buffer, sizeof(buffer) - 1, &bytesRead, nullptr);

        if (!success)
        {
            DWORD error = GetLastError();
            if (error == ERROR_BROKEN_PIPE)
            {
                TraceErr(AimxMcpSvrMgr, "Child process closed stdout pipe");
                return E_FAIL;
            }
            TraceErr(AimxMcpSvrMgr, "Failed to read from child stdout: %d", error);
            return HRESULT_FROM_WIN32(error);
        }

        if (bytesRead == 0)
        {
            TraceErr(AimxMcpSvrMgr, "No data read from child stdout");
            return E_FAIL;
        }

        buffer[bytesRead] = '\0';
        responseStr += buffer;

        // Check if we have a complete line (JSON-RPC responses are line-delimited)
        if (responseStr.find('\n') != std::string::npos)
        {
            foundNewline = true;
        }
    }

    // Extract the first complete line
    size_t newlinePos = responseStr.find('\n');
    if (newlinePos != std::string::npos)
    {
        responseStr = responseStr.substr(0, newlinePos);
    }

    TraceInfo(AimxMcpSvrMgr, "Received JSON-RPC response: %s", responseStr.c_str());

    // Parse the JSON response
    try
    {
        response = nlohmann::json::parse(responseStr);
    }
    catch (const nlohmann::json::exception& e)
    {
        // dump the full json string for debugging
        TraceErr(AimxMcpSvrMgr, "Failed to parse JSON response: %s. Full response: %s", e.what(), responseStr.c_str());        
        return E_FAIL;
    }

    return S_OK;
}

HRESULT
McpStdioClient::SendJsonRpcRequestWithTimeout(
    _In_ const nlohmann::json& request,
    _Out_ nlohmann::json& response,
    _In_ DWORD timeoutMs
    )
/*++

Routine Description:
    Send a JSON-RPC request with timeout support.

Arguments:
    request - JSON-RPC request to send
    response - Output JSON-RPC response from server
    timeoutMs - Timeout in milliseconds

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    HRESULT hr = WriteJsonRequest(request);
    if (FAILED(hr))
    {
        return hr;
    }

    return ReadJsonResponseWithTimeout(response, timeoutMs);
}

HRESULT
McpStdioClient::ReadJsonResponseWithTimeout(
    _Out_ nlohmann::json& response,
    _In_ DWORD timeoutMs
    )
/*++

Routine Description:
    Read a JSON-RPC response with timeout support using overlapped I/O.

Arguments:
    response - Output JSON-RPC response
    timeoutMs - Timeout in milliseconds

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (!m_hChildStdOutRd)
    {
        TraceErr(AimxMcpSvrMgr, "Child stdout handle is null");
        return E_FAIL;
    }

    std::string responseStr;
    bool foundNewline = false;
    char buffer[4096];
    DWORD bytesRead;

    DWORD startTime = GetTickCount();

    // Read until we get a complete JSON line or timeout
    while (!foundNewline)
    {
        // Check timeout
        DWORD elapsed = GetTickCount() - startTime;
        if (elapsed >= timeoutMs)
        {
            TraceErr(AimxMcpSvrMgr, "Timeout reading response after %d ms", timeoutMs);
            return HRESULT_FROM_WIN32(ERROR_TIMEOUT);
        }

        // Calculate remaining timeout
        DWORD remainingTimeout = timeoutMs - elapsed;

        // Use overlapped I/O for timeout support
        OVERLAPPED overlapped = {};
        overlapped.hEvent = CreateEvent(nullptr, TRUE, FALSE, nullptr);
        if (!overlapped.hEvent)
        {
            return HRESULT_FROM_WIN32(GetLastError());
        }

        BOOL success = ReadFile(m_hChildStdOutRd, buffer, sizeof(buffer) - 1,
                               &bytesRead, &overlapped);

        if (!success)
        {
            DWORD error = GetLastError();
            if (error == ERROR_IO_PENDING)
            {
                // Wait for completion or timeout
                DWORD waitResult = WaitForSingleObject(overlapped.hEvent, remainingTimeout);
                if (waitResult == WAIT_TIMEOUT)
                {
                    CancelIo(m_hChildStdOutRd);
                    CloseHandle(overlapped.hEvent);
                    TraceErr(AimxMcpSvrMgr, "I/O operation timed out");
                    return HRESULT_FROM_WIN32(ERROR_TIMEOUT);
                }
                else if (waitResult == WAIT_OBJECT_0)
                {
                    if (!GetOverlappedResult(m_hChildStdOutRd, &overlapped, &bytesRead, FALSE))
                    {
                        DWORD overlappedError = GetLastError();
                        CloseHandle(overlapped.hEvent);
                        if (overlappedError == ERROR_BROKEN_PIPE)
                        {
                            TraceErr(AimxMcpSvrMgr, "Child process closed stdout pipe");
                            return E_FAIL;
                        }
                        return HRESULT_FROM_WIN32(overlappedError);
                    }
                }
                else
                {
                    CloseHandle(overlapped.hEvent);
                    return HRESULT_FROM_WIN32(GetLastError());
                }
            }
            else if (error == ERROR_BROKEN_PIPE)
            {
                CloseHandle(overlapped.hEvent);
                TraceErr(AimxMcpSvrMgr, "Child process closed stdout pipe");
                return E_FAIL;
            }
            else
            {
                CloseHandle(overlapped.hEvent);
                return HRESULT_FROM_WIN32(error);
            }
        }

        CloseHandle(overlapped.hEvent);

        if (bytesRead == 0)
        {
            TraceErr(AimxMcpSvrMgr, "No data read from child stdout");
            return E_FAIL;
        }

        buffer[bytesRead] = '\0';
        responseStr += buffer;

        // Check if we have a complete line (JSON-RPC responses are line-delimited)
        if (responseStr.find('\n') != std::string::npos)
        {
            foundNewline = true;
        }
    }

    // Extract the first complete line
    size_t newlinePos = responseStr.find('\n');
    if (newlinePos != std::string::npos)
    {
        responseStr = responseStr.substr(0, newlinePos);
    }

    TraceInfo(AimxMcpSvrMgr, "Received JSON-RPC response with timeout: %s", responseStr.c_str());

    // Parse the JSON response
    try
    {
        response = nlohmann::json::parse(responseStr);
    }
    catch (const nlohmann::json::exception& e)
    {
        TraceErr(AimxMcpSvrMgr, "Failed to parse JSON response: %s", e.what());
        return E_FAIL;
    }

    return S_OK;
}

HRESULT
McpStdioClient::CreateServerProcess(
    _In_ const std::string& command,
    _In_ const std::vector<std::string>& arguments,
    _In_ const std::map<std::string, std::string>& /* environment */,
    _In_ const std::string& workingDirectory
    )
/*++

Routine Description:
    Enhanced process creation with environment and working directory support.

Arguments:
    command - Executable command for the MCP server
    arguments - Command line arguments for the MCP server
    environment - Environment variables for the process
    workingDirectory - Working directory for the process

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    // Create pipes for stdin/stdout communication
    SECURITY_ATTRIBUTES saAttr;
    saAttr.nLength = sizeof(SECURITY_ATTRIBUTES);
    saAttr.bInheritHandle = TRUE;
    saAttr.lpSecurityDescriptor = nullptr;

    // Create pipes for child process stdin
    if (!CreatePipe(&m_hChildStdInRd, &m_hChildStdInWr, &saAttr, 0))
    {
        TraceErr(AimxMcpSvrMgr, "Failed to create stdin pipe: %d", GetLastError());
        return HRESULT_FROM_WIN32(GetLastError());
    }

    // Create pipes for child process stdout
    if (!CreatePipe(&m_hChildStdOutRd, &m_hChildStdOutWr, &saAttr, 0))
    {
        TraceErr(AimxMcpSvrMgr, "Failed to create stdout pipe: %d", GetLastError());
        CloseHandle(m_hChildStdInRd);
        CloseHandle(m_hChildStdInWr);
        return HRESULT_FROM_WIN32(GetLastError());
    }

    // Ensure the read handle to the pipe for stdout is not inherited
    if (!SetHandleInformation(m_hChildStdOutRd, HANDLE_FLAG_INHERIT, 0))
    {
        TraceErr(AimxMcpSvrMgr, "Failed to set handle information for stdout: %d", GetLastError());
        Disconnect();
        return HRESULT_FROM_WIN32(GetLastError());
    }

    // Ensure the write handle to the pipe for stdin is not inherited
    if (!SetHandleInformation(m_hChildStdInWr, HANDLE_FLAG_INHERIT, 0))
    {
        TraceErr(AimxMcpSvrMgr, "Failed to set handle information for stdin: %d", GetLastError());
        Disconnect();
        return HRESULT_FROM_WIN32(GetLastError());
    }

    // Create the child process
    PROCESS_INFORMATION piProcInfo;
    STARTUPINFOA siStartInfo;
    ZeroMemory(&piProcInfo, sizeof(PROCESS_INFORMATION));
    ZeroMemory(&siStartInfo, sizeof(STARTUPINFOA));

    siStartInfo.cb = sizeof(STARTUPINFOA);
    siStartInfo.hStdError = m_hChildStdOutWr;
    siStartInfo.hStdOutput = m_hChildStdOutWr;
    siStartInfo.hStdInput = m_hChildStdInRd;
    siStartInfo.dwFlags |= STARTF_USESTDHANDLES;

    // Build command line
    std::string commandLine = BuildCommandLine(command, arguments);
    TraceInfo(AimxMcpSvrMgr, "Starting enhanced MCP server process: %s", commandLine.c_str());

    // inherited environment blocks from the parent process (aimxsrv)
    LPVOID lpEnvironment = nullptr; 
    TraceInfo(AimxMcpSvrMgr, "Using inherited environment.");

    // Set working directory
    LPCSTR lpCurrentDirectory = workingDirectory.empty() ? nullptr : workingDirectory.c_str();

    // Create the child process
    BOOL bSuccess = CreateProcessA(
        nullptr,                    // No module name (use command line)
        const_cast<char*>(commandLine.c_str()), // Command line
        nullptr,                    // Process handle not inheritable
        nullptr,                    // Thread handle not inheritable
        TRUE,                       // Set handle inheritance to TRUE
        0,                          // No creation flags
        lpEnvironment,              // Environment block
        lpCurrentDirectory,         // Working directory
        &siStartInfo,               // Pointer to STARTUPINFO structure
        &piProcInfo                 // Pointer to PROCESS_INFORMATION structure
    );

    if (!bSuccess)
    {
        TraceErr(AimxMcpSvrMgr, "Failed to create enhanced MCP server process: %d", GetLastError());
        Disconnect();
        return HRESULT_FROM_WIN32(GetLastError());
    }

    m_hChildProcess = piProcInfo.hProcess;
    m_hChildThread = piProcInfo.hThread;
    m_processId = piProcInfo.dwProcessId;

    // Close handles to the stdin and stdout pipes no longer needed by the child process
    CloseHandle(m_hChildStdOutWr);
    CloseHandle(m_hChildStdInRd);
    m_hChildStdOutWr = nullptr;
    m_hChildStdInRd = nullptr;

    TraceInfo(AimxMcpSvrMgr, "Successfully created enhanced MCP server process (PID: %d)", m_processId);
    return S_OK;
}

std::string
McpStdioClient::CreateEnvironmentBlock(
    _In_ const std::map<std::string, std::string>& environment
    )
/*++

Routine Description:
    Create an environment block from a map of environment variables.

Arguments:
    environment - Map of environment variable name-value pairs

Return Value:
    Environment block string, or empty string if no environment variables.

--*/
{
    if (environment.empty())
    {
        return std::string();
    }

    std::string envBlock;
    for (const auto& [key, value] : environment)
    {
        envBlock += key + "=" + value + '\0';
    }
    envBlock += '\0'; // Double null terminator

    return envBlock;
}

HRESULT
McpStdioClient::PerformHealthCheck()
/*++

Routine Description:
    Perform a simple health check by attempting to list tools.

Return Value:
    S_OK if healthy, error HRESULT if unhealthy.

--*/
{
    try
    {
        // Create a simple ping-like request (list tools is a good health check) using shared library
        nlohmann::json request = McpProtocol::Mcp::CreateListToolsRequest(m_requestId.fetch_add(1));

        nlohmann::json response;
        HRESULT hr = SendJsonRpcRequestWithTimeout(request, response, 5000); // 5 second timeout for health check
        if (FAILED(hr))
        {
            TraceWarn(AimxMcpSvrMgr, "Health check failed - communication error: %!HRESULT!", hr);
            return hr;
        }

        // Check for valid response using shared library
        if (McpProtocol::JsonRpc::IsErrorResponse(response))
        {
            auto error = McpProtocol::JsonRpc::GetError(response);
            TraceWarn(AimxMcpSvrMgr, "Health check failed - server returned error: %s", error.message.c_str());
            return E_FAIL;
        }

        if (!response.contains(AimxConstants::Protocol::AIMX_FIELD_RESULT))
        {
            TraceWarn(AimxMcpSvrMgr, "Health check failed - invalid response format");
            return E_FAIL;
        }

        TraceInfo(AimxMcpSvrMgr, "Health check passed for server: %s", m_serverName.c_str());
        return S_OK;
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxMcpSvrMgr, "Health check failed with exception: %s", e.what());
        return E_FAIL;
    }
}

void
McpStdioClient::UpdateLastActivity()
/*++

Routine Description:
    Update the last activity timestamp to current time.

--*/
{
    GetSystemTimeAsFileTime(&m_lastActivity);
}

HRESULT
McpStdioClient::InitializeMcpHandshake()
/*++

Routine Description:
    Perform the MCP initialization handshake after connecting to the server.
    Sends initialize request and handles the response, then sends initialized notification.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxMcpSvrMgr, "Starting MCP initialization handshake");

    // Create initialize request using shared library
    nlohmann::json initRequest = McpProtocol::Client::CreateInitializeRequest("AIMX", "1.0.0");

    nlohmann::json initResponse;
    HRESULT hr = SendJsonRpcRequestWithTimeout(initRequest, initResponse, 10000); // 10 second timeout
    if (FAILED(hr))
    {
        TraceErr(AimxMcpSvrMgr, "Failed to send initialize request: %!HRESULT!", hr);
        return hr;
    }

    // Check for error response
    if (McpProtocol::JsonRpc::IsErrorResponse(initResponse))
    {
        auto error = McpProtocol::JsonRpc::GetError(initResponse);
        TraceErr(AimxMcpSvrMgr, "MCP server returned error during initialization: %s", error.message.c_str());
        return E_FAIL;
    }

    // Validate initialize response
    if (!initResponse.contains("result"))
    {
        TraceErr(AimxMcpSvrMgr, "Invalid initialize response - missing result field");
        return E_FAIL;
    }

    const auto& result = initResponse["result"];
    if (result.contains("serverInfo"))
    {
        const auto& serverInfo = result["serverInfo"];
        std::string serverName = serverInfo.value("name", "Unknown");
        std::string serverVersion = serverInfo.value("version", "Unknown");
        TraceInfo(AimxMcpSvrMgr, "Connected to MCP server: %s v%s", serverName.c_str(), serverVersion.c_str());
    }

    // Send initialized notification to complete the handshake
    nlohmann::json initializedNotification = {
        {"jsonrpc", "2.0"},
        {"method", "notifications/initialized"}
    };

    hr = WriteJsonRequest(initializedNotification);
    if (FAILED(hr))
    {
        TraceErr(AimxMcpSvrMgr, "Failed to send initialized notification: %!HRESULT!", hr);
        return hr;
    }

    // Add a small delay to allow the server to process the initialized notification
    Sleep(100);

    TraceInfo(AimxMcpSvrMgr, "MCP initialization handshake completed successfully");
    return S_OK;
}
