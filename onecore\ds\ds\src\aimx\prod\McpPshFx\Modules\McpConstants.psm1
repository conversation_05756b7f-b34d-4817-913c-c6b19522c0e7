<#
.SYNOPSIS
    MCP Protocol Constants Module
    
.DESCRIPTION
    Centralized constants for the MCP (Model Context Protocol) implementation.
    This module defines all protocol strings, versions, and standard values
    to avoid typos and ensure consistency across the MCP server.

.AUTHOR
    Rup<PERSON> (rizhang) 07-21-2025
#>

# MCP Protocol Version
$script:MCP_PROTOCOL_VERSION = "2024-11-05"

# JSON-RPC Version
$script:JSONRPC_VERSION = "2.0"

# MCP Methods
$script:MCP_METHODS = @{
    Initialize = "initialize"
    ToolsList = "tools/list"
    ToolsCall = "tools/call"
}

# MCP Notifications
$script:MCP_NOTIFICATIONS = @{
    Initialized = "notifications/initialized"
}

# MCP Error Codes (JSON-RPC standard error codes)
$script:MCP_ERROR_CODES = @{
    ParseError = -32700
    InvalidRequest = -32600
    MethodNotFound = -32601
    InvalidParams = -32602
    InternalError = -32603
}

# MCP Error Messages
$script:MCP_ERROR_MESSAGES = @{
    ParseError = "Parse error"
    InvalidRequest = "Invalid Request"
    MethodNotFound = "Method not found"
    InvalidParams = "Invalid params"
    InternalError = "Internal error"
}

# MCP Server Information
$script:MCP_SERVER_INFO = @{
    Name = "PowerShell MCP Server"
    Version = "1.0.0"
}

# MCP Client Information (for responses)
$script:MCP_CLIENT_INFO = @{
    Name = "AIMX"
    Version = "1.0.0"
}

# MCP Capabilities
$script:MCP_CAPABILITIES = @{
    Tools = @{
        ListChanged = $true
    }
    Roots = @{
        ListChanged = $true
    }
    Sampling = @{}
}

#region Public Functions

function Get-McpProtocolVersion {
    <#
    .SYNOPSIS
        Gets the MCP protocol version
    #>
    return $script:MCP_PROTOCOL_VERSION
}

function Get-JsonRpcVersion {
    <#
    .SYNOPSIS
        Gets the JSON-RPC version
    #>
    return $script:JSONRPC_VERSION
}

function Get-McpMethod {
    <#
    .SYNOPSIS
        Gets MCP method names
    .PARAMETER Method
        The method type: Initialize, ToolsList, ToolsCall
    #>
    param(
        [ValidateSet("Initialize", "ToolsList", "ToolsCall")]
        [string]$Method
    )
    return $script:MCP_METHODS[$Method]
}

function Get-McpNotification {
    <#
    .SYNOPSIS
        Gets MCP notification names
    .PARAMETER Notification
        The notification type: Initialized
    #>
    param(
        [ValidateSet("Initialized")]
        [string]$Notification
    )
    return $script:MCP_NOTIFICATIONS[$Notification]
}

function Get-McpErrorCode {
    <#
    .SYNOPSIS
        Gets MCP error codes
    .PARAMETER ErrorType
        The error type: ParseError, InvalidRequest, MethodNotFound, InvalidParams, InternalError
    #>
    param(
        [ValidateSet("ParseError", "InvalidRequest", "MethodNotFound", "InvalidParams", "InternalError")]
        [string]$ErrorType
    )
    return $script:MCP_ERROR_CODES[$ErrorType]
}

function Get-McpErrorMessage {
    <#
    .SYNOPSIS
        Gets MCP error messages
    .PARAMETER ErrorType
        The error type: ParseError, InvalidRequest, MethodNotFound, InvalidParams, InternalError
    #>
    param(
        [ValidateSet("ParseError", "InvalidRequest", "MethodNotFound", "InvalidParams", "InternalError")]
        [string]$ErrorType
    )
    return $script:MCP_ERROR_MESSAGES[$ErrorType]
}

function Get-McpServerInfo {
    <#
    .SYNOPSIS
        Gets MCP server information
    #>
    return $script:MCP_SERVER_INFO
}

function Get-McpClientInfo {
    <#
    .SYNOPSIS
        Gets MCP client information
    #>
    return $script:MCP_CLIENT_INFO
}

function Get-McpCapabilities {
    <#
    .SYNOPSIS
        Gets MCP server capabilities
    #>
    return $script:MCP_CAPABILITIES
}

function New-McpResponse {
    <#
    .SYNOPSIS
        Creates a standard MCP response object
    .PARAMETER Id
        The request ID
    .PARAMETER Result
        The result object
    #>
    param(
        [string]$Id,
        [object]$Result
    )
    
    return @{
        jsonrpc = Get-JsonRpcVersion
        id = $Id
        result = $Result
    }
}

function New-McpErrorResponse {
    <#
    .SYNOPSIS
        Creates a standard MCP error response object
    .PARAMETER Id
        The request ID (can be null)
    .PARAMETER ErrorType
        The error type
    .PARAMETER ErrorMessage
        Custom error message (optional)
    #>
    param(
        [string]$Id,
        [ValidateSet("ParseError", "InvalidRequest", "MethodNotFound", "InvalidParams", "InternalError")]
        [string]$ErrorType,
        [string]$ErrorMessage
    )
    
    $message = if ($ErrorMessage) { $ErrorMessage } else { Get-McpErrorMessage -ErrorType $ErrorType }
    
    return @{
        jsonrpc = Get-JsonRpcVersion
        id = $Id
        error = @{
            code = Get-McpErrorCode -ErrorType $ErrorType
            message = $message
        }
    }
}

#endregion

# Export functions
Export-ModuleMember -Function @(
    'Get-McpProtocolVersion',
    'Get-JsonRpcVersion',
    'Get-McpMethod',
    'Get-McpNotification',
    'Get-McpErrorCode',
    'Get-McpErrorMessage',
    'Get-McpServerInfo',
    'Get-McpClientInfo',
    'Get-McpCapabilities',
    'New-McpResponse',
    'New-McpErrorResponse'
)
