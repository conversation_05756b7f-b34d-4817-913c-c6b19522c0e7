/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    aimxrpcserver.cpp

Abstract:
    Implementation of AIMXRPC RPC interface methods.

Author:
    <PERSON> (SNAKE FIGHTER) (lindakup) 06/09/2025

--*/
#include "pch.hxx"
#include "aimxrpcserver.h"
#include "aimxrpc_s.c"
#include "aimxrpc.h"
#include "RequestHandler.h"
#include "Planner.h"
#include "Orchestrator.h"
#include "ConversationManager.h"
#include "StringUtils.h"
#include "AimxCommon.h"
#include "AimxConstants.h"
#include "LLMInfer.h"
#include "McpSvrMgr.h"
#include <functional>
#include <ctime>

#include "aimxrpcserver.cpp.tmh"

// SDDL for the RPC server security descriptor
static const std::wstring LowBoxRpcSDDL =
    L"D:"
    L"(A;;GRGWGX;;;WD)"    // World:      Execute | Read | Write
    L"(A;;GRGWGX;;;RC)"    // Restricted: Execute | Read | Write
    L"(A;;GA;;;BA)"        // Admin:   All Access
    L"(A;;GA;;;OW)"        // Owner:   All Access
    L"(A;;GR;;;AC)"        // LowBox: Read, meaning connect
    L")";

static const std::wstring RpcProtocolSequence = L"ncalrpc";

BOOL AimxRpcServer::_fListening;
BOOL AimxRpcServer::_fAcceptingCalls;

// Global operation tracking (definition - declaration is in AimxCommon.h)
std::unordered_map<GUID, std::shared_ptr<AIMX_OPERATION>, GuidHash, GuidEqual> g_OperationMap;
std::mutex g_OperationMapMutex;

// Global map to track active context handles by GUID
static std::unordered_map<GUID, PAIMX_HANDLE, GuidHash, GuidEqual> g_ContextHandleMap;
static std::mutex g_ContextHandleMapMutex;

HRESULT
AimxRpcServer::MakeRpcSecurityDescriptor(
    _Outptr_ PSECURITY_DESCRIPTOR* ppRpcSD
    )
/*++
Routine Description:
    Creates a security descriptor for the RPC server.

Arguments:
    ppRpcSD - Pointer to the security descriptor to be created.

Return Value:
    STATUS_SUCCESS on success, or an error NTSTATUS on failure.
--*/
{
    HRESULT hr = S_OK;

    TraceInfo(AimxServer, "Entry");

    if (!ppRpcSD)
    {
        TraceErr(AimxServer, "ppRpcSD is null");
        TraceInfo(AimxServer, "Exit");
        return E_INVALIDARG;
    }

    *ppRpcSD = NULL;
    if (!ConvertStringSecurityDescriptorToSecurityDescriptor(
            LowBoxRpcSDDL.c_str(),
            SDDL_REVISION_1,
            ppRpcSD,
            NULL))
    {
        DWORD WinError = GetLastError();
        hr = HRESULT_FROM_WIN32(WinError);
        TraceErr(AimxServer, "ConvertStringSecurityDescriptorToSecurityDescriptor failed: %!WINERROR!", WinError);
    }
    TraceInfo(AimxServer, "Exit");
    return hr;
}

HRESULT
AimxRpcServer::_StartRpcServer()
/*++
Routine Description:
    Initializes the aimxrpc RPC server, sets up security, and registers the interface.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.
--*/
{
    RPC_STATUS RpcStatus;
    PSECURITY_DESCRIPTOR pRpcSD = NULL;
    HRESULT hr = S_OK;
    TraceInfo(AimxServer, "Entry");

    // Make an RPC security descriptor for the RPC server.
    RpcStatus = MakeRpcSecurityDescriptor(&pRpcSD);
    if (RpcStatus != STATUS_SUCCESS)
    {
        hr = HRESULT_FROM_WIN32(RpcStatus);
        TraceErr(AimxServer, "MakeRpcSecurityDescriptor failed: %!HRESULT!", hr);
        goto Exit;
    }

    // Register the RPC protocol sequence and endpoint.
    RpcStatus = RpcServerUseProtseqEpW(
        (RPC_WSTR)RpcProtocolSequence.c_str(),  // protocol sequence
        RPC_C_PROTSEQ_MAX_REQS_DEFAULT,         // max concurrent calls
        (RPC_WSTR)AIMXSRV_LRPC_ENDPOINT,        // endpoint name
        pRpcSD);                               // security descriptor

    if (RpcStatus != RPC_S_OK && RpcStatus != RPC_S_DUPLICATE_ENDPOINT)
    {
        hr = HRESULT_FROM_WIN32(RpcStatus);
        TraceErr(AimxServer, "RpcServerUseProtseqEpW failed: %!HRESULT!", hr);
        goto Exit;
    }

    // Register the RPC interface using RpcServerRegisterIf3
    // we dont' need a callback for security, because the SD
    // and the combination of RPC_IF_ALLOW_LOCAL_ONLY, and RPC_IF_ALLOW_SECURE_ONLY
    // will ensure that only local and secure connections are allowed.
    RpcStatus = RpcServerRegisterIf3(
        s_aimxrpc_ServerIfHandle,
        NULL,
        NULL,
        RPC_IF_AUTOLISTEN | RPC_IF_ALLOW_LOCAL_ONLY | RPC_IF_ALLOW_SECURE_ONLY,
        RPC_C_LISTEN_MAX_CALLS_DEFAULT,
        (unsigned)-1,
        NULL, // no security callback
        pRpcSD);
    if (RpcStatus != RPC_S_OK)
    {
        hr = HRESULT_FROM_WIN32(RpcStatus);
        TraceErr(AimxServer, "RpcServerRegisterIf3 failed: %!HRESULT!. RpcStatus: %d", hr, RpcStatus);
        goto Exit;
    }

      // Start the RPC server to listen for incoming requests
    RpcStatus = RpcServerListen(
        1,                              // Minimum number of concurrent calls
        RPC_C_LISTEN_MAX_CALLS_DEFAULT, // Maximum number of concurrent calls
        TRUE);                          // Don't wait for all calls to complete
    if (RPC_S_ALREADY_LISTENING == RpcStatus)
    {
        // The server is already listening for RPC calls
        RpcStatus = RPC_S_OK;
    }
    if (RpcStatus != RPC_S_OK)
    {
        hr = HRESULT_FROM_WIN32(RpcStatus);
        TraceErr(AimxServer, "RpcServerListen failed: %!HRESULT!", hr);
        goto Exit;
    }

    _fListening = true;
    _fAcceptingCalls = true;

    hr = S_OK;

Exit:
    // Free the security descriptor if it was created
    if (pRpcSD)
    {
        LocalFree(pRpcSD);
        pRpcSD = NULL;
    }
    TraceInfo(AimxServer, "Exit");
    return hr;
}

HRESULT
AimxRpcServer::_StopRpcServer()
/*++
Routine Description:
    Stops the AIMXSRV RPC server, unregisters the interface, and cleans up resources.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.
--*/
{
    RPC_STATUS RpcStatus;
    HRESULT hr = S_OK;
    TraceInfo(AimxServer, "Entry");

    _fAcceptingCalls = false;

    // Unregister the RPC interface
    RpcStatus = RpcServerUnregisterIf(
        s_aimxrpc_ServerIfHandle,
        NULL,
        TRUE);
    if (RpcStatus != RPC_S_OK)
    {
        hr = HRESULT_FROM_WIN32(RpcStatus);
        TraceErr(AimxServer, "RpcServerUnregisterIf failed: %!HRESULT!", hr);
    }

    _fListening = false;
    hr = S_OK;
    TraceInfo(AimxServer, "Exit");
    return hr;
}

HRESULT
AimxRpcServer::RpcStartListening()
/*++
Routine Description:
    Starts the AIMXSRV RPC server to listen for incoming requests.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.
--*/
{
    HRESULT hr = S_OK;
    TraceInfo(AimxServer, "Entry");

    if (_fListening)
    {
        TraceInfo(AimxServer, "Already listening");
        TraceInfo(AimxServer, "Exit");
        return hr;
    }

    hr = _StartRpcServer();

    TraceInfo(AimxServer, "Exit");
    return hr;
}

HRESULT
AimxRpcServer::RpcStopListening()
/*++
Routine Description:
    Stops the AIMXSRV RPC server from listening for incoming requests.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.
--*/
{
    HRESULT hr = S_OK;
    TraceInfo(AimxServer, "Entry");

    if (!_fListening)
    {
        TraceInfo(AimxServer, "Not currently listening");
        TraceInfo(AimxServer, "Exit");
        return hr;
    }

    hr = _StopRpcServer();

    TraceInfo(AimxServer, "Exit");
    return hr;
}

void AimxRpcServer::RpcAcceptCalls()
/*++
Routine Description:
    Accepts incoming RPC calls and processes them.

Arguments:
    None.

Return Value:
    None.
--*/
{
    TraceInfo(AimxServer, "Entry");
    _fAcceptingCalls = true;
    TraceInfo(AimxServer, "Exit");
}

BOOL
AimxRpcServer::IsAcceptingCalls()
/*++
Routine Description:
    Checks if the AIMXSRV RPC server is currently accepting calls.

Arguments:
    None.

Return Value:
    True if accepting calls, false otherwise.
--*/
{
    TraceInfo(AimxServer, "Entry");
    return _fAcceptingCalls;
}


bool
IsContextHandleOwnedByCaller(
    _In_ PAIMX_HANDLE pContextHandle
    )
/*++
Routine Description:
    Validates that the given PAIMX_HANDLE belongs to the same SID as the current RPC caller.
    Compares the stored owner SID in the context handle with the SID of the current RPC caller.

Arguments:
    pContextHandle - The context handle to validate.

Return Value:
    true if the SIDs match, false otherwise. If either SID is null or retrieval fails, returns false.
--*/
{

    PSID pCallerSid = nullptr;
    HRESULT hr = AimxpGetRpcClientSid(&pCallerSid);
    if (FAILED(hr) || !pCallerSid)
    {
        TraceErr(AimxServer, "Failed to get caller SID: %!HRESULT!", hr);
        return false;
    }

    BOOL bEqual = EqualSid(pContextHandle->pOwnerSid, pCallerSid);
    MIDL_user_free(pCallerSid);
    
    if (!bEqual)
    {
        // only trace the non-matached ones, or it is too chatty.
        TraceInfo(AimxServer, "IsContextHandleOwnedByCaller: Exit (%s)", bEqual ? "true" : "false");
    }

    return bEqual;
}

HRESULT
AimxpGetRpcClientSid(
    _Outptr_ PSID* ppClientSid
)
/*++
Routine Description:
    Retrieves the security identifier (SID) of the RPC client making the call.

Arguments:
    ppClientSid - Pointer to receive the SID of the client.

Return Value:
    S_OK on success, or an error HRESULT on failure.
--*/
{
    NTSTATUS status;
    HRESULT hr = S_OK;
    HANDLE hToken = NULL;
    ULONG sidLength = 0;
    bool fImpersonating = false;
    ULONG ulSize = 0;
    union
    {
        TOKEN_USER TokenUser;
        BYTE Rawbytes[sizeof(TOKEN_USER) + SECURITY_MAX_SID_SIZE];
    } TokenData;    

    //initialize output parameter
    *ppClientSid = nullptr;

    //
    // Impersonate the client to get their SID
    //
    status = I_RpcMapWin32Status(RpcImpersonateClient(NULL));
    if (!NT_SUCCESS(status))
    {
        hr = HRESULT_FROM_WIN32(status);
        TraceErr(AimxServer, "ImpersonateRpcClient failed: %!HRESULT!", hr);
        goto Exit;
    }

    fImpersonating = TRUE;

    hToken = NtThreadToken();
    if (hToken == NULL)
    {
        hr = E_ACCESSDENIED;
        TraceErr(AimxServer, "NtThreadToken failed: %!HRESULT!", hr);
        goto Exit;
    }

    // Now query the token for the user SID
    status = NtQueryInformationToken(
        hToken,
        TokenUser,
        &TokenData.TokenUser,
        sizeof(TokenData),
        &ulSize);
    if (!NT_SUCCESS(status))
    {
        hr = HRESULT_FROM_NT(status);
        TraceErr(AimxServer, "NtQueryInformationToken failed: %!HRESULT!", hr);
        goto Exit;
    }

    sidLength = RtlLengthSid(TokenData.TokenUser.User.Sid);
    *ppClientSid = (PSID)MIDL_user_allocate(sidLength);
    if (!*ppClientSid)
    {
        hr = E_OUTOFMEMORY;
        TraceErr(AimxServer, "MIDL_user_allocate failed: %!HRESULT!", hr);
        goto Exit;
    }

    RtlZeroMemory(*ppClientSid, sidLength);
    RtlCopyMemory(*ppClientSid, TokenData.TokenUser.User.Sid, sidLength);

Exit:

    if (fImpersonating)
    {
        // Revert to self to end impersonation
        status = I_RpcMapWin32Status(RpcRevertToSelf());
        if (status != RPC_S_OK)
        {
            hr = HRESULT_FROM_WIN32(status);
            TraceErr(AimxServer, "RevertToSelf failed: %!HRESULT!", hr);
        }
    }
    if (hToken)
    {
        NtClose(hToken);
        hToken = NULL;
    }
    
    return hr;
}

HRESULT
AimxpCreateNewContext(
    _Outptr_ AIMXR_HANDLE* pContextHandle
    )
/*++
Routine Description:
    Allocates and initializes a new AIMX context handle with a unique GUID.

Arguments:
    pContextHandle - Receives a pointer to the newly allocated context handle structure.

Return Value:
    S_OK on success, or an error HRESULT on failure.
--*/
{
    PAIMX_HANDLE localHandle = nullptr;
    GUID contextId;
    HRESULT hr = S_OK;

    // get the caller SID
    PSID pClientSid = nullptr;
    hr = AimxpGetRpcClientSid(&pClientSid);
    if (FAILED(hr))
    {
        TraceErr(AimxServer, "AimxpGetRpcClientSid failed: %!HRESULT!", hr);
        TraceInfo(AimxServer, "Exit");
        return hr;
    }
    TraceInfo(AimxServer, "Caller SID: %!sid!", pClientSid);

    localHandle = (PAIMX_HANDLE)MIDL_user_allocate(sizeof(AIMX_HANDLE));
    if (!localHandle)
    {
        TraceErr(AimxServer, "MIDL_user_allocate failed");
        TraceInfo(AimxServer, "Exit");
        return E_OUTOFMEMORY;
    }
    RtlZeroMemory(localHandle, sizeof(AIMX_HANDLE));

    // Generate a new GUID for the context
    hr = CoCreateGuid(&contextId);
    if (FAILED(hr))
    {
        TraceErr(AimxServer, "CoCreateGuid failed: %!HRESULT!", hr);
        goto Exit;
    }

    localHandle->pOwnerSid = pClientSid;
    pClientSid = nullptr; // context owns SID now.
    localHandle->ContextId = contextId;
    (*pContextHandle) = localHandle;
    localHandle = nullptr; // context owns handle now.

Exit:

    MIDL_user_free(pClientSid);
    AimxpFreeContext(localHandle);
    return hr;
}

// Helper function to validate a context handle
bool IsValidContextHandle(
    _In_ PAIMX_HANDLE pLocalHandle
)
{
    if (!pLocalHandle) return false;

    if (!IsContextHandleOwnedByCaller(pLocalHandle))
    {
        TraceErr(AimxServer, "Context handle is not owned by the caller");
        return false;
    }

    std::lock_guard<std::mutex> lock(g_ContextHandleMapMutex);
    return (g_ContextHandleMap.find(pLocalHandle->ContextId) != g_ContextHandleMap.end());
}

void
AimxpFreeContext(
    _In_opt_ PAIMX_HANDLE pContextHandle
    )
/*++
Routine Description:
    Frees the memory associated with an AIMX context handle and sets the pointer to nullptr.

Arguments:
    pContextHandle - Pointer to the context handle to be freed. Set to nullptr on return.

Return Value:
    S_OK always.
--*/
{
    TraceInfo(AimxServer, "Entry");

    if (pContextHandle)
    {
        MIDL_user_free(pContextHandle->pOwnerSid);
        pContextHandle->pOwnerSid = nullptr;
        MIDL_user_free(pContextHandle);
        pContextHandle = nullptr;
    }
    TraceInfo(AimxServer, "Exit");
}

extern "C" {
HRESULT
s_AimxrConnect(
    _In_ handle_t IDL_handle,
    _Out_ AIMXR_HANDLE* pContextHandle
    )
/*++
Routine Description:
    Handles an RPC request to connect to the AIMXSRV service. Allocates a new context handle for the caller.

Arguments:
    pContextHandle - Receives a pointer to the new context handle structure.

Return Value:
    S_OK on success, or an error HRESULT on failure.
--*/
{
    HRESULT hr = S_OK;
    TraceInfo(AimxServer, "Entry");

    UNREFERENCED_PARAMETER(IDL_handle);

    hr = AimxpCreateNewContext(pContextHandle);
    if (FAILED(hr))
    {
        TraceErr(AimxServer, "AimxpCreateNewContext failed: %!HRESULT!", hr);
        goto Exit;
    }

    // Add the new context handle to the map
    if (pContextHandle && *pContextHandle)
    {
        PAIMX_HANDLE pLocalHandle = (PAIMX_HANDLE)(*pContextHandle);
        std::lock_guard<std::mutex> lock(g_ContextHandleMapMutex);
        g_ContextHandleMap[pLocalHandle->ContextId] = pLocalHandle;
    }

Exit:
    TraceInfo(AimxServer, "Exit");
    return hr;
}

HRESULT
s_AimxrProcessPrompt(
    _In_ AIMXR_HANDLE contextHandle,
    _In_ LPCWSTR InputPrompt,
    _Out_ LPWSTR* Response
    )
/*++

Routine Description:
    Handles an RPC request to process an input prompt using the AIMX protocol.
    Parses the request type and delegates to appropriate handlers for chatbot queries,
    direct queries, plan status checks, plan execution, and operation cancellation.

Arguments:
    contextHandle - The context handle for the session.
    InputPrompt   - The input prompt string from the client (JSON format).
    Response      - Receives the allocated response string (caller must free).

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    HRESULT hr = S_OK;
    PAIMX_HANDLE pLocalHandle = nullptr;
    nlohmann::json requestJson;
    nlohmann::json responseJson;
    std::string inputUtf8;
    std::string responseUtf8;
    std::wstring responseWide;

    TraceInfo(AimxServer, "Entry");

    // Validate input parameters
    if (!Response)
    {
        TraceErr(AimxServer, "Response pointer is null");
        hr = E_INVALIDARG;
        goto Exit;
    }

    *Response = nullptr; // Initialize response to null

    if (!InputPrompt)
    {
        TraceErr(AimxServer, "InputPrompt is null");
        hr = E_INVALIDARG;
        goto Exit;
    }

    if (wcslen(InputPrompt) == 0)
    {
        TraceErr(AimxServer, "InputPrompt is empty");
        hr = E_INVALIDARG;
        goto Exit;
    }

    if (!contextHandle)
    {
        TraceErr(AimxServer, "contextHandle is null");
        hr = E_INVALIDARG;
        goto Exit;
    }

    pLocalHandle = (PAIMX_HANDLE)contextHandle;

    if (!IsValidContextHandle(pLocalHandle))
    {
        TraceErr(AimxServer, "contextHandle not found in map");
        hr = E_INVALIDARG;
        goto Exit;
    }

    // Convert wide string to UTF-8 for JSON parsing
    inputUtf8 = WideToUtf8(InputPrompt);
    if (inputUtf8.empty() && wcslen(InputPrompt) > 0)
    {
        TraceErr(AimxServer, "Failed to convert input to UTF-8");
        hr = E_INVALIDARG;
        goto Exit;
    }

    // Parse JSON request
    if (!nlohmann::json::accept(inputUtf8))
    {
        TraceErr(AimxServer, "Failed to parse JSON request");
        hr = E_INVALIDARG;
        goto Exit;
    }
    
    requestJson = nlohmann::json::parse(inputUtf8);

    // Unified request processing and dispatch
    hr = RequestHandler::ProcessRequest(contextHandle, requestJson, responseJson);
    if (FAILED(hr))
    {
        TraceErr(AimxServer, "Request processing failed: %!HRESULT!", hr);
        goto Exit;
    }

    // Convert response JSON to string
    responseUtf8 = responseJson.dump();

    // Convert UTF-8 response to wide string
    responseWide = Utf8ToWide(responseUtf8);
    
    *Response = (LPWSTR)MIDL_user_allocate((responseWide.length() + 1) * sizeof(wchar_t));
    if (!*Response)
    {
        TraceErr(AimxServer, "MIDL_user_allocate failed");
        hr = E_OUTOFMEMORY;
        goto Exit;
    }
    wcscpy_s(*Response, responseWide.length() + 1, responseWide.c_str());

    hr = S_OK;

Exit:
    TraceInfo(AimxServer, "Exit. hr: %!HRESULT!", hr);
    return hr;
}

HRESULT
s_AimxrClose(
    _Inout_ AIMXR_HANDLE* pContextHandle
    )
/*++
Routine Description:
    Handles an RPC request to close and free an AIMXSRV context handle.

Arguments:
    pContextHandle - Pointer to the context handle to be closed and freed.

Return Value:
    S_OK always.
--*/
{
    HRESULT hr = S_OK;
    PAIMX_HANDLE pLocalHandle = nullptr;
    TraceInfo(AimxServer, "Entry");

    // Validate the context handle
    if (!pContextHandle)
    {
        TraceErr(AimxServer, "pContextHandle is null");
        TraceInfo(AimxServer, "Exit");
        return E_INVALIDARG;
    }

    pLocalHandle = (PAIMX_HANDLE)*pContextHandle;
    if (!pLocalHandle)
    {
        TraceErr(AimxServer, "pLocalHandle is null");
        TraceInfo(AimxServer, "Exit");
        return E_INVALIDARG;
    }

    // Validate the context handle before proceeding
    if (!IsValidContextHandle(pLocalHandle))
    {
        TraceErr(AimxServer, "Attempted to close invalid or unknown context handle");
        TraceInfo(AimxServer, "Exit");
        return E_INVALIDARG;
    }

    // Remove from context handle map
    {
        std::lock_guard<std::mutex> lock(g_ContextHandleMapMutex);
        g_ContextHandleMap.erase(pLocalHandle->ContextId);
    }

    AimxpFreeContext(pLocalHandle);

    //invalidate the RPC handle
    *pContextHandle = nullptr;

    return hr;
}

void
__RPC_USER
AIMXR_HANDLE_rundown(
    _In_ AIMXR_HANDLE contextHandle
    )
/*++
Routine Description:
    RPC rundown function for AIMXR context handles.
    Called if the binding breaks when a handle is still active.

Arguments:
    contextHandle - The context handle to be cleaned up.

Return Value:
    None.
--*/
{
    TraceInfo(AimxServer, "Entry");
    // Call the cleanup function for the context handle
    AimxpFreeContext((PAIMX_HANDLE)contextHandle);
    TraceInfo(AimxServer, "Exit");
}

HRESULT
s_AimxrPollConversationMessages(
    _In_ AIMXR_HANDLE contextHandle,
    _Out_ LPWSTR* messages
    )
/*++

Routine Description:
    Polls for new conversation messages 

    Note - this is still a polling-based mechanism instead of callback based. the reason of that
    is this is a 2 hop RPC call from RPC to AimxRpcClient to AimxRpcServer, the 2 hopmarshaling over RPC
    does not support callback functions.

    The design here is we maintain a queue of messages in the ConversationSession class, and the client
    polls this queue for new messages and don't miss any of the transient messages when the state meachine 
    is moving fast between stages.

Arguments:
    contextHandle - The context handle for the session.
    messages - Output allocated string containing JSON array of messages (caller must free)

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    HRESULT hr = S_OK;

    if (!contextHandle || !messages)
    {
        TraceErr(AimxServer, "Invalid arguments");
        return E_INVALIDARG;
    }

    *messages = nullptr;

    PAIMX_HANDLE pHandle = (PAIMX_HANDLE)contextHandle;
    GUID contextGuid = pHandle->ContextId;

    // Validate the context handle before proceeding
    if (!IsValidContextHandle(pHandle))
    {
        TraceErr(AimxServer, "Attempted to close invalid or unknown context handle");
        TraceInfo(AimxServer, "Exit");
        return E_INVALIDARG;
    }

    // Get conversation session
    std::shared_ptr<ConversationSession> session;
    hr = ConversationSessionManager::GetSession(contextGuid, session);
    if (FAILED(hr))
    {
        TraceErr(AimxServer, "No conversation session found for context: %!GUID!", &contextGuid);
        return hr;
    }

    // Poll for new messages
    std::vector<AIMX_CONVERSATION_MESSAGE> newMessages;
    hr = session->PollMessages(newMessages);
    if (FAILED(hr))
    {
        TraceErr(AimxServer, "Failed to poll messages: %!HRESULT!", hr);
        return hr;
    }

    // Format messages as JSON string (same pattern as s_AimxrProcessPrompt)
    hr = FormatMessagesForPolling(newMessages, messages);
    if (FAILED(hr))
    {
        TraceErr(AimxServer, "Failed to format messages: %!HRESULT!", hr);
        return hr;
    }

    if (newMessages.size() > 0)
    {
        TraceInfo(AimxServer, "Polled %Iu messages for context: %!GUID!", newMessages.size(), &contextGuid);
    }
    
    return hr;
}

HRESULT
s_AimxrGetConversationStatus(
    _In_ AIMXR_HANDLE contextHandle,
    _Out_ LPWSTR* statusJson
    )
/*++

Routine Description:
    Gets the current status of the conversation session.

Arguments:
    contextHandle - The context handle for the session.
    statusJson - Output allocated string containing JSON status (caller must free)

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    HRESULT hr = S_OK;

    if (!contextHandle || !statusJson)
    {
        TraceErr(AimxServer, "Invalid arguments");
        return E_INVALIDARG;
    }

    *statusJson = nullptr;

    PAIMX_HANDLE pHandle = (PAIMX_HANDLE)contextHandle;
    GUID contextGuid = pHandle->ContextId;

    // Get conversation session
    std::shared_ptr<ConversationSession> session;
    hr = ConversationSessionManager::GetSession(contextGuid, session);
    if (FAILED(hr))
    {
        TraceErr(AimxServer, "No conversation session found for context: %!GUID!", &contextGuid);
        return hr;
    }

    // Get current status
    std::wstring statusDescription, currentStage;
    bool isActive;
    hr = session->GetCurrentStatus(statusDescription, currentStage, isActive);
    if (FAILED(hr))
    {
        TraceErr(AimxServer, "Failed to get conversation status: %!HRESULT!", hr);
        return hr;
    }

    // Format as JSON
    try
    {
        nlohmann::json statusObj;
        statusObj["stage"] = WideToUtf8(currentStage);
        statusObj["description"] = WideToUtf8(statusDescription);
        statusObj["isActive"] = isActive;

        // Add timestamp
        FILETIME currentTime;
        GetSystemTimeAsFileTime(&currentTime);
        SYSTEMTIME st;
        if (FileTimeToSystemTime(&currentTime, &st))
        {
            char timeStr[64];
            sprintf_s(timeStr, "%04d-%02d-%02dT%02d:%02d:%02d.%03dZ",
                st.wYear, st.wMonth, st.wDay,
                st.wHour, st.wMinute, st.wSecond, st.wMilliseconds);
            statusObj["timestamp"] = timeStr;
        }

        std::string jsonUtf8 = statusObj.dump();
        std::wstring jsonWide = Utf8ToWide(jsonUtf8);

        // Allocate string with MIDL_user_allocate (same pattern as other methods)
        size_t statusLen = jsonWide.length() + 1;
        *statusJson = (LPWSTR)MIDL_user_allocate(statusLen * sizeof(wchar_t));
        if (!*statusJson)
        {
            TraceErr(AimxServer, "Failed to allocate status string");
            return E_OUTOFMEMORY;
        }

        wcscpy_s(*statusJson, statusLen, jsonWide.c_str());
    }
    catch (const std::exception& ex)
    {
        TraceErr(AimxServer, "Exception formatting status: %s", ex.what());
        return E_FAIL;
    }

    return hr;
}

HRESULT
s_AimxrStartConversation(
    _In_ AIMXR_HANDLE contextHandle,
    _In_ LPCWSTR query,
    _In_ LONG executionMode
    )
/*++

Routine Description:
    Starts an interactive conversation with real-time updates.

Arguments:
    contextHandle - The context handle for the session.
    query - The query to process.
    executionMode - The execution mode (automated or interactive).

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxServer, "Entry");
    HRESULT hr = S_OK;
    nlohmann::json requestJson;
    nlohmann::json responseJson;

    if (!contextHandle || !query)
    {
        TraceErr(AimxServer, "Invalid arguments");
        hr = E_INVALIDARG;
        goto Exit;
    }

    // Build request JSON for conversation
    try
    {
        requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_REQUEST_TYPE] = AIMX_CHATBOT_QUERY;
        requestJson[AimxConstants::JsonFields::AIMX_JSON_KEY_QUERY] = WideToUtf8(query);
        requestJson[AimxConstants::JsonFields::AIMX_EXECUTION_MODE] = executionMode;
    }
    catch (...)
    {
        TraceErr(AimxServer, "Failed to build request JSON");
        hr = E_FAIL;
        goto Exit;
    }

    // Process the request using existing RequestHandler
    hr = RequestHandler::ProcessRequest(contextHandle, requestJson, responseJson);
    if (FAILED(hr))
    {
        TraceErr(AimxServer, "RequestHandler::ProcessRequest failed: %!HRESULT!", hr);
        goto Exit;
    }

    TraceInfo(AimxServer, "Conversation started successfully");

Exit:
    TraceInfo(AimxServer, "Exit. hr: %!HRESULT!", hr);
    return hr;
}

HRESULT
s_AimxrGetLlmStatus(
    _In_ AIMXR_HANDLE contextHandle,
    _Out_ LPWSTR* statusJson
    )
/*++

Routine Description:
    Gets the current LLM service status and connectivity information.

Arguments:
    contextHandle - The context handle for the session.
    statusJson - Output allocated string containing JSON status (caller must free)

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxServer, "Entry");
    HRESULT hr = S_OK;

    if (!contextHandle || !statusJson)
    {
        TraceErr(AimxServer, "Invalid arguments");
        return E_INVALIDARG;
    }

    *statusJson = nullptr;

    try
    {
        // Test LLM connectivity
        std::wstring testResult;
        hr = LLMInfer::TestLlmConnectivity(testResult);

        // Build status JSON
        nlohmann::json statusObj;
        statusObj["llmConnectivity"] = SUCCEEDED(hr);
        statusObj["testResult"] = WideToUtf8(testResult);
        statusObj["statusCode"] = static_cast<int>(hr);
        statusObj["timestamp"] = std::time(nullptr);

        // Get LLM instance status
        LLMInfer* instance = LLMInfer::GetInstance();
        statusObj["instanceInitialized"] = (instance != nullptr);

        // Convert to string
        std::string jsonUtf8 = statusObj.dump();
        std::wstring jsonWide = Utf8ToWide(jsonUtf8);

        // Allocate string with MIDL_user_allocate
        size_t statusLen = jsonWide.length() + 1;
        *statusJson = (LPWSTR)MIDL_user_allocate(statusLen * sizeof(wchar_t));
        if (!*statusJson)
        {
            TraceErr(AimxServer, "Failed to allocate status string");
            return E_OUTOFMEMORY;
        }

        wcscpy_s(*statusJson, statusLen, jsonWide.c_str());

        TraceInfo(AimxServer, "LLM status retrieved successfully");
    }
    catch (const std::exception& ex)
    {
        TraceErr(AimxServer, "Exception getting LLM status: %s", ex.what());
        hr = E_FAIL;
    }
    catch (...)
    {
        TraceErr(AimxServer, "Unknown exception getting LLM status");
        hr = E_FAIL;
    }

    TraceInfo(AimxServer, "Exit. hr: %!HRESULT!", hr);
    return hr;
}

HRESULT
s_AimxrGetMcpServerInfo(
    _In_ AIMXR_HANDLE contextHandle,
    _Out_ LPWSTR* serverInfoJson
    )
/*++

Routine Description:
    Gets information about registered MCP servers and their available tools.

Arguments:
    contextHandle - The context handle for the session.
    serverInfoJson - Output allocated string containing JSON server info (caller must free)

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxServer, "Entry");
    HRESULT hr = S_OK;

    if (!contextHandle || !serverInfoJson)
    {
        TraceErr(AimxServer, "Invalid arguments");
        return E_INVALIDARG;
    }

    *serverInfoJson = nullptr;

    try
    {
        // Get all enabled servers
        std::vector<MCP_SERVER_INFO> enabledServers;
        hr = McpSvrMgr::GetEnabledMcpServers(enabledServers);
        if (FAILED(hr))
        {
            TraceErr(AimxServer, "Failed to enumerate enabled servers: %!HRESULT!", hr);            
            enabledServers.clear();
            return hr;
        }

        // Build server info JSON (without detailed tool information)
        nlohmann::json infoObj;
        infoObj["timestamp"] = std::time(nullptr);
        infoObj["serverCount"] = enabledServers.size();

        // Server details (basic info only)
        nlohmann::json serversArray = nlohmann::json::array();
        for (const auto& server : enabledServers)
        {
            nlohmann::json serverObj;
            serverObj["name"] = WideToUtf8(server.serverName.c_str());
            serverObj["description"] = WideToUtf8(server.description.c_str());
            serverObj["type"] = (server.serverType == MCP_SERVER_TYPE::IN_PROCESS) ? "in-process" : "out-of-process";
            serverObj["status"] = static_cast<int>(server.status);
            serverObj["enabled"] = server.isEnabled;
            serverObj["toolCount"] = server.availableTools.size();

            serversArray.push_back(serverObj);
        }
        infoObj["servers"] = serversArray;

        // Summary by server type
        int inProcessCount = 0, outOfProcessCount = 0;
        int totalToolCount = 0;
        for (const auto& server : enabledServers)
        {
            if (server.serverType == MCP_SERVER_TYPE::IN_PROCESS)
                inProcessCount++;
            else
                outOfProcessCount++;

            totalToolCount += static_cast<int>(server.availableTools.size());
        }

        nlohmann::json summaryObj;
        summaryObj["inProcessServers"] = inProcessCount;
        summaryObj["outOfProcessServers"] = outOfProcessCount;
        summaryObj["totalServers"] = enabledServers.size();
        summaryObj["totalTools"] = totalToolCount;
        infoObj["summary"] = summaryObj;

        // Convert to string
        std::string jsonUtf8 = infoObj.dump();
        std::wstring jsonWide = Utf8ToWide(jsonUtf8);

        // Allocate string with MIDL_user_allocate
        size_t infoLen = jsonWide.length() + 1;
        *serverInfoJson = (LPWSTR)MIDL_user_allocate(infoLen * sizeof(wchar_t));
        if (!*serverInfoJson)
        {
            TraceErr(AimxServer, "Failed to allocate server info string");
            return E_OUTOFMEMORY;
        }

        wcscpy_s(*serverInfoJson, infoLen, jsonWide.c_str());

        TraceInfo(AimxServer, "MCP server info retrieved successfully");
    }
    catch (const std::exception& ex)
    {
        TraceErr(AimxServer, "Exception getting MCP server info: %s", ex.what());
        hr = E_FAIL;
    }
    catch (...)
    {
        TraceErr(AimxServer, "Unknown exception getting MCP server info");
        hr = E_FAIL;
    }

    TraceInfo(AimxServer, "Exit. hr: %!HRESULT!", hr);
    return hr;
}

} // extern "C"