# RAG Service (ragsrv)

## Overview

RAG Service is a Windows service that provides Retrieval Augmented Generation (RAG) functionality through a REST API. It allows users to build RAG databases from document collections, query those databases, and manage RAG jobs asynchronously.

## Key Features

- **Document Processing**: Extract text from various document types
- **Text Chunking**: Process documents into semantically-meaningful chunks
- **Semantic Chunking**: Combine related chunks based on embedding similarity
- **Vector Embeddings**: Generate embeddings for document chunks using llm-infer.dll
- **Vector Search**: Build and query vector indices using HNSWLIB
- **HTTP API**: RESTful API for database building, querying, and job management
- **Asynchronous Jobs**: Process long-running tasks in the background

## Architecture

The service is composed of several key components:

1. **RagService**: Windows service implementation
2. **RagHttpServer**: HTTP server providing REST API endpoints
3. **RagDatabaseBuilder**: Core component for building RAG databases
4. **RagJobManager**: Manages asynchronous database build jobs

## File Structure

```
ragsrv/
├── RagService.h/cpp          - Windows service implementation
├── RagHttpServer.h/cpp       - HTTP REST API server
├── RagDatabaseBuilder.h/cpp  - Core RAG database building functionality
├── RagJobManager.h/cpp       - Asynchronous job management
├── main.cpp                  - Entry point for the service
├── ragsrv.rc                 - Resource file for the Windows service
└── README.md                 - Documentation
```

## API Endpoints

- **POST /build** - Start building a RAG database
- **GET /status/{job_id}** - Check status of a database build job
- **GET /jobs** - List all jobs
- **POST /test** - Test a RAG database with a query
- **GET /health** - Health check endpoint

## Dependencies

- **llm-infer.dll** - Embedding model interface library
- **hnswlib** - Vector search library
- **httplib** - HTTP server library
- **nlohmann/json** - JSON parsing library

## Building a RAG Database

To build a RAG database:

1. Send a POST request to `/build` with parameters:
   - `folder_path`: Path to directory containing documents
   - `output_dir`: Path to save the RAG database
   - `max_chunk_size`: Maximum token size for chunks (default: 1000)
   - `similarity_threshold`: Threshold for semantic chunking (default: 0.7)
   - `model_name`: Embedding model name (default: "all-MiniLM-L6-v2")
   - `use_gpu`: Whether to use GPU acceleration (default: true)

2. The server returns a job ID that can be used to monitor progress:
   ```json
   {
     "job_id": "job_1712345678",
     "status": "started",
     "message": "RAG database build started from /path/to/docs",
     "using_gpu": true
   }
   ```

3. Monitor job progress using `/status/{job_id}` endpoint.

## Querying a RAG Database

To query a RAG database:

1. Send a POST request to `/test` with parameters:
   - `index_path`: Path to the .bin index file
   - `query`: Query text
   - `k`: Number of results to return (default: 5)

2. The server returns matching chunks:
   ```json
   {
     "query": "sample query",
     "using_gpu": true,
     "model": "all-MiniLM-L6-v2",
     "result_count": 3,
     "max_results": 5,
     "results": [
       {
         "rank": 1,
         "score": 0.92,
         "distance": 0.08,
         "text": "Sample chunk text...",
         "source": "document1.txt",
         "tokens": 120
       }
     ]
   }
   ```

