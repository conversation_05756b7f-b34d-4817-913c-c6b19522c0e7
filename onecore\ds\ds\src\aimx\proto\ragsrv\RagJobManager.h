/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    RagJobManager.h

Abstract:

    This module defines the job management class for the RAG service.
    Tracks and manages asynchronous RAG build jobs.

Author:

    <PERSON><PERSON><PERSON> (r<PERSON><PERSON>) 04/12/2025

--*/

#pragma once

#include <string>
#include <map>
#include <mutex>
#include "../common/nlohmann/json.hpp"

using json = nlohmann::json;

// Job status structure
struct RagJobStatus 
{
    std::string status;       // "extracting", "chunking", "embedding", "indexing", "saving", "completed", "failed"
    double progress;          // Progress from 0.0 to 1.0
    std::string message;      // Status message
    bool completed;           // Whether the job is completed
    bool usingGpu;            // Whether the job is using GPU
    int documentCount;        // Number of documents processed
    std::map<std::string, int> documentTypes;  // Types of documents and their counts
    int chunkCount;           // Number of chunks created
    int totalTokens;          // Total tokens in all chunks

    // Constructor with default values
    RagJobStatus()
        : status("created")
        , progress(0.0)
        , message("Job created")
        , completed(false)
        , usingGpu(false)
        , documentCount(0)
        , chunkCount(0)
        , totalTokens(0)
    {
    }
    
    // Convert to JSON
    json ToJson() const;
};

class RagJobManager 
{
public:
    RagJobManager();
    ~RagJobManager();
    
    // Create a new job
    _Ret_z_ std::string 
    CreateJob();
    
    // Update job status
    void 
    UpdateJobStatus(
        _In_z_ const std::string& jobId, 
        _In_ const RagJobStatus& status
        );
    
    // Get job status
    json 
    GetJobStatus(
        _In_z_ const std::string& jobId
        );
    
    // Check if job exists
    bool 
    HasJob(
        _In_z_ const std::string& jobId
        );
    
    // Get all jobs
    json 
    GetAllJobs();
    
    // Get counts for active and total jobs
    int 
    GetActiveJobCount();

    int 
    GetTotalJobCount();

private:
    // Map of job IDs to job statuses
    std::map<std::string, RagJobStatus> m_jobs;
    
    // Mutex for thread safety
    std::mutex m_mutex;

    // Generate a unique job ID
    _Ret_z_ std::string 
    GenerateJobId();
};