/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    AppConfig.cpp

Abstract:

    This module implements App configration retrieval and storage for the AIMX app.
    Allows settings for logging, RAG support, prompt length, and output directory to be globally accessible.

Author:

    <PERSON><PERSON> (solaadekunle) 05/16/2025

--*/

#include "AppConfig.h"
#include "pch.h"
#include <fstream>

AppConfig::AppConfig()
    : enableLogging(false),
      enableRagSupport(false),
      maxPromptLength(1024),
      defaultRagDatabase("")
{
}

AppConfig& AppConfig::getInstance()
{
    static AppConfig instance;
    return instance;
}

void AppConfig::init()
{
    LOGINFO("Initializing application configuration");

    std::ifstream configFile("./configuration/rag.json");
    if (configFile.is_open()) {
        nlohmann::json configJson;
        try {
            configJson = nlohmann::json::parse(configFile);
            enableLogging = configJson.value("enableLogging", false);
            enableRagSupport = configJson.value("enableRagSupport", false);
            maxPromptLength = configJson.value("maxPromptLength", 1024);
            defaultRagDatabase = configJson.value("defaultDatabase", "");

            if (configJson.contains("databases") && configJson["databases"].is_array()) {
                LOGINFO("Reading RAG databases from configuration file");
                ragDatabases.clear();
                for (const auto& db : configJson["databases"]) {
                    if (db.contains("name") && db["name"].is_string()) {
                        ragDatabases.push_back(db["name"].get<std::string>());
                    }
                }
                LOGINFO("RAG databases loaded: " + std::to_string(ragDatabases.size()));
            } else {
                ragDatabases.clear();
            }
        } catch (const std::exception& e) {
            LOGERROR("Error reading configuration: " + std::string(e.what()));
        }
        configFile.close();
    } else {
        LOGINFO("No configuration file found, using default settings");
    }
}

bool AppConfig::isLoggingEnabled() const { return enableLogging; }
bool AppConfig::isRagSupportEnabled() const { return enableRagSupport; }
int AppConfig::getMaxPromptLength() const { return maxPromptLength; }
const std::string& AppConfig::getDefaultRagDatabase() const { return defaultRagDatabase; }
const std::vector<std::string>& AppConfig::getRagDatabases() const { return ragDatabases; }