======================================================================
                AD MCP SERVER - TEST SUITE DOCUMENTATION
======================================================================

OVERVIEW:
Comprehensive test suite for Active Directory MCP (Model Context Protocol) Server.
Tests all core functionality including server initialization, tool definitions,
command mapping, LLM integration, error handling, and concurrent operations.

======================================================================
                              FILE LISTING
======================================================================

CORE TEST FILES:
----------------
AimxMcpToolTests.h          - Test class definition with 9 comprehensive test methods
AimxMcpToolTests.cpp        - Complete test implementation with helper methods and utilities
pch.hxx                     - Precompiled header for test dependencies and OS headers

BUILD AND CONFIGURATION FILES:
-------------------------------
sources                     - Windows build system configuration for TAEF test DLL
AimxMcpSrvTaef.def         - TAEF module definition file for test exports and DLL entry points


======================================================================
                           TEST CASES SUMMARY
======================================================================

CURRENT TEST METHODS (9 Total):
================================

Priority 1 Tests (Core Functionality):
---------------------------------------
1. TestMcpServerInitialization     - Validates MCP server startup, command map initialization, and command availability
2. TestMcpToolDefinitions          - Verifies get_ad_command tool schema definition and required JSON fields
3. TestExportedGetAdToolsPromptApi - Tests exported GetADToolsPrompt API with validation and error handling

Priority 2 Tests (Feature Validation):
---------------------------------------
4. TestToolsListEndpoint           - Validates MCP tools/list endpoint returns correct tool definitions array
5. TestGetAdCommandTool            - Tests get_ad_command tool with various AD prompts (users, computers, groups, DCs)
6. TestCommandMapping              - Validates natural language to AD PowerShell command mapping and query handling
7. TestLlmEndpointConfiguration    - Tests registry-based LLM endpoint URL configuration and fallback mechanisms

Priority 3 Tests (Robustness & Edge Cases):
--------------------------------------------
8. TestErrorHandlingAndEdgeCases   - Validates error handling for malformed requests, invalid tools, missing parameters
9. TestConcurrentMcpOperations     - Tests concurrent command mapping operations with multiple threads (5 simultaneous)

======================================================================
                        DETAILED TEST SCENARIOS
======================================================================

INITIALIZATION TESTING:
• MCP server startup and component initialization validation
• Command map population and availability verification (>0 commands)
• System readiness and dependency validation

TOOL DEFINITION TESTING:
• JSON schema validation for MCP tool definitions (get_ad_command)
• Required field validation (name, description, input_schema, output_schema)
• Input schema properties validation (prompt parameter requirements)

FUNCTIONAL TESTING:
• Natural language prompt processing for AD operations
• Command mapping accuracy for various query types:
  - "list all users" → user-related commands
  - "find computer accounts" → computer-related commands
  - "show domain controllers" → DC-related commands
  - "get group members" → group-related commands
• Response format and content validation

API TESTING:
• GetADToolsPrompt exported function parameter validation
• Return value (HRESULT) and error code verification
• JSON response structure validation
• Empty prompt handling (E_INVALIDARG expected)

CONFIGURATION TESTING:
• Registry-based LLM endpoint configuration (HKLM\SOFTWARE\Microsoft\Aimx)
• Test registry key setup and cleanup procedures
• Configuration error handling and fallback validation

ROBUSTNESS TESTING:
• Concurrent operation handling (5 simultaneous threads)
• Error scenario validation (malformed JSON, invalid tools, missing params)
• Edge case processing (empty queries, uninitialized state)
• JSON-RPC 2.0 protocol compliance verification

======================================================================
                          HELPER METHODS
======================================================================

UTILITY FUNCTIONS:
==================
CreateMcpRequest()              - Creates properly formatted JSON-RPC MCP requests for testing
ValidateJsonRpcResponse()       - Validates JSON-RPC 2.0 response format and required fields
ValidateToolDefinition()        - Validates MCP tool definition schema compliance
SetupTestRegistry()             - Creates test registry keys for LLM endpoint configuration
CleanupTestRegistry()           - Removes test registry keys after configuration testing

VALIDATION HELPERS:
===================
• JSON-RPC 2.0 format validation (jsonrpc, method, params, id fields)
• Tool definition schema validation (name, description, input/output schemas)
• Response structure validation (result or error fields)
• Registry key management for configuration testing

======================================================================
                           TEST COVERAGE
======================================================================

COMPREHENSIVE COVERAGE:
• 9 test methods covering all major MCP server functionality
• End-to-end workflow validation from natural language prompt to AD command
• Error handling and edge case scenarios with proper exception testing
• Multi-threaded operation testing with 5 concurrent workers
• JSON-RPC 2.0 protocol compliance verification
• Registry-based configuration management testing
• Exported API validation with proper HRESULT handling

TESTED COMPONENTS:
• AdMcpSrv - Main server class and initialization
• AdMcpCommandMap - Natural language to AD command mapping
• AdMcpLlmInference - LLM integration and endpoint configuration
• AdMcpTools - Tool definition schemas and validation
• GetADToolsPrompt - Primary exported API function

======================================================================
                              USAGE GUIDE
======================================================================

RUNNING TESTS:
==============
Build and run using TAEF framework:

# Build test DLL
build -c -z

# Run all tests
te.exe AimxMcpSrvTaef.dll

# Filter by priority level
te.exe AimxMcpSrvTaef.dll /select:"@Priority=1"     # Core functionality only
te.exe AimxMcpSrvTaef.dll /select:"@Priority=2"     # Feature validation only
te.exe AimxMcpSrvTaef.dll /select:"@Priority=3"     # Robustness testing only

# Run specific test method
te.exe AimxMcpSrvTaef.dll /select:"@Name='AdMcpToolTests::TestMcpServerInitialization'"

# Run with verbose logging
te.exe AimxMcpSrvTaef.dll /enablewttlogging

