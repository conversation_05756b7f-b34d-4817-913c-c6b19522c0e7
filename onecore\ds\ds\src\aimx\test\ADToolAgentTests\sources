TARGETNAME=ADToolAgentTaef
TARGETTYPE=<PERSON><PERSON><PERSON><PERSON>K
TARGET_DESTINATION=retail

TEST_CODE=1

USE_MSVCRT=1
USE_UNICRT=1
USE_STL=1
STL_VER=STL_VER_CURRENT

C_DEFINES=$(C_DEFINES) -DWIN32 -D_WIN32 -DUNICODE -D_UNICODE

# Disable these warnings for string conversion issues and enable exception handling
MSC_WARNING_LEVEL=/W3 /EHsc /wd4244 /wd4267

PRECOMPILED_INCLUDE = pch.hxx
COMPILE_CXX = 1

# Enable C++ exception handling with proper unwind semantics
USE_NATIVE_EH=1

DLLENTRY=_DllMainCRTStartup

SOURCES=\
    ADToolAgentTests.cpp \
    ADReplicationToolAgentTests.cpp \

INCLUDES=\
    ..\..\prod\common; \
    ..\..\prod\common\nlohmann; \
    ..\..\prod\ADToolAgent; \
    $(OBJ_PATH); \
    $(OBJ_PATH)\$(O); \
    $(BASE_INC_PATH); \
    $(INTERNAL_SDK_INC_PATH); \
    $(ONECORE_INTERNAL_SDK_INC_PATH); \
    $(ONECORE_INTERNAL_PRIV_SDK_INC_PATH_L); \
    $(ONECORESDKTOOLS_INTERNAL_INC_PATH_L)\wextest\cue; \

TARGETLIBS=\
    $(OBJ_PATH)\..\..\prod\agents\ADToolAgent\$(O)\ADToolAgent.lib \
    $(ONECORE_EXTERNAL_SDK_LIB_PATH)\ntdll.lib \
    $(ONECORE_EXTERNAL_SDK_LIB_PATH)\rpcrt4.lib \
    $(ONECORE_EXTERNAL_SDK_LIB_PATH)\ntdsapi.lib \
    $(MINCORE_EXTERNAL_SDK_LIB_VPATH_L)\mincore.lib \
    $(ONECORESDKTOOLS_INTERNAL_LIB_PATH_L)\WexTest\Cue\Wex.Common.lib \
    $(ONECORESDKTOOLS_INTERNAL_LIB_PATH_L)\WexTest\Cue\Wex.Logger.lib \
    $(ONECORESDKTOOLS_INTERNAL_LIB_PATH_L)\WexTest\Cue\Te.Common.lib \

MUI_VERIFY_NO_LOC_RESOURCE=1