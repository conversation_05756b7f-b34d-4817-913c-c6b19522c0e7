/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    AimxServiceTests.h

Abstract:
    Header file for comprehensive AIMX Service component testing.

--*/

#pragma once

#include "WexTestClass.h"

using namespace WEX::Logging;
using namespace WEX::Common;
using namespace WEX::TestExecution;

class AimxServiceTests : public WEX::TestClass<AimxServiceTests>
{
public:
    BEGIN_TEST_CLASS(AimxServiceTests)
        TEST_CLASS_PROPERTY(L"TestClass", L"AIMX Service")
        TEST_CLASS_PROPERTY(L"Description", L"Comprehensive AIMX Service component testing")
    END_TEST_CLASS()

    // Test service initialization and RPC server startup
    BEGIN_TEST_METHOD(TestServiceInitialization)
        TEST_METHOD_PROPERTY(L"Priority", L"1")
        TEST_METHOD_PROPERTY(L"Description", L"Tests service initialization and RPC server startup")
    END_TEST_METHOD()

    // Test RequestHandler component integration
    BEGIN_TEST_METHOD(TestRequestHandlerComponent)
        TEST_METHOD_PROPERTY(L"Priority", L"1")
        TEST_METHOD_PROPERTY(L"Description", L"Tests RequestHandler static method routing and validation")
    END_TEST_METHOD()

    // Test Planner integration through RequestHandler
    BEGIN_TEST_METHOD(TestPlannerIntegration)
        TEST_METHOD_PROPERTY(L"Priority", L"2")
        TEST_METHOD_PROPERTY(L"Description", L"Tests Planner component integration and asynchronous planning")
    END_TEST_METHOD()

    // Test Orchestrator integration through RequestHandler
    BEGIN_TEST_METHOD(TestOrchestratorIntegration)
        TEST_METHOD_PROPERTY(L"Priority", L"2")
        TEST_METHOD_PROPERTY(L"Description", L"Tests Orchestrator component integration and execution")
    END_TEST_METHOD()

    // Test concurrent operations handling
    BEGIN_TEST_METHOD(TestConcurrentOperations)
        TEST_METHOD_PROPERTY(L"Priority", L"3")
        TEST_METHOD_PROPERTY(L"Description", L"Tests service handling of multiple concurrent operations")
    END_TEST_METHOD()

    // Test error recovery and service stability
    BEGIN_TEST_METHOD(TestErrorRecovery)
        TEST_METHOD_PROPERTY(L"Priority", L"3")
        TEST_METHOD_PROPERTY(L"Description", L"Tests service error handling and recovery")
    END_TEST_METHOD()
};