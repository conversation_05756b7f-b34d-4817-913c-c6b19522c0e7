/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    ADReplicationToolAgentTests.cpp

Abstract:    
    TAEF test suite for ADReplicationToolAgent component.

Author:
    <PERSON><PERSON><PERSON><PERSON> (pumathur) 06/18/2025

--*/

#include "pch.hxx"
#include <vector>
#include "ADReplicationToolAgentTests.h"
#include <nlohmann/json.hpp>
#include <chrono>
#include <thread>
#include <atomic>
#include <mutex>
#include <future>
#include <psapi.h>  // For GetProcessMemoryInfo

void ADReplicationToolAgentTests::TestSingletonInstance()
{
    Log::Comment(L"====== Starting TestSingletonInstance ======");
    Log::Comment(L"Testing ADReplicationToolAgent singleton instance");
    
    // Get instance of ADReplicationToolAgent
    ADReplicationToolAgent& agent1 = ADReplicationToolAgent::GetADReplicationToolAgentInstance();
    Log::Comment(L"First instance obtained");
    
    ADReplicationToolAgent& agent2 = ADReplicationToolAgent::GetADReplicationToolAgentInstance();
    Log::Comment(L"Second instance obtained");
    
    // Verify that both references point to the same instance
    VERIFY_ARE_EQUAL(&agent1, &agent2, L"Singleton should return the same instance");
    Log::Comment(String().Format(L"Instance 1 address: 0x%p, Instance 2 address: 0x%p", &agent1, &agent2));
    
    Log::Comment(L"ADReplicationToolAgent singleton instance verified");
    Log::Comment(L"====== Finished TestSingletonInstance ======");
}

void ADReplicationToolAgentTests::TestJsonProcessing()
{
    Log::Comment(L"====== Starting TestJsonProcessing ======");
    Log::Comment(L"Testing JSON processing in ADReplicationToolAgent");
    
    ADReplicationToolAgent& agent = ADReplicationToolAgent::GetADReplicationToolAgentInstance();
    Log::Comment(L"ADReplicationToolAgent instance obtained");
    
    // Test case 1: Valid JSON input for individual DC
    {
        Log::Comment(L"Test case 1: Valid JSON input for individual DC");
        
        nlohmann::json request;
        request[AimxConstants::JsonFields::AIMX_ACTION] = AimxConstants::ActionTypes::AIMX_GET_REPLICATION_STATUS_INDIVIDUAL_DC;
        request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";

        nlohmann::json response;
        
        HRESULT hr = agent.GetReplicationInformation(request, response);
        Log::Comment(String().Format(L"GetReplicationInformation returned HRESULT: 0x%08X", hr));
        
        // Verify response format (no need to check if valid JSON since it's already a JSON object)
        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
    }
    
    // Test case 2: Valid JSON input for all DCs
    {
        Log::Comment(L"Test case 2: Valid JSON input for all DCs");
        
        nlohmann::json request;
        request[AimxConstants::JsonFields::AIMX_ACTION] = AimxConstants::ActionTypes::AIMX_GET_REPLICATION_STATUS_ALL_DCS;
        request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";

        nlohmann::json response;
        
        HRESULT hr = agent.GetReplicationInformation(request, response);
        Log::Comment(String().Format(L"GetReplicationInformation returned HRESULT: 0x%08X", hr));
        
        // Verify response format
        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
    }
    
    // Test case 3: Invalid action
    {
        Log::Comment(L"Test case 3: Invalid action");
        
        nlohmann::json request;
        request[AimxConstants::JsonFields::AIMX_ACTION] = "InvalidAction";
        request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";

        nlohmann::json response;
        
        HRESULT hr = agent.GetReplicationInformation(request, response);
        Log::Comment(String().Format(L"GetReplicationInformation returned HRESULT: 0x%08X", hr));
        
        // For invalid action, we should get an error HRESULT
        VERIFY_IS_FALSE(SUCCEEDED(hr), L"GetReplicationInformation should return error HRESULT for invalid action");
        
        if (0 != strcmp(response[AimxConstants::JsonFields::AIMX_RESULT].get<std::string>().c_str(), AimxConstants::JsonFields::AIMX_FAILURE))
        {
            VERIFY_FAIL(L"Test case 3: Invalid action failed");
        }
    }
    
    // Test case 4: Missing required fields
    {
        Log::Comment(L"Test case 4: Missing required fields");
        
        nlohmann::json request;
        // Missing Action field
        request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";

        nlohmann::json response;
        
        HRESULT hr = agent.GetReplicationInformation(request, response);
        Log::Comment(String().Format(L"GetReplicationInformation returned HRESULT: 0x%08X", hr));
        
        // For missing Action field, we should get an error HRESULT
        VERIFY_IS_FALSE(SUCCEEDED(hr), L"GetReplicationInformation should return error HRESULT for missing Action field");
        
        if (0 != strcmp(response[AimxConstants::JsonFields::AIMX_RESULT].get<std::string>().c_str(), AimxConstants::JsonFields::AIMX_FAILURE))
        {
            VERIFY_FAIL(L"Test case 4: Missing required fields failed");
        }
    }
    // Test case 5: Invalid JSON (test parsing error at test level)
    {
        Log::Comment(L"Test case 5: Invalid JSON");
        std::string invalidJsonStr = "{ invalid json }";
        
        Log::Comment(L"Testing JSON parse error handling at test level");
        
        // Attempt to parse invalid JSON at the test level (before calling agent)
        try
        {
            nlohmann::json parsedJson = nlohmann::json::parse(invalidJsonStr);
            
            // Should not reach here
            VERIFY_FAIL(L"Invalid JSON should have thrown an exception");
            
            // Create a valid request for the agent just in case we reached here
            nlohmann::json request;
            request[AimxConstants::JsonFields::AIMX_ACTION] = AimxConstants::ActionTypes::AIMX_GET_REPLICATION_STATUS_INDIVIDUAL_DC;
            request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";

            nlohmann::json response;
            HRESULT hr = agent.GetReplicationInformation(request, response);
            Log::Comment(String().Format(L"GetReplicationInformation returned HRESULT: 0x%08X", hr));
        }
        catch (const nlohmann::json::exception& e)
        {
            // Successfully caught the JSON parse exception
            Log::Comment(String().Format(L"Successfully caught JSON parse exception: %hs", e.what()));
            
            // Now test the agent with a malformed but valid JSON object
            // (valid JSON syntax but with missing required fields)
            nlohmann::json badRequest = nlohmann::json::object();
            // Intentionally missing Action field
            
            nlohmann::json response;
            HRESULT hr = agent.GetReplicationInformation(badRequest, response);
            
            // For missing Action field, we should get an error HRESULT
            VERIFY_IS_FALSE(SUCCEEDED(hr), L"GetReplicationInformation should return error HRESULT for malformed JSON");
            Log::Comment(String().Format(L"GetReplicationInformation returned HRESULT: 0x%08X", hr));
            
            // Verify error response format
            VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
            VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_JSON_ERROR), L"Response should contain Error field");
            
            if (response.contains(AimxConstants::JsonFields::AIMX_RESULT)) {
                VERIFY_ARE_EQUAL(std::string(AimxConstants::JsonFields::AIMX_FAILURE), response[AimxConstants::JsonFields::AIMX_RESULT].get<std::string>(), 
                                L"Result field should be 'Failure'");
            }
            
            Log::Comment(String().Format(L"Error field found: %hs", response.contains(AimxConstants::JsonFields::AIMX_JSON_ERROR) ? "Yes" : "No"));
            if (response.contains(AimxConstants::JsonFields::AIMX_JSON_ERROR)) {
                Log::Comment(String().Format(L"Error message: %hs", response[AimxConstants::JsonFields::AIMX_JSON_ERROR].get<std::string>().c_str()));
            }
        }
    }
    
    Log::Comment(L"JSON processing tests completed");
    Log::Comment(L"====== Finished TestJsonProcessing ======");
}

void ADReplicationToolAgentTests::TestGetReplicationStatusForIndividualDC()
{
    Log::Comment(L"====== Starting TestGetReplicationStatusForIndividualDC ======");
    Log::Comment(L"Testing GetReplicationStatusForIndividualDC to verify individual DC replication status retrieval");
    
    // Get instance of ADReplicationToolAgent - this now derives from ADToolAgent
    ADReplicationToolAgent& agent = ADReplicationToolAgent::GetADReplicationToolAgentInstance();
    Log::Comment(L"ADReplicationToolAgent instance obtained");
    
    // Create request for individual DC
    nlohmann::json request;
    request[AimxConstants::JsonFields::AIMX_ACTION] = AimxConstants::ActionTypes::AIMX_GET_REPLICATION_STATUS_INDIVIDUAL_DC;
    request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost"; // Use local machine for test

    Log::Comment(String().Format(L"Request created: %hs", request.dump().c_str()));
    
    nlohmann::json response;
    
    // Check if we're in a domain environment
    bool isInDomain = IsDomainJoined(agent);
    if (isInDomain)
    {
        Log::Comment(L"Machine is domain-joined - expecting success");
    }
    else
    {
        Log::Comment(L"Machine is not domain-joined - replication operations may fail");
    }
    
    auto start = std::chrono::high_resolution_clock::now();
    HRESULT hr = agent.GetReplicationInformation(request, response);
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
    Log::Comment(String().Format(L"GetReplicationInformation completed in %lld ms with HRESULT: 0x%08X", duration, hr));
    
    // Log detailed error information regardless of domain membership
    if (FAILED(hr))
    {
        if (hr == 0x80072032)
        {
            // ERROR_DS_DRA_NAME_COLLISION (Win32 error 8242)
            Log::Comment(L"Error is ERROR_DS_DRA_NAME_COLLISION (0x80072032) - likely due to no domain controller access");
        }
        else if (HRESULT_CODE(hr) == ERROR_NO_SUCH_DOMAIN)
        {
            // Win32 error 1355
            Log::Comment(L"Error is ERROR_NO_SUCH_DOMAIN - no domain is available");
        }
        else if (HRESULT_CODE(hr) == RPC_S_SERVER_UNAVAILABLE)
        {
            // Win32 error 1722
            Log::Comment(L"Error is RPC_S_SERVER_UNAVAILABLE - cannot connect to DC via RPC");
        }
        else
        {
            // Log Win32 error code for better diagnostics
            Log::Comment(String().Format(L"Win32 error code: %d (0x%08X)", HRESULT_CODE(hr), HRESULT_CODE(hr)));
        }
    }

    // Different verification based on domain membership
    if (isInDomain)
    {
        // Even in domain environments, certain errors are expected in test environments
        // without proper AD infrastructure
        if (hr == 0x80072032 || // ERROR_DS_DRA_NAME_COLLISION
            HRESULT_CODE(hr) == ERROR_NO_SUCH_DOMAIN ||
            HRESULT_CODE(hr) == RPC_S_SERVER_UNAVAILABLE)
        {
            Log::Comment(L"Test environment appears to be domain-joined but cannot access domain controllers properly");
            Log::Comment(L"This is expected in certain test environments - marking test as passed");
        }
        else
        {
            // Only fail the test for unexpected errors
            VERIFY_IS_TRUE(SUCCEEDED(hr), String().Format(L"GetReplicationInformation should succeed in domain environment, got HRESULT: 0x%08X", hr));
        }
    }
    else
    {
        // In non-domain environment, don't fail the test - errors are expected
        Log::Comment(String().Format(L"HRESULT 0x%08X in non-domain environment - this is expected", hr));
        
        // Early return if the operation failed - we already logged appropriate information
        if (FAILED(hr))
        {
            // If we're in a domain but can't connect to DCs, or if we're not in a domain,
            // this is an expected error case in test environments
            Log::Comment(L"Test marked as passing - the failure is expected in this environment");
            Log::Comment(L"====== Finished TestGetReplicationStatusForIndividualDC ======");
            return;
        }
    }
    
    // Parse response
    // (No need to parse - already a JSON object)
    
    // Log result for debugging
    Log::Comment(String().Format(L"Response: %hs", response.dump().c_str()));
    
    // Verify response format
    VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
    Log::Comment(String().Format(L"Result field value: %hs",

    response[AimxConstants::JsonFields::AIMX_RESULT].get<std::string>().c_str()));
    // If in a domain environment with DC access, we would expect success
    if (response[AimxConstants::JsonFields::AIMX_RESULT].get<std::string>() == AimxConstants::JsonFields::AIMX_SUCCESS)
    {
        Log::Comment(L"Success response received - verifying structure");
        
        // Verify the response contains the ReplicationData field
        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_REPLICATION_DATA), L"Response should contain ReplicationData field");
        
        // Access the ReplicationData object
        auto& replData = response[AimxConstants::JsonFields::AIMX_REPLICATION_DATA];
        
        // Verify fields in the ReplicationData object
        VERIFY_IS_TRUE(replData.contains(AimxConstants::JsonFields::AIMX_DC_NAME), L"ReplicationData should contain DC_Name field");
        VERIFY_IS_TRUE(replData.contains(AimxConstants::JsonFields::AIMX_REPL_STATUS), L"ReplicationData should contain Repl_Status field");
        VERIFY_IS_TRUE(replData.contains(AimxConstants::JsonFields::AIMX_REPLICATION_NEIGHBORS), L"ReplicationData should contain Replication_Neighbors field");
        
        // Additional verification for replication status value
        if (replData.contains(AimxConstants::JsonFields::AIMX_REPL_STATUS)) 
        {
            Log::Comment(String().Format(L"Replication Status: %d", 
                                      replData[AimxConstants::JsonFields::AIMX_REPL_STATUS].get<int>()));
        }
        
        // Verify Replication_Neighbors array structure
        if (replData.contains(AimxConstants::JsonFields::AIMX_REPLICATION_NEIGHBORS) && replData[AimxConstants::JsonFields::AIMX_REPLICATION_NEIGHBORS].is_array()) 
        {
            size_t neighborCount = replData[AimxConstants::JsonFields::AIMX_REPLICATION_NEIGHBORS].size();
            Log::Comment(String().Format(L"Number of replication neighbors: %zu", neighborCount));
            
            // Check fields in first neighbor entry if available
            if (neighborCount > 0) 
            {
                Log::Comment(L"Examining first neighbor entry:");
                auto& firstNeighbor = replData[AimxConstants::JsonFields::AIMX_REPLICATION_NEIGHBORS][0];
                
                if (firstNeighbor.contains(AimxConstants::JsonFields::AIMX_FTIME_LAST_SYNC_SUCCESS)) 
                {
                    Log::Comment(String().Format(L"Last Sync Success: %hs", firstNeighbor[AimxConstants::JsonFields::AIMX_FTIME_LAST_SYNC_SUCCESS].get<std::string>().c_str()));
                }
                
                if (firstNeighbor.contains(AimxConstants::JsonFields::AIMX_FTIME_LAST_SYNC_ATTEMPT)) 
                {
                    Log::Comment(String().Format(L"Last Sync Attempt: %hs", firstNeighbor[AimxConstants::JsonFields::AIMX_FTIME_LAST_SYNC_ATTEMPT].get<std::string>().c_str()));
                }
                
                if (firstNeighbor.contains(AimxConstants::JsonFields::AIMX_CNUM_CONSECUTIVE_SYNC_FAILURES)) 
                {
                    Log::Comment(String().Format(L"Consecutive Sync Failures: %d", firstNeighbor[AimxConstants::JsonFields::AIMX_CNUM_CONSECUTIVE_SYNC_FAILURES].get<int>()));
                }
            }
        }
    }
    else
    {
        // On a non-domain machine, we would expect failure
        Log::Comment(L"Failure response received - this is expected on non-domain machines");
        
        // Verify error information if present
        if (response.contains(AimxConstants::JsonFields::AIMX_JSON_ERROR)) 
        {
            Log::Comment(String().Format(L"Error message: %hs", response[AimxConstants::JsonFields::AIMX_JSON_ERROR].get<std::string>().c_str()));
        }
    }
    
    Log::Comment(L"GetReplicationStatusForIndividualDC test completed");
    Log::Comment(L"====== Finished TestGetReplicationStatusForIndividualDC ======");
}

void ADReplicationToolAgentTests::TestGetReplicationStatusForAllDCs()
{
    Log::Comment(L"====== Starting TestGetReplicationStatusForAllDCs ======");
    Log::Comment(L"Testing GetReplicationStatusForAllDCs to verify domain-wide replication status retrieval");
    
    // Get instance of ADReplicationToolAgent - now inherits from ADToolAgent base class
    ADReplicationToolAgent& agent = ADReplicationToolAgent::GetADReplicationToolAgentInstance();
    Log::Comment(L"ADReplicationToolAgent instance obtained");        // Create request for all DCs
        nlohmann::json request;
        request[AimxConstants::JsonFields::AIMX_ACTION] = AimxConstants::ActionTypes::AIMX_GET_REPLICATION_STATUS_ALL_DCS;
        request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost"; // Use local machine for test
    
    Log::Comment(String().Format(L"Request created: %hs", request.dump().c_str()));
    
    nlohmann::json response;
    
    // Check if we're in a domain environment
    bool isInDomain = IsDomainJoined(agent);
    if (isInDomain)
    {
        Log::Comment(L"Machine is domain-joined - expecting success");
    } 
    else
    {
        Log::Comment(L"Machine is not domain-joined - replication operations may fail");
    }
    
    auto start = std::chrono::high_resolution_clock::now();
    HRESULT hr = agent.GetReplicationInformation(request, response);
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
    Log::Comment(String().Format(L"GetReplicationInformation completed in %lld ms with HRESULT: 0x%08X", duration, hr));
    
    // Log detailed error information regardless of domain membership
    if (FAILED(hr))
    {
        if (hr == 0x80072032)
        {
            // ERROR_DS_DRA_NAME_COLLISION (Win32 error 8242)
            Log::Comment(L"Error is ERROR_DS_DRA_NAME_COLLISION (0x80072032) - likely due to no domain controller access");
        }
        else if (HRESULT_CODE(hr) == ERROR_NO_SUCH_DOMAIN)
        {
            // Win32 error 1355
            Log::Comment(L"Error is ERROR_NO_SUCH_DOMAIN - no domain is available");
        }
        else if (HRESULT_CODE(hr) == RPC_S_SERVER_UNAVAILABLE)
        {
            // Win32 error 1722
            Log::Comment(L"Error is RPC_S_SERVER_UNAVAILABLE - cannot connect to DC via RPC");
        }
        else
        {
            // Log Win32 error code for better diagnostics
            Log::Comment(String().Format(L"Win32 error code: %d (0x%08X)", HRESULT_CODE(hr), HRESULT_CODE(hr)));
        }
    }

    // Different verification based on domain membership
    if (isInDomain)
    {
        // Even in domain environments, certain errors are expected in test environments
        // without proper AD infrastructure
        if (hr == 0x80072032 || // ERROR_DS_DRA_NAME_COLLISION
            HRESULT_CODE(hr) == ERROR_NO_SUCH_DOMAIN ||
            HRESULT_CODE(hr) == RPC_S_SERVER_UNAVAILABLE)
        {
            Log::Comment(L"Test environment appears to be domain-joined but cannot access domain controllers properly");
            Log::Comment(L"This is expected in certain test environments - marking test as passed");
        }
        else
        {
            // Only fail the test for unexpected errors
            VERIFY_IS_TRUE(SUCCEEDED(hr), String().Format(L"GetReplicationInformation should succeed in domain environment, got HRESULT: 0x%08X", hr));
        }
    } 
    else
    {
        // In non-domain environment, don't fail the test - errors are expected
        Log::Comment(String().Format(L"HRESULT 0x%08X in non-domain environment - this is expected", hr));
        
        // Early return if the operation failed - we already logged appropriate information
        if (FAILED(hr))
        {
            // If we're in a domain but can't connect to DCs, or if we're not in a domain,
            // this is an expected error case in test environments
            Log::Comment(L"Test marked as passing - the failure is expected in this environment");
            Log::Comment(L"====== Finished TestGetReplicationStatusForAllDCs ======");
            return;
        }
    }
    
    // Log result for debugging
    Log::Comment(String().Format(L"Response: %hs", response.dump().c_str()));
    
    // Verify response format
    VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
    Log::Comment(String().Format(L"Result field value: %hs", 
                              response[AimxConstants::JsonFields::AIMX_RESULT].get<std::string>().c_str()));
    
    // If in a domain environment with DC access, we would expect success
    if (response[AimxConstants::JsonFields::AIMX_RESULT].get<std::string>() == AimxConstants::JsonFields::AIMX_SUCCESS)
    {
        Log::Comment(L"Success response received - verifying structure");
        
        // Verify that DCs array exists
        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_REPLICATION_DATA), L"Response should contain DCs array");
        VERIFY_IS_TRUE(response[AimxConstants::JsonFields::AIMX_REPLICATION_DATA].is_array(), L"DCs should be an array");
        
        // Log the number of DCs found
        size_t dcCount = response[AimxConstants::JsonFields::AIMX_REPLICATION_DATA].size();
        Log::Comment(String().Format(L"Number of DCs found: %zu", dcCount));
        
        // If we have any DCs, verify their structure
        if (dcCount > 0) 
        {
            // Sample the first DC (or a few if available)
            int samplesToLog = min(3, static_cast<int>(dcCount));
            for (int i = 0; i < samplesToLog; i++) 
            {
                auto& dc = response[AimxConstants::JsonFields::AIMX_REPLICATION_DATA][i];
                Log::Comment(String().Format(L"DC %d:", i));
                
                // Verify expected fields for each DC
                if (dc.contains(AimxConstants::JsonFields::AIMX_DC_NAME)) 
                {
                    Log::Comment(String().Format(L"  DC Name: %hs", 
                        dc[AimxConstants::JsonFields::AIMX_DC_NAME].get<std::string>().c_str()));
                } 
                else 
                {
                    Log::Comment(L"  DC_Name field missing");
                }
                
                if (dc.contains(AimxConstants::JsonFields::AIMX_REPL_STATUS)) 
                {
                    Log::Comment(String().Format(L"  Replication Status: %d", dc[AimxConstants::JsonFields::AIMX_REPL_STATUS].get<int>()));
                } 
                else
                {
                    Log::Comment(L"  Repl_Status field missing");
                }
                
                // Verify Replication_Neighbors array if present
                if (dc.contains(AimxConstants::JsonFields::AIMX_REPLICATION_NEIGHBORS) && dc[AimxConstants::JsonFields::AIMX_REPLICATION_NEIGHBORS].is_array()) 
                {
                    size_t neighborCount = dc[AimxConstants::JsonFields::AIMX_REPLICATION_NEIGHBORS].size();
                    Log::Comment(String().Format(L"  Number of replication neighbors: %zu", neighborCount));
                    
                    // Sample some entries for logging
                    int neighborsToLog = min(2, static_cast<int>(neighborCount));
                    for (int j = 0; j < neighborsToLog; j++) 
                    {
                        Log::Comment(String().Format(L"  Neighbor %d:", j));
                        
                        auto& neighbor = dc[AimxConstants::JsonFields::AIMX_REPLICATION_NEIGHBORS][j];
                        
                        if (neighbor.contains(AimxConstants::JsonFields::AIMX_FTIME_LAST_SYNC_SUCCESS)) 
                        {
                            Log::Comment(String().Format(L"    Last Sync Success: %hs", 
                                neighbor[AimxConstants::JsonFields::AIMX_FTIME_LAST_SYNC_SUCCESS].get<std::string>().c_str()));
                        }
                        
                        if (neighbor.contains(AimxConstants::JsonFields::AIMX_CNUM_CONSECUTIVE_SYNC_FAILURES)) 
                        {
                            Log::Comment(String().Format(L"    Consecutive Failures: %d", 
                                neighbor[AimxConstants::JsonFields::AIMX_CNUM_CONSECUTIVE_SYNC_FAILURES].get<int>()));
                        }
                    }
                } 
                else
                {
                    Log::Comment(L"  Replication_Neighbors field missing or not an array");
                }
            }
        } 
        else
        {
            Log::Comment(L"No DCs found in the response");
        }
    }
    else
    {
        // On a non-domain machine, we would expect failure
        Log::Comment(L"Failure response received - this is expected on non-domain machines");
        
        // Check for error details
        if (response.contains(AimxConstants::JsonFields::AIMX_JSON_ERROR)) 
        {
            Log::Comment(String().Format(L"Error message: %hs", response[AimxConstants::JsonFields::AIMX_JSON_ERROR].get<std::string>().c_str()));
        }
    }
    
    Log::Comment(L"GetReplicationStatusForAllDCs test completed");
    Log::Comment(L"====== Finished TestGetReplicationStatusForAllDCs ======");
}

void ADReplicationToolAgentTests::TestErrorHandling()
{
    Log::Comment(L"====== Starting TestErrorHandling ======");
    Log::Comment(L"Testing error handling in ADReplicationToolAgent to verify robust error management");
    
    ADReplicationToolAgent& agent = ADReplicationToolAgent::GetADReplicationToolAgentInstance();
    Log::Comment(L"ADReplicationToolAgent instance obtained");
    
    // Test case 1: Invalid DC name
    {
        Log::Comment(L"Test case 1: Invalid DC name");
        
        nlohmann::json request;
        request[AimxConstants::JsonFields::AIMX_ACTION] = AimxConstants::ActionTypes::AIMX_GET_REPLICATION_STATUS_INDIVIDUAL_DC;
        request[AimxConstants::JsonFields::AIMX_DC_NAME] = "non_existent_dc_name";
        
        Log::Comment(String().Format(L"Request created with invalid DC name: %hs", request.dump().c_str()));
        
        nlohmann::json response;
        
        HRESULT hr = agent.GetReplicationInformation(request, response);
        Log::Comment(String().Format(L"Response received with HRESULT: 0x%08X", hr));
        Log::Comment(String().Format(L"Response content: %hs", response.dump().c_str()));
        
        // For error cases, we expect an error HRESULT
        VERIFY_IS_FALSE(SUCCEEDED(hr), L"GetReplicationInformation should return error HRESULT for invalid DC name");
        
        // We expect this to fail since the DC doesn't exist
        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");

        std::string result = response[AimxConstants::JsonFields::AIMX_RESULT].get<std::string>();
        Log::Comment(String().Format(L"Result field: %hs", result.c_str()));
        
        // Verify appropriate error information is included
        if (result == AimxConstants::JsonFields::AIMX_FAILURE)
        {
            Log::Comment(L"Correctly received failure result for non-existent DC");
            
            if (response.contains(AimxConstants::JsonFields::AIMX_JSON_ERROR)) 
            {
                Log::Comment(String().Format(L"Error message: %hs", response[AimxConstants::JsonFields::AIMX_JSON_ERROR].get<std::string>().c_str()));
            } 
            else
            {
                Log::Comment(L"WARNING: No Error field found in response for invalid DC");
            }
        }
    }
    
    // Test case 2: DNS resolution failure
    {
        Log::Comment(L"Test case 2: DNS resolution failure");
        
        nlohmann::json request;
        request[AimxConstants::JsonFields::AIMX_ACTION] = AimxConstants::ActionTypes::AIMX_GET_REPLICATION_STATUS_INDIVIDUAL_DC;
        request[AimxConstants::JsonFields::AIMX_DC_NAME] = "dc.invalid.domain.that.should.not.resolve";
        
        Log::Comment(String().Format(L"Request created with unresolvable domain: %hs", request.dump().c_str()));
        
        nlohmann::json response;
        
        HRESULT hr = agent.GetReplicationInformation(request, response);
        Log::Comment(String().Format(L"Response received with HRESULT: 0x%08X", hr));
        Log::Comment(String().Format(L"Response content: %hs", response.dump().c_str()));
        
        // We expect this to fail with an error HRESULT since the domain shouldn't resolve
        VERIFY_IS_FALSE(SUCCEEDED(hr), L"GetReplicationInformation should return error HRESULT for DNS resolution failure");
        
        // We expect this to fail since the domain shouldn't resolve
        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
        
        if (response[AimxConstants::JsonFields::AIMX_RESULT] == AimxConstants::JsonFields::AIMX_FAILURE) 
        {
            Log::Comment(L"Correctly received failure result for DNS resolution failure");
        }
    }
    
    // Test case 3: Missing DC_Name
    {
        Log::Comment(L"Test case 3: Missing DC_Name field");
        
        nlohmann::json request;
        request[AimxConstants::JsonFields::AIMX_ACTION] = AimxConstants::ActionTypes::AIMX_GET_REPLICATION_STATUS_INDIVIDUAL_DC;
        
        // Deliberately omit DC_Name
        Log::Comment(String().Format(L"Request created without DC_Name: %hs", request.dump().c_str()));
        
        nlohmann::json response;
        
        HRESULT hr = agent.GetReplicationInformation(request, response);
        Log::Comment(String().Format(L"Response received with HRESULT: 0x%08X", hr));
        Log::Comment(String().Format(L"Response received: %hs", response.dump().c_str()));
        
        // For error cases, we expect an error HRESULT
        VERIFY_IS_FALSE(SUCCEEDED(hr), L"GetReplicationInformation should return error HRESULT for missing DC_Name");

        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
        VERIFY_IS_TRUE(response[AimxConstants::JsonFields::AIMX_RESULT] == AimxConstants::JsonFields::AIMX_FAILURE, L"Result should be Failure for missing DC_Name");
    }
    
    Log::Comment(L"Error handling tests completed");
    Log::Comment(L"====== Finished TestErrorHandling ======");
}

void ADReplicationToolAgentTests::TestPerformance()
{
    Log::Comment(L"====== Starting TestPerformance ======");
    Log::Comment(L"Testing performance of ADReplicationToolAgent");
    
    ADReplicationToolAgent& agent = ADReplicationToolAgent::GetADReplicationToolAgentInstance();
    
    // Create request for individual DC
    nlohmann::json request;
    request[AimxConstants::JsonFields::AIMX_ACTION] = AimxConstants::ActionTypes::AIMX_GET_REPLICATION_STATUS_INDIVIDUAL_DC;
    request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost"; // Use local machine for test
    
    nlohmann::json response;
    
    const int NUM_ITERATIONS = 5;
    std::vector<long long> durations;
    
    Log::Comment(String().Format(L"Running performance test with %d iterations", NUM_ITERATIONS));
    
    // Perform multiple iterations of the operation and measure time
    for (int i = 0; i < NUM_ITERATIONS; i++) 
    {
        auto start = std::chrono::high_resolution_clock::now();
        
        agent.GetReplicationInformation(request, response);
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
        
        durations.push_back(duration);
        Log::Comment(String().Format(L"Iteration %d completed in %lld ms", i + 1, duration));
    }
    
    // Calculate average, min, and max durations
    long long totalDuration = 0;
    long long minDuration = LLONG_MAX;
    long long maxDuration = 0;
    
    for (long long duration : durations) 
    {
        totalDuration += duration;
        minDuration = min(minDuration, duration);
        maxDuration = max(maxDuration, duration);
    }
    
    long long avgDuration = totalDuration / NUM_ITERATIONS;
    
    Log::Comment(String().Format(L"Performance results:"));
    Log::Comment(String().Format(L"  Average duration: %lld ms", avgDuration));
    Log::Comment(String().Format(L"  Minimum duration: %lld ms", minDuration));
    Log::Comment(String().Format(L"  Maximum duration: %lld ms", maxDuration));
    
    // Verify that the average performance is within acceptable limits
    // Adjust the threshold based on expected performance in your environment
    const long long ACCEPTABLE_THRESHOLD_MS = 5000; // 5 seconds
    
    VERIFY_IS_LESS_THAN(avgDuration, ACCEPTABLE_THRESHOLD_MS, 
                       L"Average operation time should be less than threshold");
    
    Log::Comment(L"Performance test completed");
    Log::Comment(L"====== Finished TestPerformance ======");
}

void ADReplicationToolAgentTests::TestInputValidation()
{
    Log::Comment(L"====== Starting TestInputValidation ======");
    Log::Comment(L"Testing input validation in ADReplicationToolAgent to verify proper handling of edge cases");
    
    ADReplicationToolAgent& agent = ADReplicationToolAgent::GetADReplicationToolAgentInstance();
    Log::Comment(L"ADReplicationToolAgent instance obtained");
    
    // Test case 1: Empty input
    {
        Log::Comment(L"Test case 1: Empty input");
        
        nlohmann::json emptyRequest; // Create an empty JSON object
        nlohmann::json response;
        
        HRESULT hr = agent.GetReplicationInformation(emptyRequest, response);
        
        Log::Comment(String().Format(L"Response from empty input: %hs", response.dump().c_str()));
        
        // No need to parse - already a JSON object
        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
        VERIFY_IS_TRUE(response[AimxConstants::JsonFields::AIMX_RESULT] == AimxConstants::JsonFields::AIMX_FAILURE, L"Empty input should result in failure");
        
        if (response.contains(AimxConstants::JsonFields::AIMX_JSON_ERROR)) 
        {
            Log::Comment(String().Format(L"Error message: %hs", 
                response[AimxConstants::JsonFields::AIMX_JSON_ERROR].get<std::string>().c_str()));
        }
    }
    
    // Test case 2: Very large DC name
    {
        Log::Comment(L"Test case 2: Very large DC name");
        
        std::string veryLargeDCName(10000, 'x'); // 10KB string
        
        nlohmann::json request;
        request[AimxConstants::JsonFields::AIMX_ACTION] = AimxConstants::ActionTypes::AIMX_GET_REPLICATION_STATUS_INDIVIDUAL_DC;
        request[AimxConstants::JsonFields::AIMX_DC_NAME] = veryLargeDCName;
        
        Log::Comment(L"Created request with 10KB DC name");
        
        nlohmann::json response;
        auto start = std::chrono::high_resolution_clock::now();
        HRESULT hr = agent.GetReplicationInformation(request, response);
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
        
        Log::Comment(String().Format(L"Request processed in %lld ms with HRESULT: 0x%08X", duration, hr));
        Log::Comment(L"Response received for very large DC name (first 100 chars):");
        
        std::string responseStr = response.dump();
        if (responseStr.length() > 100)
        {
            Log::Comment(String().Format(L"%hs...", responseStr.substr(0, 100).c_str()));
        }
        else
        {
            Log::Comment(String().Format(L"%hs", responseStr.c_str()));
        }
        
        // Should handle large input without crashing
        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
        Log::Comment(String().Format(L"Result: %hs", 
            response[AimxConstants::JsonFields::AIMX_RESULT].get<std::string>().c_str()));
    }
    
    // Test case 3: Unicode characters in DC name
    {
        Log::Comment(L"Test case 3: Unicode characters in DC name");
        
        nlohmann::json request;
        request[AimxConstants::JsonFields::AIMX_ACTION] = AimxConstants::ActionTypes::AIMX_GET_REPLICATION_STATUS_INDIVIDUAL_DC;
        request[AimxConstants::JsonFields::AIMX_DC_NAME] = "öäüÖÄÜß-उनिकोड-테스트"; // Unicode characters
        
        Log::Comment(String().Format(L"Request with Unicode DC name: %hs", request.dump().c_str()));
        
        nlohmann::json response;
        
        HRESULT hr = agent.GetReplicationInformation(request, response);
        
        Log::Comment(String().Format(L"Response received for Unicode DC name with HRESULT: 0x%08X", hr));
        Log::Comment(String().Format(L"Response: %hs", response.dump().c_str()));
        
        // Should gracefully handle Unicode, even if it fails to connect
        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
        Log::Comment(String().Format(L"Result for Unicode test: %hs", 
            response[AimxConstants::JsonFields::AIMX_RESULT].get<std::string>().c_str()));
    }
    
    // Test case 4: Testing for SQL injection style input
    {
        Log::Comment(L"Test case 4: SQL injection style input");
        
        nlohmann::json request;
        request[AimxConstants::JsonFields::AIMX_ACTION] = AimxConstants::ActionTypes::AIMX_GET_REPLICATION_STATUS_INDIVIDUAL_DC;
        request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost'; DROP TABLE users; --";
        
        Log::Comment(String().Format(L"Request with SQL injection input: %hs", request.dump().c_str()));
        
        nlohmann::json response;
        
        HRESULT hr = agent.GetReplicationInformation(request, response);
        
        Log::Comment(String().Format(L"Response received for SQL injection style input with HRESULT: 0x%08X", hr));
        Log::Comment(String().Format(L"Response: %hs", response.dump().c_str()));
        
        // Should handle malicious input gracefully
        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
    }
    
    // Test case 5: Testing API action case sensitivity
    {
        Log::Comment(L"Test case 5: Action name case sensitivity test");
        
        nlohmann::json request;
        request[AimxConstants::JsonFields::AIMX_ACTION] = "getreplicationstatusforindividualdc"; // lowercase
        request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";
        
        Log::Comment(String().Format(L"Request with lowercase action: %hs", request.dump().c_str()));
        
        nlohmann::json response;
        
        HRESULT hr = agent.GetReplicationInformation(request, response);
        
        Log::Comment(String().Format(L"Response received for lowercase action with HRESULT: 0x%08X", hr));
        Log::Comment(String().Format(L"Response: %hs", response.dump().c_str()));
        
        // Validate whether case matters for action names
        if (response.contains(AimxConstants::JsonFields::AIMX_RESULT)) {
            std::string result = response[AimxConstants::JsonFields::AIMX_RESULT].get<std::string>();
            
            if (result == AimxConstants::JsonFields::AIMX_FAILURE)
            {
                Log::Comment(L"API confirmed to be case-sensitive for action names");
            }
            else
            {
                Log::Comment(L"API appears to be case-insensitive for action names");
            }
        }
    }
    
    Log::Comment(L"Input validation tests completed");
    Log::Comment(L"====== Finished TestInputValidation ======");
}

void ADReplicationToolAgentTests::TestConcurrentAccess()
{
    // Use a mutex to synchronize access to Log::Comment to avoid race conditions
    std::mutex logMutex;
    auto threadSafeLog = [&logMutex](const String& message) {
        std::lock_guard<std::mutex> lock(logMutex);
        Log::Comment(message);
    };

    threadSafeLog(L"====== Starting TestConcurrentAccess ======");
    threadSafeLog(L"Testing thread safety of ADReplicationToolAgent with concurrent requests");
    
    const int NUM_THREADS = 5;
    std::atomic<int> successCount(0);
    std::vector<std::thread> threads;
    
    // Use a barrier to ensure all threads start approximately at the same time
    // to increase likelihood of concurrency issues being exposed
    std::atomic<bool> startBarrier(false);
    std::atomic<int> threadsReady(0);
    
    threadSafeLog(String().Format(L"Launching %d concurrent threads", NUM_THREADS));
    
    // Create and start multiple threads with thread-safe logging
    for (int i = 0; i < NUM_THREADS; i++) 
    {
        // Use a copy of i instead of reference to avoid capture issues
        threads.push_back(std::thread([&successCount, &threadSafeLog, &startBarrier, &threadsReady, threadId = i]() 
        {
            // Thread-local JSON objects to avoid shared memory access
            nlohmann::json localRequest;
            nlohmann::json localResponse;
            
            // Prepare request before signaling ready
            localRequest[AimxConstants::JsonFields::AIMX_ACTION] = AimxConstants::ActionTypes::AIMX_GET_REPLICATION_STATUS_INDIVIDUAL_DC;
            localRequest[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";
            localRequest["ThreadID"] = threadId;
            
            // Signal that this thread is ready and wait for all threads
            threadsReady++;
            threadSafeLog(String().Format(L"Thread %d ready", threadId));
            
            // Wait for all threads to be ready before starting
            while (!startBarrier.load()) {
                // Short sleep to avoid CPU spinning
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }
            
            threadSafeLog(String().Format(L"Thread %d started work", threadId));
            
            // Isolate ADReplicationToolAgent instance access within try-catch
            try
            {
                // Get ADReplicationToolAgent instance in each thread
                ADReplicationToolAgent& agent = ADReplicationToolAgent::GetADReplicationToolAgentInstance();
                
                // Execute request with timeout to prevent hanging
                HRESULT hr = S_OK;
                
                // Use a future with timeout to prevent potential deadlocks
                std::packaged_task<HRESULT()> task([&agent, &localRequest, &localResponse]() {
                    return agent.GetReplicationInformation(localRequest, localResponse);
                });
                
                std::future<HRESULT> future = task.get_future();
                std::thread taskThread(std::move(task));
                
                // Wait for the task to complete with a timeout
                if (future.wait_for(std::chrono::seconds(5)) == std::future_status::ready) {
                    hr = future.get();
                    
                    // Verify response after successful execution
                    if (localResponse.contains(AimxConstants::JsonFields::AIMX_RESULT) && SUCCEEDED(hr))
                    {
                        // Count as success if we got a valid response with Result field and success HRESULT
                        successCount++;
                        threadSafeLog(String().Format(L"Thread %d completed successfully with HRESULT: 0x%08X", threadId, hr));
                    }
                    else
                    {
                        threadSafeLog(String().Format(L"Thread %d completed with unsuccessful HRESULT: 0x%08X", threadId, hr));
                    }
                } 
                else 
                {
                    // Timeout occurred, consider it a failure
                    threadSafeLog(String().Format(L"Thread %d timed out after 5 seconds", threadId));
                    
                    // Detach the thread that might be stuck (this is better than terminating)
                    taskThread.detach();
                }
                
                // If thread is still joinable, join it
                if (taskThread.joinable()) {
                    taskThread.join();
                }
            }
            catch (const std::exception& e)
            {
                // Log any exceptions that occur during execution
                threadSafeLog(String().Format(L"Thread %d encountered exception: %S", threadId, e.what()));
            }
            catch (...)
            {
                // Catch any other exceptions
                threadSafeLog(String().Format(L"Thread %d encountered unknown exception", threadId));
            }
        }));
    }
    
    // Wait until all threads are ready
    while (threadsReady.load() < NUM_THREADS) {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // Start all threads simultaneously
    threadSafeLog(L"All threads ready, starting execution");
    startBarrier.store(true);
    
    // Wait for all threads to complete
    threadSafeLog(L"Waiting for all threads to complete...");
    for (auto& thread : threads) 
    {
        if (thread.joinable()) 
        {
            thread.join();
        }
    }
    
    // Verify results
    threadSafeLog(String().Format(L"All threads completed. Success count: %d/%d", 
                              successCount.load(), NUM_THREADS));
    
    // We should have at least some successful responses
    VERIFY_IS_GREATER_THAN(successCount.load(), 0, 
                         L"At least some concurrent requests should succeed");
    
    threadSafeLog(L"Thread safety test completed");
    threadSafeLog(L"====== Finished TestConcurrentAccess ======");
}

void ADReplicationToolAgentTests::TestGetDCNameAndRunRepadmin()
{
    Log::Comment(L"====== Starting TestGetDCNameAndRunRepadmin ======");
    Log::Comment(L"Testing getting an actual DC name and running repadmin command on it");
    
    // Get instance of ADReplicationToolAgent - now inherits from ADToolAgent with DC connection functionality
    ADReplicationToolAgent& replicationAgent = ADReplicationToolAgent::GetADReplicationToolAgentInstance();
    Log::Comment(L"ADReplicationToolAgent instance obtained");
    
    // Use a simpler approach to get a DC name
    std::wstring dcNameOutput = L"localhost"; // Default to localhost
    
    // First try a simple command to check if we're in a domain
    std::wstring checkDomainOutput;
    bool inDomain = false;
    
    bool checkResult = replicationAgent.RunCommand(
        L"powershell.exe -Command \"(Get-WmiObject -Class Win32_ComputerSystem).PartOfDomain\"", 
        checkDomainOutput);
    
    // Trim whitespace from the output
    checkDomainOutput.erase(0, checkDomainOutput.find_first_not_of(L" \t\n\r\f\v"));
    checkDomainOutput.erase(checkDomainOutput.find_last_not_of(L" \t\n\r\f\v") + 1);
    
    if (checkResult && !checkDomainOutput.empty() && 
        (checkDomainOutput == L"True" || checkDomainOutput == L"true")) 
    {
        inDomain = true;
        Log::Comment(L"Computer is part of a domain, attempting to get domain controller name");
        
        // Try to get primary domain controller with simple PowerShell
        std::wstring dcOutput;
        bool dcResult = replicationAgent.RunCommand(
            L"powershell.exe -Command \"[System.DirectoryServices.ActiveDirectory.Domain]::GetCurrentDomain().PdcRoleOwner.Name\"", 
            dcOutput);
        
        // Trim whitespace from the output
        dcOutput.erase(0, dcOutput.find_first_not_of(L" \t\n\r\f\v"));
        dcOutput.erase(dcOutput.find_last_not_of(L" \t\n\r\f\v") + 1);
        
        if (dcResult && !dcOutput.empty() && dcOutput.find(L"Exception") == std::wstring::npos) 
        {
            dcNameOutput = dcOutput;
            Log::Comment(String().Format(L"Successfully found Domain Controller: %s", dcNameOutput.c_str()));
        } 
        else 
        {
            Log::Comment(L"Failed to get domain controller with .NET approach, trying NetAPI approach");
            
            // Try another method using NetAPI
            std::wstring netApiDcOutput;
            bool netApiResult = replicationAgent.RunCommand(
                L"powershell.exe -Command \"$null = [System.Reflection.Assembly]::LoadWithPartialName('System.DirectoryServices.AccountManagement'); $ct = [System.DirectoryServices.AccountManagement.ContextType]::Domain; $pc = New-Object System.DirectoryServices.AccountManagement.PrincipalContext($ct); $pc.ConnectedServer\"", 
                netApiDcOutput);
            
            // Trim whitespace from the output
            netApiDcOutput.erase(0, netApiDcOutput.find_first_not_of(L" \t\n\r\f\v"));
            netApiDcOutput.erase(netApiDcOutput.find_last_not_of(L" \t\n\r\f\v") + 1);
            
            if (netApiResult && !netApiDcOutput.empty() && netApiDcOutput.find(L"Exception") == std::wstring::npos) 
            {
                dcNameOutput = netApiDcOutput;
                Log::Comment(String().Format(L"Successfully found Domain Controller using NetAPI: %s", dcNameOutput.c_str()));
            } 
            else 
            {
                Log::Warning(L"Failed to get DC name with NetAPI, falling back to localhost");
            }
        }
    } 
    else 
    {
        Log::Warning(L"Computer is not part of a domain, using 'localhost'");
    }
    
    // Get replication information using GetReplicationInformation first
    {
        Log::Comment(L"Testing replication information retrieval for the DC");
        
        nlohmann::json request;
        request[AimxConstants::JsonFields::AIMX_ACTION] = AimxConstants::ActionTypes::AIMX_GET_REPLICATION_STATUS_INDIVIDUAL_DC;
        
        // Convert wstring to string for the DC name
        std::string dcNameStr(dcNameOutput.begin(), dcNameOutput.end());
        request[AimxConstants::JsonFields::AIMX_DC_NAME] = dcNameStr;
        
        Log::Comment(String().Format(L"Request created: %hs", request.dump().c_str()));
        
        nlohmann::json response;
        
        HRESULT hr = replicationAgent.GetReplicationInformation(request, response);
        Log::Comment(String().Format(L"Replication information received with HRESULT: 0x%08X", hr));
        Log::Comment(String().Format(L"Response size: %zu fields", response.size()));
        
        // In a non-domain environment, we expect this to fail or return a specific error
        // We'll check if we're in a domain first
        if (!inDomain) 
        {
            Log::Warning(L"Not in a domain environment, skipping HRESULT verification");
        } 
        else 
        {
            // Only verify success when in a domain
            VERIFY_IS_TRUE(SUCCEEDED(hr), String().Format(L"GetReplicationInformation should succeed, got HRESULT: 0x%08X", hr));
        }
        
        // No need to parse - already a JSON object
        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");

        if (response[AimxConstants::JsonFields::AIMX_RESULT] == AimxConstants::JsonFields::AIMX_SUCCESS)
        {
            Log::Comment(L"Successfully retrieved replication information using ADReplicationToolAgent");

            if (response.contains(AimxConstants::JsonFields::AIMX_DC_NAME)) 
            {
                Log::Comment(String().Format(L"DC Name from response: %hs", 
                    response[AimxConstants::JsonFields::AIMX_DC_NAME].get<std::string>().c_str()));
            }
        } 
        else
        {
            Log::Warning(L"Failed to get replication info, this is expected if not in a domain environment");
        }
    }
    
    // Now run repadmin command on the same DC - only if we're in a domain
    if (inDomain)
    {
        Log::Comment(L"Now testing repadmin command execution directly on the DC");
        
        std::wstring repadminOutput;
        // Use a more reliable way to run repadmin
        std::wstring repadminCommand = L"powershell.exe -Command \"if (Get-Command repadmin.exe -ErrorAction SilentlyContinue) { repadmin.exe /showrepl '" + dcNameOutput + L"' } else { Write-Output 'Repadmin command not available on this system' }\"";
        
        Log::Comment(String().Format(L"Executing command: %s", repadminCommand.c_str()));
        
        // Using RunCommand from the replicationAgent (which is derived from ADToolAgent)
        bool cmdResult = replicationAgent.RunCommand(repadminCommand, repadminOutput);
        
        VERIFY_IS_TRUE(cmdResult, L"RunCommand should succeed for repadmin command");
        VERIFY_IS_FALSE(repadminOutput.empty(), L"Output should not be empty");
        
        // Check if the output indicates success
        if (repadminOutput.find(L"not available") != std::wstring::npos || 
            repadminOutput.find(L"Command failed") != std::wstring::npos) 
        {
            Log::Warning(L"Repadmin command failed, this is expected if repadmin is not available");
        } 
        else
        {
            Log::Comment(L"Repadmin command executed successfully");
            
            // Log a sample of the output (first 200 characters or less)
            size_t sampleLength = min(200, static_cast<int>(repadminOutput.length()));
            std::wstring outputSample = repadminOutput.substr(0, sampleLength);
            Log::Comment(String().Format(L"Repadmin output sample: %s%s", 
                outputSample.c_str(), 
                repadminOutput.length() > sampleLength ? L"..." : L""));
            
            // Check for specific patterns in the output
            if (repadminOutput.find(L"DS Replication Status") != std::wstring::npos) 
            {
                Log::Comment(L"Output contains 'DS Replication Status'");
            }
            
            if (repadminOutput.find(dcNameOutput) != std::wstring::npos)
            {
                Log::Comment(String().Format(L"Output references the specified DC: %s", dcNameOutput.c_str()));
            }
        }
        
        // Try a more targeted repadmin command that shows replication summary
        Log::Comment(L"Testing repadmin /replsummary command");
        
        std::wstring replSummaryOutput;
        std::wstring replSummaryCommand = L"powershell.exe -Command \"if (Get-Command repadmin.exe -ErrorAction SilentlyContinue) { repadmin.exe /replsummary '" + dcNameOutput + L"' } else { Write-Output 'Repadmin command not available on this system' }\"";
        
        Log::Comment(String().Format(L"Executing command: %s", replSummaryCommand.c_str()));
        
        // Using RunCommand from the replicationAgent (which is derived from ADToolAgent)
        bool summaryResult = replicationAgent.RunCommand(replSummaryCommand, replSummaryOutput);
        
        VERIFY_IS_TRUE(summaryResult, L"RunCommand should succeed for repadmin /replsummary command");
        VERIFY_IS_FALSE(replSummaryOutput.empty(), L"Output should not be empty");
        
        // Check if the output indicates success
        if (replSummaryOutput.find(L"not available") != std::wstring::npos || 
            replSummaryOutput.find(L"Command failed") != std::wstring::npos) 
        {
            Log::Warning(L"Repadmin /replsummary command failed, this is expected if repadmin is not available");
        }
        else
        {
            Log::Comment(L"Repadmin /replsummary command executed successfully");
            
            // Log the output (it's usually more concise than /showrepl)
            Log::Comment(String().Format(L"Repadmin /replsummary output: %s", replSummaryOutput.c_str()));
        }
    }
    else
    {
        Log::Comment(L"Skipping repadmin tests since the computer is not part of a domain");
    }
    
    Log::Comment(L"Test complete - compared GetReplicationInformation with direct repadmin commands");
    Log::Comment(L"====== Finished TestGetDCNameAndRunRepadmin ======");
}

void ADReplicationToolAgentTests::TestMemoryManagement()
{
    Log::Comment(L"====== Starting TestMemoryManagement ======");
    Log::Comment(L"Testing memory management in ADReplicationToolAgent");
    
    // Get initial memory usage
    PROCESS_MEMORY_COUNTERS pmc1;
    BOOL result1 = GetProcessMemoryInfo(GetCurrentProcess(), &pmc1, sizeof(pmc1));
    
    if (result1)
    {
        Log::Comment(String().Format(L"Initial working set size: %lld bytes", pmc1.WorkingSetSize));
        Log::Comment(String().Format(L"Initial page file usage: %lld bytes", pmc1.PagefileUsage));
    }
    else
    {
        Log::Comment(L"Failed to get initial memory info");
    }
    
    // Run multiple replication queries in a loop to potentially trigger memory leaks
    const int NUM_ITERATIONS = 20;
    
    Log::Comment(String().Format(L"Running %d iterations of replication queries", NUM_ITERATIONS));
    
    for (int i = 0; i < NUM_ITERATIONS; i++)
    {
        // Create and immediately destroy an instance of replication data
        {
            // Get the agent instance
            ADReplicationToolAgent& agent = ADReplicationToolAgent::GetADReplicationToolAgentInstance();
            
            // Create request for individual DC
            nlohmann::json request;
            request[AimxConstants::JsonFields::AIMX_ACTION] = AimxConstants::ActionTypes::AIMX_GET_REPLICATION_STATUS_INDIVIDUAL_DC;
            request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";
            
            nlohmann::json response;
            
            // Execute the request
            HRESULT hr = agent.GetReplicationInformation(request, response);
            
            // We don't check the result as this is just for memory leak testing
            
            // Let the response go out of scope
        }
        
        if (i % 5 == 0)
        {
            Log::Comment(String().Format(L"Completed %d iterations", i));
        }
    }
    
    // Force garbage collection if possible
    Log::Comment(L"Forcing garbage collection");
    SetProcessWorkingSetSize(GetCurrentProcess(), (SIZE_T)-1, (SIZE_T)-1);
    
    // Get final memory usage
    PROCESS_MEMORY_COUNTERS pmc2;
    BOOL result2 = GetProcessMemoryInfo(GetCurrentProcess(), &pmc2, sizeof(pmc2));
    
    if (result1 && result2)
    {
        long long workingSetDiff = (long long)pmc2.WorkingSetSize - (long long)pmc1.WorkingSetSize;
        long long pagefileDiff = (long long)pmc2.PagefileUsage - (long long)pmc1.PagefileUsage;
        
        Log::Comment(String().Format(L"Final working set size: %lld bytes", pmc2.WorkingSetSize));
        Log::Comment(String().Format(L"Final page file usage: %lld bytes", pmc2.PagefileUsage));
        Log::Comment(String().Format(L"Working set difference: %lld bytes", workingSetDiff));
        Log::Comment(String().Format(L"Page file difference: %lld bytes", pagefileDiff));
        
        // Check for significant memory increase
        // Note: This is a rough check - some increase is normal due to caching
        const long long ACCEPTABLE_INCREASE = 5 * 1024 * 1024; // 5 MB
        
        if (pagefileDiff > ACCEPTABLE_INCREASE)
        {
            Log::Warning(String().Format(
                L"Significant memory increase detected: %lld bytes. Possible memory leak.", 
                pagefileDiff));
        }
        else
        {
            Log::Comment(L"No significant memory increase detected");
        }
    }
    else
    {
        Log::Comment(L"Failed to get memory info for comparison");
    }
    
    Log::Comment(L"Memory management test completed");
    Log::Comment(L"====== Finished TestMemoryManagement ======");
}

void ADReplicationToolAgentTests::TestTimeoutHandling()
{
    Log::Comment(L"====== Starting TestTimeoutHandling ======");
    Log::Comment(L"Testing timeout handling in ADReplicationToolAgent");
    
    ADReplicationToolAgent& agent = ADReplicationToolAgent::GetADReplicationToolAgentInstance();
    Log::Comment(L"ADReplicationToolAgent instance obtained");
    
    // Test case 1: Very slow or non-responsive DC
    {
        Log::Comment(L"Test case 1: Testing with a non-responsive DC");
        
        try
        {
            // Use a hostname that will cause DNS resolution to timeout
            // This is typically an invalid hostname on a non-routable network
            nlohmann::json request;
            request[AimxConstants::JsonFields::AIMX_ACTION] = AimxConstants::ActionTypes::AIMX_GET_REPLICATION_STATUS_INDIVIDUAL_DC;
            request[AimxConstants::JsonFields::AIMX_DC_NAME] = "very-slow-dc.non-existent-domain.local";
            
            std::string requestStr = request.dump();
            Log::Comment(String().Format(L"Request created with slow DC name: %hs", requestStr.c_str()));
            
            nlohmann::json response;
            
            // Start measuring time for the potentially slow operation
            auto start = std::chrono::high_resolution_clock::now();
            
            HRESULT hr = agent.GetReplicationInformation(request, response);
            
            auto end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
            
            Log::Comment(String().Format(L"Operation completed in %lld ms with HRESULT: 0x%08X", duration, hr));
            Log::Comment(String().Format(L"Response received: %hs", response.dump().c_str()));
            
            // Verify that we get a reasonable response even for potentially timed-out operations
            // No need to parse - already a JSON object

            VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");

            if (response[AimxConstants::JsonFields::AIMX_RESULT] == AimxConstants::JsonFields::AIMX_FAILURE) 
            {
                Log::Comment(L"Correctly received failure result for slow/timed-out operation");
                
                // Check if error message indicates a timeout
                if (response.contains(AimxConstants::JsonFields::AIMX_JSON_ERROR))
                {
                    std::string errorMsg = response[AimxConstants::JsonFields::AIMX_JSON_ERROR].get<std::string>();
                    Log::Comment(String().Format(L"Error message: %hs", errorMsg.c_str()));
                    
                    // Log whether it appears to be a timeout error
                    if (errorMsg.find("time") != std::string::npos || 
                        errorMsg.find("timed out") != std::string::npos)
                    {
                        Log::Comment(L"Error message indicates a timeout condition");
                    }
                }
            }
        }
        catch (const std::exception& e) 
        {
            VERIFY_FAIL(String().Format(L"Failed to parse response: %hs", e.what()));
        }
    }
    
    // Test case 2: Using ADToolAgent's RunCommand with a timeout command
    {
        Log::Comment(L"Test case 2: Using RunCommand with a command that may time out");
        
        // Get ADToolAgent instance
        ADToolAgent& toolAgent = ADToolAgent::GetInstance();
        
        std::wstring output;
        
        // Execute a command with a built-in timeout (ping with wait time)
        Log::Comment(L"Running ping command with timeout");
        bool result = toolAgent.RunCommand(L"ping -n 1 -w 5000 non-existent-host.local", output);
        
        // Even if the ping fails (which it should), the command itself should complete
        VERIFY_IS_TRUE(result, L"RunCommand should return success even for a timed-out ping");
        VERIFY_IS_FALSE(output.empty(), L"Output should not be empty");
        
        Log::Comment(String().Format(L"Command output: %s", output.c_str()));
        
        // Check if the output contains indication of timeout or failure
        if (output.find(L"timed out") != std::wstring::npos || 
            output.find(L"could not find host") != std::wstring::npos) 
        {
            Log::Comment(L"Output correctly indicates timeout or host not found");
        }
    }
    
    Log::Comment(L"Timeout handling tests completed");
    Log::Comment(L"====== Finished TestTimeoutHandling ======");
}

void ADReplicationToolAgentTests::TestIntegrationWithADToolAgent()
{
    Log::Comment(L"====== Starting TestIntegrationWithADToolAgent ======");
    Log::Comment(L"Testing integration between ADToolAgent and ADReplicationToolAgent");
    
    // Get the main ADToolAgent instance
    ADToolAgent& toolAgent = ADToolAgent::GetInstance();
    Log::Comment(L"ADToolAgent instance obtained");
    
    // Create a request that should be routed to the ADReplicationToolAgent
    nlohmann::json request;
    request["ADArea"] = "AD_REPLICATION";
    request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";
    request[AimxConstants::JsonFields::AIMX_ACTION] = AimxConstants::ActionTypes::AIMX_GET_REPLICATION_STATUS_INDIVIDUAL_DC;
    
    std::string requestStr = request.dump();
    Log::Comment(String().Format(L"Request created: %hs", requestStr.c_str()));
    
    std::string responseStr;
    
    // Execute the request through the main agent
    bool result = toolAgent.ExecuteAction(requestStr, responseStr);
    
    Log::Comment(String().Format(L"ExecuteAction result: %s", result ? L"Success" : L"Failure"));
    Log::Comment(String().Format(L"Response received: %hs", responseStr.c_str()));
    
    // Verify that the response is valid and contains expected fields
    try 
    {
        // Parse the response
        nlohmann::json response = nlohmann::json::parse(responseStr);
        
        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
        
        // Verify that we have ReplicationData field in the response when successful
        if (response[AimxConstants::JsonFields::AIMX_RESULT] == AimxConstants::JsonFields::AIMX_SUCCESS) 
        {
            Log::Comment(L"Success response received - verifying replication data");
            
            // Should have ReplicationData field from the ADReplicationToolAgent
            VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_REPLICATION_DATA), 
                          L"Response should contain ReplicationData field");
            
            // Check that the ReplicationData field contains the expected structure
            if (response.contains(AimxConstants::JsonFields::AIMX_REPLICATION_DATA)) 
            {
                auto& replData = response[AimxConstants::JsonFields::AIMX_REPLICATION_DATA];
                
                // Log some details from the replication data
                if (replData.contains("DC_Name")) 
                {
                    Log::Comment(String().Format(L"Replication DC Name: %hs", 
                        replData["DC_Name"].get<std::string>().c_str()));
                }
                
                if (replData.contains("Repl_Status")) 
                {
                    Log::Comment(String().Format(L"Replication Status: %d", 
                        replData["Repl_Status"].get<int>()));
                }
                
                if (replData.contains("Replication_Neighbors") && 
                    replData["Replication_Neighbors"].is_array()) 
                {
                    Log::Comment(String().Format(L"Number of replication neighbors: %zu", 
                        replData["Replication_Neighbors"].size()));
                }
            }
        }
        else 
        {
            // Handle expected failure in non-domain environments
            Log::Comment(L"Failure response received - this is expected in non-domain environments");
            
            if (response.contains(AimxConstants::JsonFields::AIMX_JSON_ERROR)) 
            {
                Log::Comment(String().Format(L"Error message: %hs", 
                    response[AimxConstants::JsonFields::AIMX_JSON_ERROR].get<std::string>().c_str()));
            }
        }
    }
    catch (const std::exception& e) 
    {
        VERIFY_FAIL(String().Format(L"Failed to parse response: %hs", e.what()));
    }
    
    // Also test with an invalid action to verify error handling in the integration
    {
        Log::Comment(L"Testing with invalid replication action");
        
        nlohmann::json badRequest;
        badRequest["ADArea"] = "AD_REPLICATION";
        badRequest[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";
        badRequest[AimxConstants::JsonFields::AIMX_ACTION] = "InvalidReplicationAction";
        
        std::string badRequestStr = badRequest.dump();
        std::string badResponseStr;
        
        bool badResult = toolAgent.ExecuteAction(badRequestStr, badResponseStr);
        
        Log::Comment(String().Format(L"ExecuteAction result for invalid action: %s", 
                                  badResult ? L"Success" : L"Failure"));
        Log::Comment(String().Format(L"Response received: %hs", badResponseStr.c_str()));
        
        // Verify error handling is working correctly
        try
        {
            // Parse the response
            nlohmann::json badResponse = nlohmann::json::parse(badResponseStr);

            VERIFY_IS_TRUE(badResponse.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");

            // Should be a failure for invalid action
            if (badResponse[AimxConstants::JsonFields::AIMX_RESULT] == AimxConstants::JsonFields::AIMX_FAILURE) 
            {
                Log::Comment(L"Correctly received failure for invalid action");
            }
            else {
                Log::Warning(L"Expected failure response for invalid action, but got success");
            }
        }
        catch (const std::exception& e) {
            VERIFY_FAIL(String().Format(L"Failed to parse response: %hs", e.what()));
        }
    }
    
    Log::Comment(L"Integration test completed");
    Log::Comment(L"====== Finished TestIntegrationWithADToolAgent ======");
}

void ADReplicationToolAgentTests::TestDataConsistency()
{
    Log::Comment(L"====== Starting TestDataConsistency ======");
    Log::Comment(L"Testing data consistency across different retrieval methods");
    
    // Get the agents
    ADReplicationToolAgent& replicationAgent = ADReplicationToolAgent::GetADReplicationToolAgentInstance();
    ADToolAgent& toolAgent = ADToolAgent::GetInstance();
    
    Log::Comment(L"Agent instances obtained");
    
    // First attempt to discover a domain controller
    std::wstring dcNameOutput;
    bool result = replicationAgent.RunCommand(
        L"powershell.exe -Command \"try { Import-Module ActiveDirectory; $dc = Get-ADomainController -Discover -Service 'GlobalCatalog'; if ($dc) { $dc[0].Name } else { '' } } catch { Write-Output '' }\"", 
        dcNameOutput);
    
    // Trim whitespace
    dcNameOutput.erase(0, dcNameOutput.find_first_not_of(L" \t\n\r\f\v"));
    dcNameOutput.erase(dcNameOutput.find_last_not_of(L" \t\n\r\f\v") + 1);
    
    std::string dcNameStr;
    bool isDcAvailable = false;
    
    if (!result || dcNameOutput.empty() || dcNameOutput.find(L"Command failed") != std::wstring::npos)
    {
        Log::Comment(L"No domain controller available - using localhost for tests");
        dcNameStr = "localhost";
    } 
    else
    {
        Log::Comment(String().Format(L"Domain controller found: %s", dcNameOutput.c_str()));
        dcNameStr.assign(dcNameOutput.begin(), dcNameOutput.end());
        isDcAvailable = true;
    }
    
    // 1. Get replication data using ADReplicationToolAgent directly
    Log::Comment(L"Method 1: Using ADReplicationToolAgent directly");
    
    nlohmann::json directRequest;
    directRequest[AimxConstants::JsonFields::AIMX_ACTION] = AimxConstants::ActionTypes::AIMX_GET_REPLICATION_STATUS_INDIVIDUAL_DC;
    directRequest[AimxConstants::JsonFields::AIMX_DC_NAME] = dcNameStr;
    
    nlohmann::json directResponse;
    
    HRESULT hr = replicationAgent.GetReplicationInformation(directRequest, directResponse);
    Log::Comment(String().Format(L"Direct response received with HRESULT: 0x%08X", hr));
    Log::Comment(String().Format(L"Direct response received: %hs", directResponse.dump().c_str()));
    
    // 2. Get replication data through ADToolAgent
    Log::Comment(L"Method 2: Using ADToolAgent ExecuteAction");
    
    nlohmann::json indirectRequest;
    indirectRequest["ADArea"] = "AD_REPLICATION";
    indirectRequest[AimxConstants::JsonFields::AIMX_DC_NAME] = dcNameStr;
    indirectRequest[AimxConstants::JsonFields::AIMX_ACTION] = AimxConstants::ActionTypes::AIMX_GET_REPLICATION_STATUS_INDIVIDUAL_DC;
    
    std::string indirectRequestStr = indirectRequest.dump();
    std::string indirectResponseStr;
    
    bool result2 = toolAgent.ExecuteAction(indirectRequestStr, indirectResponseStr);
    Log::Comment(String().Format(L"Indirect response received with result: %s", result2 ? L"Success" : L"Failure"));
    Log::Comment(String().Format(L"Indirect response received: %hs", indirectResponseStr.c_str()));
    
    // 3. Get replication data using RunCommand
    Log::Comment(L"Method 3: Using RunCommand to execute repadmin");
    
    std::wstring repadminOutput;
    std::wstring wstrDcName(dcNameStr.begin(), dcNameStr.end());
    
    std::wstring repadminCommand = L"powershell.exe -Command \"try { & repadmin /showrepl '" + wstrDcName + L"' } catch { Write-Output 'Command failed: repadmin may not be available' }\"";
    
    // Use the RunCommand method from the replicationAgent (which inherits from ADToolAgent)
    replicationAgent.RunCommand(repadminCommand, repadminOutput);
    
    // Log just a sample of the repadmin output (it can be very verbose)
    size_t sampleLength = min(200, static_cast<int>(repadminOutput.length()));
    std::wstring outputSample = repadminOutput.substr(0, sampleLength);
    
    Log::Comment(String().Format(L"Repadmin output sample: %s%s", 
                              outputSample.c_str(), 
                              repadminOutput.length() > sampleLength ? L"..." : L""));
    
    // Compare results between method 1 and 2
    try 
    {
        // We already have the direct response as JSON
        nlohmann::json directJson = directResponse;
        
        // For method 2, we need to extract the ReplicationData field
        nlohmann::json indirectJson = nlohmann::json::parse(indirectResponseStr);
        nlohmann::json indirectReplData;
        
        if (indirectJson.contains(AimxConstants::JsonFields::AIMX_REPLICATION_DATA))
        {
            indirectReplData = indirectJson[AimxConstants::JsonFields::AIMX_REPLICATION_DATA];
        }
        
        // Compare result status
        Log::Comment(L"Comparing results from direct and indirect methods:");
        
        if (directJson.contains("Result") && indirectJson.contains("Result"))
        {
            Log::Comment(String().Format(L"Direct method result: %hs",
                directJson["Result"].get<std::string>().c_str()));
            Log::Comment(String().Format(L"Indirect method result: %hs", 
                indirectJson["Result"].get<std::string>().c_str()));
            
            // Results should match
            bool resultsMatch = (directJson["Result"] == indirectJson["Result"]);
            VERIFY_IS_TRUE(resultsMatch, L"Direct and indirect method results should match");
            
            if (resultsMatch)
            {
                Log::Comment(L"Result fields match between direct and indirect methods");
            }
        }
        
        // If both were successful, compare DC_Name fields
        if (directJson["Result"] == "Success" && indirectJson["Result"] == "Success")
        {
            if (directJson.contains("DC_Name") && indirectReplData.contains("DC_Name"))
            {
                bool dcNamesMatch = (directJson["DC_Name"] == indirectReplData["DC_Name"]);
                Log::Comment(String().Format(L"DC Names match: %s", dcNamesMatch ? L"Yes" : L"No"));
            }
        }
    }
    catch (const std::exception& e) 
    {
        Log::Warning(String().Format(L"Could not compare responses: %hs", e.what()));
    }
    
    Log::Comment(L"Data consistency test completed");
    Log::Comment(L"====== Finished TestDataConsistency ======");
}