/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    ADAgentTools.cpp

Abstract:

    This module implements utility functions for the ADAI Agent in the AIMX application.

Author:

    <PERSON> (SNAKE FIGHTER) (lindakup) 03-28-2025

--*/
#include "pch.h"

bool
ADAgentTools::RunCommand(
    _In_ const std::wstring& wstrCommand,
    _Out_ std::wstring& output
    )
/*++

Routine Description:

    A function that executes the given command and captures the output.
    This function is used to run commands and capture their output for further processing.

Arguments:

    wstrCommand - The command to execute.
    output - The output of the command.

Return Value:

    true if successful, false otherwise.

--*/
{
    // launch the given command and capture the output
    std::unique_ptr<FILE, decltype(&_pclose)> pipe(_wpopen(wstrCommand.c_str(), L"r"), _pclose);

    if (!pipe) {
        LOGERROR(L"Failed to open pipe for command: " + wstrCommand);
        output = L"Failed to open pipe for command: " + wstrCommand;
        return false;
    }

    wchar_t buffer[1024]; // buffer to hold each command output line
    output.clear();
    while (fgetws(buffer, sizeof(buffer) / sizeof(wchar_t), pipe.get()) != nullptr) {
        output.append(buffer);
    }

    if (output.empty())
    {
        LOGERROR(L"Command output is empty: " + wstrCommand);
        return false;
    }

    return true;
}

bool
ADAgentTools::ListDomainControllersInDomain(
    _In_ const std::wstring& wstrPrompt,
    _Out_ std::wstring& dcListString
 )
/*++

Routine Description:

    A function performs a basic health check on the domain controller.

Arguments:

    wstrPrompt - The prompt to extract parameters from.
    wstrHealthStatus - The health status of the domain controller.

Return Value:

    true if successful, false otherwise.

--*/
{
    std::vector<std::wstring> wstrOutputLines;
    bool result = false;
    std::wstring cmd = L"nltest /dclist:";

    dcListString.clear();

    // Get the domain name from the prompt
    std::wstring wstrDomainName;
    if (!GetNameFromPrompt(wstrPrompt, wstrDomainName))
    {
        LOGERROR(L"Failed to get domain name from prompt: " + wstrPrompt);
        dcListString = L"Failed to get domain name from prompt: " + wstrPrompt;
        goto Exit;
    }

    // use nltest to get a list of all domain controllers in the provided domain
    cmd.append(wstrDomainName);
    result = RunCommand(cmd, dcListString);
    if (!result)
    {
        LOGERROR(L"Failed to open pipe for command: " + cmd);
        dcListString = L"Failed to open pipe for command: " + cmd;
        goto Exit;
    }

    // Success.
    result = true;

Exit:
    return result;
}

bool
ADAgentTools::GetDomainControllerInformation(
    _In_ const std::wstring& wstrPrompt,
    _Out_ std::wstring& wstrDcInfo
    )
/*++

Routine Description:

    A function gets information about a domain controller.

Arguments:

    wstrPrompt - The prompt to extract parameters from.
    wstrDcInfo - The domain controller information. Currently using the output of PS command.

Return Value:

    true if successful, false otherwise.

--*/
{
    std::vector<std::wstring> wstrOutputLines;
    bool result = false;
    std::wstring cmd = L"powershell.exe -Command \"Get-ADDomainController -Identity ";
    wstrDcInfo.clear();

    // Get the domain controller name from the prompt
    std::wstring wstrDcName;
    if (!GetNameFromPrompt(wstrPrompt, wstrDcName))
    {
        LOGERROR(L"Failed to get domain controller name from prompt: " + wstrPrompt);
        // return the error message in the wstrDcInfo
        wstrDcInfo = L"Failed to get domain controller name from prompt: " + wstrPrompt;
        goto Exit;
    }

    // execute the command to get the domain controller information.
    cmd.append(wstrDcName + L"\"");
    result = RunCommand(cmd, wstrDcInfo);
    if (!result)
    {
        LOGERROR(L"Failed to get domain controller information for: " + wstrDcName);
        // return the error message in the wstrDcInfo
        wstrDcInfo = L"Failed to get information for: " + wstrDcName;
        goto Exit;
    }

    result = true;

Exit:

    LOGINFO(L"ADAgentTools::GetDomainControllerInformation result: " + wstrDcInfo);
    return result;
}

bool
ADAgentTools::GetNameFromPrompt(
    _In_ const std::wstring& wstrPrompt,
    _Out_ std::wstring& wstrName
    )
/*++

Routine Description:

    A function extracts the dcname or domain name from the prompt.

Arguments:

    wstrPrompt - The prompt to extract parameters from.
    wstrName - The name extracted from the prompt.

Return Value:

    true if successful, false otherwise.

--*/
{
    wstrName.clear();
    bool result = false;

    // see if the prompt contains the word "named"
    // if so, get the next word after the named
    // define an array of search strings
    std::vector<std::wstring> searchStrings = { L"dcname:", L"domain:"};

    // iterate through the searchStrings
    for (const auto& searchString : searchStrings)
    {
        std::wstring::size_type pos = wstrPrompt.find(searchString);
        if (pos != std::wstring::npos)
        {
            // move past the search string
            pos += searchString.length();
            size_t start = pos;
            // Are we at the end?
            if (pos >= wstrPrompt.length())
            {
                break;
            }

            // move to the next space and get the name
            while (pos < wstrPrompt.length() && !iswspace(wstrPrompt[pos]))
            {
                pos++;
            }
            wstrName = wstrPrompt.substr(start, pos - start);
            LOGINFO(L"ADAgentTools::GetNameFromPrompt: Found name: " + wstrName);
            result = true;
            goto Exit;
        }
    }

    // if we got here, we didn't find a name. Return an empty string.
    LOGINFO(L"ADAgentTools::GetNameFromPrompt: No name found in prompt: " + wstrPrompt);
    wstrName = L"";

Exit:
    return result;
}

bool
ADAgentTools::CheckDomainControllerHealth(
    _In_ const std::wstring& wstrPrompt,
    _Out_ std::wstring& wstrHealthStatus
    )
/*++

Routine Description:

    A function performs a basic health check on the domain controller.

Arguments:

    wstrPrompt - The prompt to extract parameters from.
    wstrHealthStatus - The health status of the domain controller.

Return Value:

    true if successful, false otherwise.

--*/
{

    // Get the Name from the prompt
    std::wstring wstrName;
    if (!GetNameFromPrompt(wstrPrompt, wstrName))
    {
        LOGERROR(L"Failed to get name from prompt: " + wstrPrompt);
        return false;
    }

    // Perform a basic health check on the domain controller
    // This is a placeholder implementation and should be replaced with actual health check logic.
    wstrHealthStatus = L"Domain controller " + wstrName + L" is healthy.";
    return true;

    // future health check logic
    // 1. Check if the domain controller is reachable by pinging its DNS name or IP address
    // 2. Check if the domain controller is reachable on all AD ports (e.g. 389, 636, 3268, 3269 and others)
    // 3. run dcdiag /s:<dcname> 
    // or maybe just run dcdiag is enough.....since it would also detect connectivity issues. CSS says blocked ports are the top cause of 
    // many support issues.

}

ADAgentTools::ADAgentTools(/* args */)
{
    // Constructor implementation
    // Initialize any member variables or resources here if needed
    
}

ADAgentTools::~ADAgentTools()
{
    // Destructor implementation
    // Clean up any resources or memory allocated in the constructor here if needed
}


bool
FindStringCaseInsensitive(
    _In_ const std::wstring& inputString,
    _In_ const std::wstring& searchFor
    )
/*++

Routine Description:

    Utlity function to find a string in another string in a case insensitive manner.
    This function is used to check if a string contains another string, regardless of case.

Arguments:

    wstrInputString - The string to search in.
    wstrSearchFor - The string to search for.

Return Value:

    true if successful, false otherwise.

--*/
{
    auto iterator = std::search(
        inputString.begin(), inputString.end(),
        searchFor.begin(), searchFor.end(),
        [](_In_ WCHAR char1, _In_ WCHAR char2)
    {
        return towupper(char1) == towupper(char2);
    });

    return (iterator != inputString.end());
}
