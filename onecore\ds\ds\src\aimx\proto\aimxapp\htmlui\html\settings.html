<!--++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    settings.html

Abstract:

    This module provides the configuration interface for AIMX.
    Allows users to customize LLM endpoints, embedding models, and system parameters
    through a tabbed interface loaded in a modal dialog.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 03/29/2025

---->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIMX Settings</title>
    <link rel="stylesheet" href="../css/styles.css">
</head>
<body>
    <div class="modal-content">
        <div class="modal-header">
            <h2>System Configuration</h2>
            <button class="close-button" id="closeSettingsButton">&times;</button>
        </div>
        <div class="modal-tabs">
            <button class="tab-button active" data-tab="llm-tab">LLM Endpoints</button>
            <button class="tab-button" data-tab="embedding-tab">Embedding Models</button>
            <button class="tab-button" data-tab="advanced-tab">Advanced</button>
        </div>
        <div class="tab-content">
            <!-- LLM Endpoints Tab -->
            <div id="llm-tab" class="tab-pane active">
                <h3>LLM Service Configuration</h3>
                
                <div class="settings-section">
                    <div class="settings-option">
                        <input type="radio" id="localLLM" name="llmType" value="local" checked>
                        <label for="localLLM">Local LLM</label>
                    </div>
                    <div class="settings-field-group local-llm-settings">
                        <div class="settings-field">
                            <label for="localEndpoint">Endpoint URL:</label>
                            <div class="input-with-status">
                                <input type="text" id="localEndpoint" placeholder="http://localhost:8080/v1" value="http://************:8080">
                                <div class="endpoint-status" id="endpointStatus">
                                    <span class="status-indicator-dot"></span>
                                    <span class="status-text">Checking...</span>
                                </div>
                            </div>
                        </div>
                        <div class="settings-field">
                            <label for="localModel">Model:</label>
                            <select id="localModel" disabled>
                                <option value="">Loading models...</option>
                            </select>
                        </div>
                        <div class="model-info" id="modelInfo">
                            <div class="model-info-item">
                                <span class="info-label">Parameters:</span>
                                <span class="info-value" id="modelParameters">-</span>
                            </div>
                            <div class="model-info-item">
                                <span class="info-label">Context Size:</span>
                                <span class="info-value" id="modelContext">-</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="settings-section">
                    <div class="settings-option">
                        <input type="radio" id="openaiLLM" name="llmType" value="openai">
                        <label for="openaiLLM">OpenAI</label>
                    </div>
                    <div class="settings-field-group openai-settings">
                        <div class="settings-field">
                            <label for="openaiKey">API Key:</label>
                            <input type="password" id="openaiKey" placeholder="sk-...">
                        </div>
                        <div class="settings-field">
                            <label for="openaiModel">Model:</label>
                            <select id="openaiModel">
                                <option value="gpt-4o">GPT-4o</option>
                                <option value="gpt-4">GPT-4</option>
                                <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="settings-section">
                    <div class="settings-option">
                        <input type="radio" id="azureLLM" name="llmType" value="azure">
                        <label for="azureLLM">Azure OpenAI</label>
                    </div>
                    <div class="settings-field-group azure-settings">
                        <div class="settings-field">
                            <label for="azureEndpoint">Endpoint:</label>
                            <input type="text" id="azureEndpoint" placeholder="https://your-resource.openai.azure.com/">
                        </div>
                        <div class="settings-field">
                            <label for="azureKey">API Key:</label>
                            <input type="password" id="azureKey" placeholder="Azure OpenAI key">
                        </div>
                        <div class="settings-field">
                            <label for="azureDeployment">Deployment Name:</label>
                            <input type="text" id="azureDeployment" placeholder="gpt-4">
                        </div>
                    </div>
                </div>
                
                <div class="settings-section">
                    <div class="settings-option">
                        <input type="radio" id="claudeLLM" name="llmType" value="claude">
                        <label for="claudeLLM">Anthropic Claude</label>
                    </div>
                    <div class="settings-field-group claude-settings">
                        <div class="settings-field">
                            <label for="claudeKey">API Key:</label>
                            <input type="password" id="claudeKey" placeholder="Anthropic API key">
                        </div>
                        <div class="settings-field">
                            <label for="claudeModel">Model:</label>
                            <select id="claudeModel">
                                <option value="claude-3-opus">Claude 3 Opus</option>
                                <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                                <option value="claude-3-haiku">Claude 3 Haiku</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Embedding Models Tab -->
            <div id="embedding-tab" class="tab-pane">
                <h3>Embedding Model Configuration</h3>
                
                <div class="settings-section">
                    <div class="settings-option">
                        <input type="radio" id="localEmbedding" name="embeddingType" value="local" checked>
                        <label for="localEmbedding">Local Embedding Model</label>
                    </div>
                    <div class="settings-field-group local-embedding-settings">
                        <div class="settings-field">
                            <label for="localEmbeddingEndpoint">Endpoint URL:</label>
                            <input type="text" id="localEmbeddingEndpoint" placeholder="http://localhost:8080/v1/embeddings">
                        </div>
                        <div class="settings-field">
                            <label for="localEmbeddingModel">Model Name:</label>
                            <input type="text" id="localEmbeddingModel" placeholder="all-MiniLM-L6-v2">
                        </div>
                    </div>
                </div>
                
                <div class="settings-section">
                    <div class="settings-option">
                        <input type="radio" id="openaiEmbedding" name="embeddingType" value="openai">
                        <label for="openaiEmbedding">OpenAI Embeddings</label>
                    </div>
                    <div class="settings-field-group openai-embedding-settings">
                        <div class="settings-field">
                            <label for="openaiEmbeddingKey">API Key:</label>
                            <input type="password" id="openaiEmbeddingKey" placeholder="sk-...">
                        </div>
                        <div class="settings-field">
                            <label for="openaiEmbeddingModel">Model:</label>
                            <select id="openaiEmbeddingModel">
                                <option value="text-embedding-3-large">text-embedding-3-large</option>
                                <option value="text-embedding-3-small">text-embedding-3-small</option>
                                <option value="text-embedding-ada-002">text-embedding-ada-002</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="settings-section">
                    <div class="settings-option">
                        <input type="radio" id="azureEmbedding" name="embeddingType" value="azure">
                        <label for="azureEmbedding">Azure OpenAI Embeddings</label>
                    </div>
                    <div class="settings-field-group azure-embedding-settings">
                        <div class="settings-field">
                            <label for="azureEmbeddingEndpoint">Endpoint:</label>
                            <input type="text" id="azureEmbeddingEndpoint" placeholder="https://your-resource.openai.azure.com/">
                        </div>
                        <div class="settings-field">
                            <label for="azureEmbeddingKey">API Key:</label>
                            <input type="password" id="azureEmbeddingKey" placeholder="Azure OpenAI key">
                        </div>
                        <div class="settings-field">
                            <label for="azureEmbeddingDeployment">Deployment Name:</label>
                            <input type="text" id="azureEmbeddingDeployment" placeholder="text-embedding-ada-002">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Advanced Tab -->
            <div id="advanced-tab" class="tab-pane">
                <h3>Advanced Settings</h3>
                
                <div class="settings-section">
                    <h4>RAG Configuration</h4>
                    <div class="settings-field">
                        <label for="chunkSize">Chunk Size:</label>
                        <input type="number" id="chunkSize" value="512" min="128" max="4096">
                    </div>
                    <div class="settings-field">
                        <label for="chunkOverlap">Chunk Overlap:</label>
                        <input type="number" id="chunkOverlap" value="50" min="0" max="512">
                    </div>
                </div>
                
                <div class="settings-section">
                    <h4>System Settings</h4>
                    <div class="settings-field">
                        <label for="maxConcurrentRequests">Max Concurrent Requests:</label>
                        <input type="number" id="maxConcurrentRequests" value="4" min="1" max="16">
                    </div>
                    <div class="settings-field">
                        <label for="debugMode">Debug Mode:</label>
                        <select id="debugMode">
                            <option value="off">Off</option>
                            <option value="basic">Basic</option>
                            <option value="verbose">Verbose</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button id="saveSettingsButton" class="primary-button">Save Settings</button>
            <button id="cancelSettingsButton" class="secondary-button">Cancel</button>
        </div>
    </div>
</body>
</html>
