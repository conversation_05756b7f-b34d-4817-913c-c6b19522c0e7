/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    RagService.h

Abstract:

    This module defines the RAG service class for hosting HTTP endpoints
    and providing RAG functionality through a REST API.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 04/12/2025

--*/

#pragma once

#include <windows.h>
#include <string>
#include <memory>
#include <mutex>
#include <atomic>

// Forward declarations
class RagHttpServer;

class RagService 
{
public:
    RagService();
    ~RagService();
    
    // Static service entry points for Windows service
    static void WINAPI 
    ServiceMain(
        DWORD argc, 
        LPWSTR* argv
        );
    
    static void WINAPI 
    ServiceCtrlHandler(
        DWORD control
        );
    
    // Service control functions
    bool 
    Initialize();
    
    void 
    Run();
    
    void 
    Stop();
    
    // Service status management
    void 
    SetServiceStatus(
        _In_ DWORD currentState, 
        _In_ DWORD exitCode = NO_ERROR, 
        _In_ DWORD waitHint = 0
        );
    
    // Access to the running flag for signal handling
    std::atomic<bool>& 
    GetRunningFlag() 
    { 
        return m_running; 
    }

private:
    // Resource management
    bool 
    InitializeResources();
    
    void 
    CleanupResources();
    
    // RAG initialization
    bool 
    InitializeRag();
    
    // HTTP server management
    bool 
    StartHttpServer();
    
    // Service state
    std::atomic<bool> m_running;
    
    // HTTP server instance
    std::unique_ptr<RagHttpServer> m_httpServer;
    
    // Static service state
    static RagService* s_service;
    static SERVICE_STATUS_HANDLE s_statusHandle;
    static SERVICE_STATUS s_status;
};