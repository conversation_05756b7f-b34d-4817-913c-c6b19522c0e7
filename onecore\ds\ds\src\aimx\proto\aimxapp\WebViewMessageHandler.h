/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    WebViewMessageHandler.h

Abstract:

    This module defines the WebView2 message handler class for AIMX.
    Handles comunication between the web UI and native application.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 03/19/2025

--*/

#pragma once

#include "pch.h"
#include "LlmService.h"
#include "RAGBuilder.h"

#include <wrl.h>
#include <wrl/client.h>
#include <wil/com.h>
#include <WebView2.h>
#include <WebView2EnvironmentOptions.h>
#include <functional>
#include <queue>
#include <mutex>
#include <shobjidl_core.h>
#include <unordered_map>

// Forward declarations for WebView2 interfaces
interface ICoreWebView2;
interface ICoreWebView2WebMessageReceivedEventArgs;

// Handler for WebView2 messages
class WebViewMessageHandler {
public:
    WebViewMessageHandler(
        _In_ ICoreWebView2* webView
        );
    ~WebViewMessageHandler();

    // Initialize message handeling
    void
    Initialize();
    
    // Set the LLM servise
    void
    SetLlmService(
        _In_ LlmService* llmService
    );

    //Set the ADAIagent
    void
    SetADAgent(
        _In_ ADAgent* adAgent
    );

private:
    // Callback for handeling messages
    HRESULT
    HandleWebMessage(
        _In_ ICoreWebView2* sender,
        _In_ ICoreWebView2WebMessageReceivedEventArgs* args
        );
    
    // Parse a WebView message into JSON
    nlohmann::json
    ParseWebMessage(
        _In_ const std::wstring& message
        );
    
    // Send a message on the WebView
    void
    SendMessageToWebView(
        _In_ const std::wstring& message,
        _In_ bool logFullMessage = true
        );

    // Send settings configuration to the WebView
    void
    SendSettingsToWebView(
        );

    // Send a toast notification to the webview
    void SendToastNotification(
        _In_ const std::wstring& message
    );
    
    // Process a prompt
    void
    ProcessPrompt(
        _In_ const std::wstring& prompt
        );
    
    // New method for streaming with optional RAG support
    void
    ProcessPromptStreaming(
        _In_ const std::wstring& prompt,
        _In_ bool useRag = false
        );

    // Parse JSON message - use the propper JSON library now
    bool
    ParseJsonMessage(
        _In_ const std::wstring& json,
        _Out_ std::wstring& type,
        _Out_ std::wstring& content
        );
    
    // method to generate session IDs
    std::wstring
    GenerateSessionId();
    
    // RAG builder methods
    void
    HandleRagBuilderMessage(
        _In_ const nlohmann::json& message
        );
    
    void
    StartRagBuild(
        _In_ const std::string& outputDir,
        _In_ const std::string& docsDir,
        _In_ const std::string& databaseName,
        _In_ int chunkSize
        );
    
    void
    SendProgressUpdate(
        _In_ const std::string& status,
        _In_ int progressPercent
        );

    void HandleRagSelectionMessage(
        _In_ const nlohmann::json& message
        );

    // Native file browser methods
    void
    BrowseForFolder();
    
    void
    BrowseForFiles();

    // Helper methods for file browsing
    void
    SendBrowseErrorResponse(
        _In_ const std::string& errorMessage
        );
    
    void
    SendBrowseCancellationResponse();
    
    bool
    ConfigureFileOpenDialog(
        _In_ IFileOpenDialog* pFileDialog,
        _In_ bool folderPicker
        );
    
    bool
    GetSelectedFolder(
        _In_ IFileDialog* pFileDialog
        );
    
    bool
    ProcessSelectedFiles(
        _In_ IShellItemArray* pItemArray
        );
    
    void
    CollectFileInfo(
        _In_ const wchar_t* filePath,
        _Inout_ nlohmann::json& filesArray
        );
      // AD Dashboard message handler
    void 
    HandleADDashboardMessage(
        _In_ const nlohmann::json& message
        );
    
    // Entra ID Migration message handler
    void 
    HandleEntraMigrationMessage(
        _In_ const nlohmann::json& message
        );

    // Message type enum for switch statement dispatch
    enum class MessageType {
        Unknown,
        RagBuilder,
        PageNavigation,
        Prompt,
        Debug,
        GetEndpointInfo,
        GetSettings,
        RagToggleChanged,
        ADDashboard,
        EntraidMigration,
        RagSelection
    };
    
    // Convert string message type to enum type
    static MessageType 
    HashMessageType(
        _In_ const std::string& messageType
        );
    
    // Static map for message type lookups
    static const std::unordered_map<std::string, MessageType> messageTypeMap;

    wil::com_ptr<ICoreWebView2> _webView;
    EventRegistrationToken _webMessageToken;
    LlmService* _llmService;
    ADAgent* _adAgent;

    // RAG builder instance
    std::unique_ptr<RAGBuilder> _ragBuilder;
    bool _ragBuildInProgress;
};
