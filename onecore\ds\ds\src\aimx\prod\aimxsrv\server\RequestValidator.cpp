/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    RequestValidator.cpp

Abstract:

    Implementation file for the Request Validator component that implements enhanced request filtering
    for the AIMX server. This component provides three-gate validation:
    1. Enterprise/IT Management Request Validation
    2. Single vs Multi-Step Task Detection  
    3. Read-Only Operation Enforcement

Author:

    <PERSON><PERSON><PERSON> (r<PERSON>hang) 07/30/2025

--*/

#include "pch.hxx"
#include "RequestValidator.h"
#include "AimxConstants.h"
#include "StringUtils.h"
#include "AimxLlmConfig.h"
#include <cpprest/http_client.h>
#include <cpprest/json.h>
#include <algorithm>
#include <ctime>

#include "RequestValidator.cpp.tmh"

// Static member definitions
RequestValidator* RequestValidator::s_instance = nullptr;
std::mutex RequestValidator::s_instanceMutex;

HRESULT
RequestValidator::Initialize()
/*++

Routine Description:
    Initialize the Request Validator component.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    std::lock_guard<std::mutex> lock(s_instanceMutex);
    
    if (s_instance != nullptr)
    {
        return AIMX_S_COMPONENT_ALREADY_INITIALIZED;
    }

    s_instance = new (std::nothrow) RequestValidator();
    if (s_instance == nullptr)
    {
        return E_OUTOFMEMORY;
    }

    HRESULT hr = s_instance->InitializeInternal();
    if (FAILED(hr))
    {
        delete s_instance;
        s_instance = nullptr;
        return hr;
    }

    return S_OK;
}

void
RequestValidator::Uninitialize()
/*++

Routine Description:
    Uninitialize and cleanup the Request Validator component.

Arguments:
    None.

Return Value:
    None.

--*/
{
    std::lock_guard<std::mutex> lock(s_instanceMutex);
    
    if (s_instance != nullptr)
    {
        delete s_instance;
        s_instance = nullptr;
    }
}

HRESULT
RequestValidator::ValidateRequest(
    _In_ const AIMX_VALIDATION_CONTEXT& context,
    _Out_ AIMX_VALIDATION_RESPONSE& response
    )
/*++

Routine Description:
    Main validation entry point that runs all three validation gates in sequence.

Arguments:
    context - Validation context containing user query and session info
    response - Output validation response

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    HRESULT hr = S_OK;

    // Initialize response
    response.Result = AIMX_VALIDATION_PASSED;
    response.ShouldContinueProcessing = true;

    // Gate 1: Enterprise/IT Management Validation
    hr = ValidateEnterpriseITRequest(context, response);
    if (FAILED(hr) || !response.ShouldContinueProcessing)
    {
        return hr;
    }

    // Gate 2: Single vs Multi-Step Detection
    hr = ValidateSingleVsMultiStep(context, response);
    if (FAILED(hr) || !response.ShouldContinueProcessing)
    {
        return hr;
    }

    // Gate 3: Read-Only Operation Enforcement
    hr = ValidateReadOnlyOperation(context, response);
    if (FAILED(hr) || !response.ShouldContinueProcessing)
    {
        return hr;
    }

    return S_OK;
}

HRESULT
RequestValidator::ValidateEnterpriseITRequest(
    _In_ const AIMX_VALIDATION_CONTEXT& context,
    _Out_ AIMX_VALIDATION_RESPONSE& response
    )
/*++

Routine Description:
    Gate 1: Validate if the request is related to enterprise system management or troubleshooting.

Arguments:
    context - Validation context containing user query
    response - Output validation response

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxRequestValidator, "Entry - ValidateEnterpriseITRequest");

    if (s_instance == nullptr)
    {
        TraceErr(AimxRequestValidator, "RequestValidator instance is null");
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    // Send progress update
    if (context.ConversationSession)
    {
        context.ConversationSession->UpdateStatus(
            std::wstring(AimxConstants::MessageStages::AIMX_STAGE_REQUEST_VALIDATION),
            L"Validating request is enterprise IT management related..."
        );
    }

    TraceInfo(AimxRequestValidator, "Getting enterprise validation prompt");
    std::wstring systemPrompt = s_instance->GetEnterpriseValidationPrompt();
    std::wstring llmResponse;

    TraceInfo(AimxRequest, "=== ENTERPRISE VALIDATION REQUEST ===");
    TraceInfo(AimxRequest, "System Prompt: %ws", systemPrompt.c_str());
    TraceInfo(AimxRequest, "User Query: %ws", context.UserQuery.c_str());
    TraceInfo(AimxRequestValidator, "Calling SendValidationLlmRequest for enterprise validation");

    HRESULT hr = s_instance->SendValidationLlmRequest(systemPrompt, context.UserQuery, llmResponse);
    if (FAILED(hr))
    {
        TraceErr(AimxRequestValidator, "SendValidationLlmRequest failed with HRESULT: %!HRESULT!", hr);
        response.Result = AIMX_VALIDATION_ERROR;
        response.Message = AimxConstants::ValidationMessages::ValidationError;
        response.ShouldContinueProcessing = false;
        return hr;
    }

    bool isEnterpriseIT = false;
    std::wstring reason;

    TraceInfo(AimxRequest, "=== ENTERPRISE VALIDATION RESPONSE ===");
    TraceInfo(AimxRequest, "Raw LLM Response: '%ws'", llmResponse.c_str());

    hr = s_instance->ParseEnterpriseValidationResponse(llmResponse, isEnterpriseIT, reason);
    if (FAILED(hr))
    {
        response.Result = AIMX_VALIDATION_ERROR;
        response.Message = AimxConstants::ValidationMessages::ValidationError;
        response.ShouldContinueProcessing = false;
        return hr;
    }

    TraceInfo(AimxRequest, "=== ENTERPRISE VALIDATION RESULT ===");
    TraceInfo(AimxRequest, "isEnterpriseIT=%s, reason='%ws'",
              isEnterpriseIT ? "true" : "false", reason.c_str());

    if (!isEnterpriseIT)
    {
        response.Result = AIMX_VALIDATION_NOT_ENTERPRISE_IT;
        response.Message = AimxConstants::ValidationMessages::NotEnterpriseItRequest;
        response.DetailedReason = reason;
        response.ShouldContinueProcessing = false;

        if (context.ConversationSession)
        {
            context.ConversationSession->SendMessage(
                AIMX_MSG_ERROR_MESSAGE,
                L"[" + AimxConstants::MessageStages::AIMX_STAGE_ENTERPRISE_FILTER + L"] " + response.Message + L" Reason: " + reason
            );
        }
    }

    return S_OK;
}

HRESULT
RequestValidator::ValidateSingleVsMultiStep(
    _In_ const AIMX_VALIDATION_CONTEXT& context,
    _Out_ AIMX_VALIDATION_RESPONSE& response
    )
/*++

Routine Description:
    Gate 2: Detect if the request requires single or multiple steps to accomplish.

Arguments:
    context - Validation context containing user query
    response - Output validation response

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    // Send progress update
    if (context.ConversationSession)
    {
        context.ConversationSession->UpdateStatus(
            AimxConstants::MessageStages::AIMX_STAGE_REQUEST_VALIDATION,
            L"Analyzing task complexity (single vs multi-step)..."
        );
    }

    std::wstring systemPrompt = s_instance->GetMultiStepDetectionPrompt();
    std::wstring llmResponse;

    TraceInfo(AimxRequest, "=== MULTI-STEP DETECTION REQUEST ===");
    TraceInfo(AimxRequest, "System Prompt: %ws", systemPrompt.c_str());
    TraceInfo(AimxRequest, "User Query: %ws", context.UserQuery.c_str());

    HRESULT hr = s_instance->SendValidationLlmRequest(systemPrompt, context.UserQuery, llmResponse);
    if (FAILED(hr))
    {
        response.Result = AIMX_VALIDATION_ERROR;
        response.Message = AimxConstants::ValidationMessages::ValidationError;
        response.ShouldContinueProcessing = false;
        return hr;
    }

    bool isMultiStep = false;
    std::vector<std::wstring> suggestedSteps;
    std::wstring reason;

    TraceInfo(AimxRequest, "=== MULTI-STEP DETECTION RESPONSE ===");
    TraceInfo(AimxRequest, "Raw LLM Response: '%ws'", llmResponse.c_str());

    hr = s_instance->ParseMultiStepResponse(llmResponse, isMultiStep, suggestedSteps, reason);
    if (FAILED(hr))
    {
        response.Result = AIMX_VALIDATION_ERROR;
        response.Message = AimxConstants::ValidationMessages::ValidationError;
        response.ShouldContinueProcessing = false;
        return hr;
    }

    TraceInfo(AimxRequest, "=== MULTI-STEP DETECTION RESULT ===");
    TraceInfo(AimxRequest, "isMultiStep=%s, reason='%ws'",
              isMultiStep ? "true" : "false", reason.c_str());

    if (isMultiStep)
    {
        response.Result = AIMX_VALIDATION_MULTI_STEP_REQUIRED;
        response.Message = AimxConstants::ValidationMessages::MultiStepRequired;
        response.DetailedReason = reason;
        response.SuggestedSteps = suggestedSteps;
        response.ShouldContinueProcessing = false;

        if (context.ConversationSession)
        {
            std::wstring stepsMessage = L"Suggested steps:\n";
            for (size_t i = 0; i < suggestedSteps.size(); ++i)
            {
                stepsMessage += L"  " + std::to_wstring(i + 1) + L". " + suggestedSteps[i] + L"\n";
            }

            context.ConversationSession->SendMessage(
                AIMX_MSG_TASK_BREAKDOWN,
                L"[" + AimxConstants::MessageStages::AIMX_STAGE_STEP_DETECTION + L"] " + response.Message + L"\n\n" + stepsMessage
            );
        }
    }

    return S_OK;
}

HRESULT
RequestValidator::ValidateReadOnlyOperation(
    _In_ const AIMX_VALIDATION_CONTEXT& context,
    _Out_ AIMX_VALIDATION_RESPONSE& response
    )
/*++

Routine Description:
    Gate 3: Validate that the request is a read-only operation and not a modification.

Arguments:
    context - Validation context containing user query
    response - Output validation response

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    // Send progress update
    if (context.ConversationSession)
    {
        context.ConversationSession->UpdateStatus(
            AimxConstants::MessageStages::AIMX_STAGE_REQUEST_VALIDATION,
            L"Validating operation is read-only..."
        );
    }

    std::wstring systemPrompt = s_instance->GetReadOnlyValidationPrompt();
    std::wstring llmResponse;

    HRESULT hr = s_instance->SendValidationLlmRequest(systemPrompt, context.UserQuery, llmResponse);
    if (FAILED(hr))
    {
        response.Result = AIMX_VALIDATION_ERROR;
        response.Message = AimxConstants::ValidationMessages::ValidationError;
        response.ShouldContinueProcessing = false;
        return hr;
    }

    bool isModificationOperation = false;
    std::wstring reason;
    hr = s_instance->ParseReadOnlyResponse(llmResponse, isModificationOperation, reason);
    if (FAILED(hr))
    {
        response.Result = AIMX_VALIDATION_ERROR;
        response.Message = AimxConstants::ValidationMessages::ValidationError;
        response.ShouldContinueProcessing = false;
        return hr;
    }

    if (isModificationOperation)
    {
        response.Result = AIMX_VALIDATION_MODIFICATION_BLOCKED;
        response.Message = AimxConstants::ValidationMessages::ModificationBlocked;
        response.DetailedReason = reason;
        response.ShouldContinueProcessing = false;

        if (context.ConversationSession)
        {
            context.ConversationSession->SendMessage(
                AIMX_MSG_ERROR_MESSAGE,
                L"[" + AimxConstants::MessageStages::AIMX_STAGE_READONLY_ENFORCEMENT + L"] " + response.Message + L" Reason: " + reason
            );
        }
    }

    return S_OK;
}

HRESULT
RequestValidator::SendValidationLlmRequest(
    _In_ const std::wstring& systemPrompt,
    _In_ const std::wstring& userQuery,
    _Out_ std::wstring& response
    )
/*++

Routine Description:
    Send a validation request to the LLM service.

Arguments:
    systemPrompt - The system prompt for validation
    userQuery - The user query to validate
    response - Output LLM response

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxRequestValidator, "Entry - SendValidationLlmRequest");

    try
    {
        // Get configuration from global singleton
        AimxLlmConfig& llmConfig = AimxLlmConfig::Instance();

        // Log configuration details
        std::wstring endpointUrl = llmConfig.GetEndpointUrl();
        std::wstring model = llmConfig.GetModel();
        TraceInfo(AimxRequestValidator, "LLM Configuration - Endpoint: %ws, Model: %ws", endpointUrl.c_str(), model.c_str());
        TraceInfo(AimxRequestValidator, "User Query Length: %lu characters", static_cast<unsigned long>(userQuery.length()));
        TraceInfo(AimxRequestValidator, "System Prompt Length: %lu characters", static_cast<unsigned long>(systemPrompt.length()));

        // Build request JSON
        web::json::value requestJson = web::json::value::object();
        requestJson[L"model"] = web::json::value::string(model);
        requestJson[L"max_tokens"] = web::json::value::number(512);
        requestJson[L"temperature"] = web::json::value::number(0.1);

        web::json::value messages = web::json::value::array();

        // System message
        web::json::value systemMessage = web::json::value::object();
        systemMessage[L"role"] = web::json::value::string(L"system");
        systemMessage[L"content"] = web::json::value::string(systemPrompt);
        messages[0] = systemMessage;

        // User message
        web::json::value userMessage = web::json::value::object();
        userMessage[L"role"] = web::json::value::string(L"user");
        userMessage[L"content"] = web::json::value::string(userQuery);
        messages[1] = userMessage;

        requestJson[L"messages"] = messages;

        // Log the request JSON (truncated for security)
        std::wstring requestJsonStr = requestJson.serialize();
        TraceInfo(AimxRequestValidator, "Request JSON size: %lu characters", static_cast<unsigned long>(requestJsonStr.length()));

        // Create HTTP client
        TraceInfo(AimxRequest, "Creating HTTP client for endpoint: %ws", endpointUrl.c_str());
        web::http::client::http_client client(endpointUrl);

        // Create request
        web::http::http_request request(web::http::methods::POST);
        // NOTE: Do not set request_uri since endpointUrl already contains the full path
        // The endpointUrl from AimxLlmConfig already includes "/v1/chat/completions"
        request.headers().set_content_type(L"application/json");

        request.set_body(requestJson);

        TraceInfo(AimxRequest, "Sending HTTP POST request to LLM service");

        // Send request and wait for response
        auto responseTask = client.request(request);
        auto httpResponse = responseTask.get();

        auto statusCode = httpResponse.status_code();
        TraceInfo(AimxRequestValidator, "HTTP Response Status Code: %d", static_cast<int>(statusCode));

        if (statusCode != web::http::status_codes::OK)
        {
            TraceErr(AimxRequestValidator, "HTTP request failed with status code: %d", static_cast<int>(statusCode));

            // Try to extract error response body for debugging
            try
            {
                auto errorBody = httpResponse.extract_string().get();
                TraceErr(AimxRequestValidator, "Error response body: %ws", errorBody.c_str());
            }
            catch (...)
            {
                TraceErr(AimxRequestValidator, "Could not extract error response body");
            }

            return E_FAIL;
        }

        TraceInfo(AimxRequestValidator, "HTTP request successful, parsing JSON response");

        // Parse response
        auto responseBody = httpResponse.extract_json().get();

        // Log response structure for debugging
        std::wstring responseStr = responseBody.serialize();
        TraceInfo(AimxRequestValidator, "Response JSON size: %lu characters", static_cast<unsigned long>(responseStr.length()));

        // Check if response has expected structure
        if (!responseBody.has_field(L"choices"))
        {
            TraceErr(AimxRequestValidator, "Response missing 'choices' field");
            TraceErr(AimxRequestValidator, "Response structure: %ws", responseStr.c_str());
            return E_FAIL;
        }

        if (!responseBody[L"choices"].is_array())
        {
            TraceErr(AimxRequestValidator, "Response 'choices' field is not an array");
            return E_FAIL;
        }

        if (responseBody[L"choices"].size() == 0)
        {
            TraceErr(AimxRequestValidator, "Response 'choices' array is empty");
            return E_FAIL;
        }

        auto choice = responseBody[L"choices"][0];

        if (!choice.has_field(L"message"))
        {
            TraceErr(AimxRequestValidator, "First choice missing 'message' field");
            return E_FAIL;
        }

        if (!choice[L"message"].has_field(L"content"))
        {
            TraceErr(AimxRequestValidator, "Message missing 'content' field");
            return E_FAIL;
        }

        response = choice[L"message"][L"content"].as_string();
        TraceInfo(AimxRequestValidator, "Successfully extracted LLM response, length: %lu characters", static_cast<unsigned long>(response.length()));
        TraceInfo(AimxRequestValidator, "Exit - SendValidationLlmRequest: S_OK");
        return S_OK;
    }
    catch (const web::http::http_exception& e)
    {
        TraceErr(AimxRequestValidator, "HTTP exception in SendValidationLlmRequest: %s", e.what());
        TraceErr(AimxRequestValidator, "Exit - SendValidationLlmRequest: E_FAIL (HTTP exception)");
        return E_FAIL;
    }
    catch (const web::json::json_exception& e)
    {
        TraceErr(AimxRequestValidator, "JSON exception in SendValidationLlmRequest: %s", e.what());
        TraceErr(AimxRequestValidator, "Exit - SendValidationLlmRequest: E_FAIL (JSON exception)");
        return E_FAIL;
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxRequestValidator, "Standard exception in SendValidationLlmRequest: %s", e.what());
        TraceErr(AimxRequestValidator, "Exit - SendValidationLlmRequest: E_FAIL (std exception)");
        return E_FAIL;
    }
    catch (...)
    {
        TraceErr(AimxRequestValidator, "Unknown exception in SendValidationLlmRequest");
        TraceErr(AimxRequestValidator, "Exit - SendValidationLlmRequest: E_FAIL (unknown exception)");
        return E_FAIL;
    }
}

HRESULT
RequestValidator::ParseEnterpriseValidationResponse(
    _In_ const std::wstring& llmResponse,
    _Out_ bool& isEnterpriseIT,
    _Out_ std::wstring& reason
    )
/*++

Routine Description:
    Parse LLM response for enterprise IT validation.

Arguments:
    llmResponse - The raw LLM response
    isEnterpriseIT - Output boolean indicating if request is enterprise IT related
    reason - Output reason for the decision

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    // Convert to lowercase for case-insensitive comparison
    std::wstring lowerResponse = llmResponse;
    std::transform(lowerResponse.begin(), lowerResponse.end(), lowerResponse.begin(), ::towlower);

    // Trim whitespace
    lowerResponse.erase(0, lowerResponse.find_first_not_of(L" \t\r\n"));
    lowerResponse.erase(lowerResponse.find_last_not_of(L" \t\r\n") + 1);

    // Simple YES/NO parsing - look for YES or NO anywhere in the response
    if (lowerResponse.find(L"yes") != std::wstring::npos)
    {
        isEnterpriseIT = true;
        reason = L"LLM classified as enterprise IT request";
    }
    else if (lowerResponse.find(L"no") != std::wstring::npos)
    {
        isEnterpriseIT = false;
        reason = L"LLM classified as non-enterprise IT request";
    }
    else
    {
        // Default to false for any unclear response
        isEnterpriseIT = false;
        reason = L"Unclear LLM response, defaulting to non-enterprise IT";
    }

    return S_OK;
}

HRESULT
RequestValidator::ParseMultiStepResponse(
    _In_ const std::wstring& llmResponse,
    _Out_ bool& isMultiStep,
    _Out_ std::vector<std::wstring>& suggestedSteps,
    _Out_ std::wstring& reason
    )
/*++

Routine Description:
    Parse LLM response for multi-step detection.

Arguments:
    llmResponse - The raw LLM response
    isMultiStep - Output boolean indicating if request requires multiple steps
    suggestedSteps - Output vector of suggested steps
    reason - Output reason for the decision

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    // Convert to lowercase for case-insensitive comparison
    std::wstring lowerResponse = llmResponse;
    std::transform(lowerResponse.begin(), lowerResponse.end(), lowerResponse.begin(), ::towlower);

    // Trim whitespace
    lowerResponse.erase(0, lowerResponse.find_first_not_of(L" \t\r\n"));
    lowerResponse.erase(lowerResponse.find_last_not_of(L" \t\r\n") + 1);

    // Simple YES/NO parsing
    if (lowerResponse == L"yes")
    {
        isMultiStep = true;
        reason = L"LLM classified as multi-step task";
        suggestedSteps.push_back(L"Break down your request into individual tasks");
        suggestedSteps.push_back(L"Submit each task separately");
    }
    else if (lowerResponse == L"no")
    {
        isMultiStep = false;
        reason = L"LLM classified as single-step task";
    }
    else
    {
        // Default to false for any unclear response
        isMultiStep = false;
        reason = L"Unclear LLM response, defaulting to single-step";
    }

    return S_OK;
}

HRESULT
RequestValidator::ParseReadOnlyResponse(
    _In_ const std::wstring& llmResponse,
    _Out_ bool& isModificationOperation,
    _Out_ std::wstring& reason
    )
/*++

Routine Description:
    Parse LLM response for read-only validation.

Arguments:
    llmResponse - The raw LLM response
    isModificationOperation - Output boolean indicating if request is a modification operation
    reason - Output reason for the decision

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    try
    {
        // Try to parse as JSON first
        std::string utf8Response = WideToUtf8(llmResponse);
        nlohmann::json responseJson = nlohmann::json::parse(utf8Response);

        if (responseJson.contains("is_modification") && responseJson.contains("reason"))
        {
            isModificationOperation = responseJson["is_modification"].get<bool>();
            reason = Utf8ToWide(responseJson["reason"].get<std::string>());
            return S_OK;
        }
    }
    catch (...)
    {
        // Fallback to text parsing if JSON parsing fails
        std::wstring lowerResponse = llmResponse;
        std::transform(lowerResponse.begin(), lowerResponse.end(), lowerResponse.begin(), ::towlower);

        // Look for modification indicators
        if (lowerResponse.find(L"add") != std::wstring::npos ||
            lowerResponse.find(L"remove") != std::wstring::npos ||
            lowerResponse.find(L"delete") != std::wstring::npos ||
            lowerResponse.find(L"create") != std::wstring::npos ||
            lowerResponse.find(L"modify") != std::wstring::npos ||
            lowerResponse.find(L"change") != std::wstring::npos ||
            lowerResponse.find(L"update") != std::wstring::npos ||
            lowerResponse.find(L"set") != std::wstring::npos)
        {
            isModificationOperation = true;
            reason = L"Request appears to involve modification operations";
        }
        else
        {
            isModificationOperation = false;
            reason = L"Request appears to be read-only";
        }
    }

    return S_OK;
}

std::wstring
RequestValidator::GetEnterpriseValidationPrompt()
/*++

Routine Description:
    Get the system prompt for enterprise IT validation.

Arguments:
    None.

Return Value:
    Enterprise validation system prompt string.

--*/
{
    return LR"(You are an expert classifier for enterprise IT management and troubleshooting requests.

Your task is to determine if a user request is related to enterprise system management, IT administration, or troubleshooting.

## Enterprise IT Management includes:
- Active Directory operations (users, groups, computers, domains)
- Windows system administration
- Network troubleshooting and configuration
- Server management and monitoring
- Security policy management
- System diagnostics and health checks
- PowerShell administration tasks
- Enterprise software management
- Infrastructure monitoring
- IT helpdesk and support tasks

## NOT Enterprise IT Management:
- General knowledge questions
- Personal computing help
- Software development questions
- Academic or research queries
- Entertainment or leisure topics
- General conversation
- Non-technical questions

Respond with only "YES" if this is an enterprise IT management request, or "NO" if it is not.
Be strict in your classification. Only return YES for genuine enterprise IT management or troubleshooting requests.)";
}

std::wstring
RequestValidator::GetMultiStepDetectionPrompt()
/*++

Routine Description:
    Get the system prompt for multi-step detection.

Arguments:
    None.

Return Value:
    Multi-step detection system prompt string.

--*/
{
    return LR"(You are an expert task analyzer for enterprise IT operations.

Your task is to determine if a user request requires multiple distinct steps or can be accomplished as a single operation.

## Single-Step Tasks:
- Get information about a specific user
- Check status of a specific service
- View configuration of a specific component
- Retrieve specific system information
- Run a single diagnostic command
- Look up specific data

## Multi-Step Tasks:
- "Set up a new user and add them to groups and configure permissions"
- "Troubleshoot network connectivity and fix any issues found"
- "Audit all users and generate a report with recommendations"
- "Check system health and optimize performance"
- "Migrate users from one OU to another and update policies"
- Tasks that involve multiple different operations
- Tasks that require conditional logic (if X then Y)
- Tasks that involve workflows or processes

Respond with only "YES" if this requires multiple steps, or "NO" if it can be done in a single step.
Be conservative - if a task could reasonably be broken down into multiple distinct operations, respond with YES.)";
}

std::wstring
RequestValidator::GetReadOnlyValidationPrompt()
/*++

Routine Description:
    Get the system prompt for read-only validation.

Arguments:
    None.

Return Value:
    Read-only validation system prompt string.

--*/
{
    return LR"(You are an expert classifier for determining if IT operations are read-only or involve modifications.

Your task is to determine if a user request involves making changes to the system or is purely informational.

## Read-Only Operations (ALLOWED):
- Get/View/Show/List/Display information
- Check status or health
- Search or query data
- Generate reports
- View configurations
- Monitor systems
- Retrieve logs
- Test connectivity (non-destructive)
- Validate settings

## Modification Operations (NOT ALLOWED):
- Add/Create users, groups, computers, or other objects
- Remove/Delete users, groups, computers, or other objects
- Modify/Update/Change user properties, group memberships, or configurations
- Set/Configure policies or settings
- Enable/Disable services or features
- Install/Uninstall software
- Move objects between OUs
- Reset passwords
- Grant/Revoke permissions
- Start/Stop/Restart services
- Apply patches or updates

## Response Format:
Respond with a JSON object:
{
  "is_modification": boolean,
  "reason": "string - brief explanation of why this is read-only or modification"
}

Be strict - any operation that could potentially change system state should be classified as modification.)";
}

HRESULT
RequestValidator::CreateValidationErrorResponse(
    _In_ AIMX_VALIDATION_RESULT result,
    _In_ const std::wstring& message,
    _In_ const std::wstring& reason,
    _Out_ nlohmann::json& responseJson
    )
/*++

Routine Description:
    Create a standardized validation error response.

Arguments:
    result - The validation result type
    message - User-friendly error message
    reason - Detailed reason for the validation failure
    responseJson - Output JSON response

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    try
    {
        responseJson = nlohmann::json::object();
        responseJson[AimxConstants::JsonFields::AIMX_JSON_KEY_STATUS] = "validation_failed";
        responseJson[AimxConstants::JsonFields::AIMX_JSON_KEY_MESSAGE] = WideToUtf8(message);
        responseJson["validation_result"] = static_cast<int>(result);
        responseJson["detailed_reason"] = WideToUtf8(reason);
        responseJson[AimxConstants::JsonFields::AIMX_JSON_KEY_TIMESTAMP] = std::time(nullptr);

        return S_OK;
    }
    catch (...)
    {
        return E_FAIL;
    }
}

HRESULT
RequestValidator::InitializeInternal()
/*++

Routine Description:
    Internal initialization for the RequestValidator instance.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    // No specific initialization needed for now
    // Future: Could initialize validation caches, load configuration, etc.
    return S_OK;
}
