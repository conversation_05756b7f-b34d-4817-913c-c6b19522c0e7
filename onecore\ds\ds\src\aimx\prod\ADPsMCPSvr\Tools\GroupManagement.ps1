<#
.SYNOPSIS
    Active Directory Group Management Tools for MCP Server
    
.DESCRIPTION
    This module registers MCP tools for Active Directory group management operations.
    Each tool is a direct wrapper around the corresponding AD PowerShell cmdlet with
    exact parameter passthrough and no output formatting.

.AUTHOR
    Rupo Zhang (rizhang)
#>

# Import required modules
Import-Module ActiveDirectory -ErrorAction SilentlyContinue

function Register-GroupManagementTools {
    [CmdletBinding()]
    param()

    # Get-ADGroup - Gets one or more Active Directory groups
    Register-McpTool -Name "Get-ADGroup" -Description "Gets one or more Active Directory groups. Supports filtering by various criteria and retrieving specific properties." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Filter) { $params.Filter = $Arguments.Filter }
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.LDAPFilter) { $params.LDAPFilter = $Arguments.LDAPFilter }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Properties) { $params.Properties = $Arguments.Properties }
        if ($Arguments.ResultPageSize) { $params.ResultPageSize = $Arguments.ResultPageSize }
        if ($Arguments.ResultSetSize) { $params.ResultSetSize = $Arguments.ResultSetSize }
        if ($Arguments.SearchBase) { $params.SearchBase = $Arguments.SearchBase }
        if ($Arguments.SearchScope) { $params.SearchScope = $Arguments.SearchScope }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        
        Get-ADGroup @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Filter = @{ type = "string"; description = "PowerShell Expression Language filter string" }
            Identity = @{ type = "string"; description = "Group identity (DN, GUID, SID, or SAM account name)" }
            LDAPFilter = @{ type = "string"; description = "LDAP query string for filtering" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Properties = @{ type = "array"; items = @{ type = "string" }; description = "Additional properties to retrieve" }
            ResultPageSize = @{ type = "integer"; description = "Number of objects per page" }
            ResultSetSize = @{ type = "integer"; description = "Maximum number of objects to return" }
            SearchBase = @{ type = "string"; description = "Active Directory path to search under" }
            SearchScope = @{ type = "string"; enum = @("Base", "OneLevel", "Subtree"); description = "Scope of the search" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
        }
    }

    # New-ADGroup - Creates a new Active Directory group
    Register-McpTool -Name "New-ADGroup" -Description "Creates a new Active Directory group with specified properties and attributes." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Name) { $params.Name = $Arguments.Name }
        if ($Arguments.SamAccountName) { $params.SamAccountName = $Arguments.SamAccountName }
        if ($Arguments.GroupCategory) { $params.GroupCategory = $Arguments.GroupCategory }
        if ($Arguments.GroupScope) { $params.GroupScope = $Arguments.GroupScope }
        if ($Arguments.DisplayName) { $params.DisplayName = $Arguments.DisplayName }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.Path) { $params.Path = $Arguments.Path }
        if ($Arguments.ManagedBy) { $params.ManagedBy = $Arguments.ManagedBy }
        if ($Arguments.OtherAttributes) { $params.OtherAttributes = $Arguments.OtherAttributes }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        New-ADGroup @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Name = @{ type = "string"; description = "Name of the group (required)" }
            SamAccountName = @{ type = "string"; description = "SAM account name for the group" }
            GroupCategory = @{ type = "string"; enum = @("Distribution", "Security"); description = "Group category" }
            GroupScope = @{ type = "string"; enum = @("DomainLocal", "Global", "Universal"); description = "Group scope" }
            DisplayName = @{ type = "string"; description = "Display name for the group" }
            Description = @{ type = "string"; description = "Description of the group" }
            Path = @{ type = "string"; description = "Distinguished name of the container for the group" }
            ManagedBy = @{ type = "string"; description = "Distinguished name of the group manager" }
            OtherAttributes = @{ type = "object"; description = "Additional attributes as hashtable" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the created group object" }
        }
        required = @("Name")
    }

    # Set-ADGroup - Modifies an Active Directory group
    Register-McpTool -Name "Set-ADGroup" -Description "Modifies properties of an existing Active Directory group." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Add) { $params.Add = $Arguments.Add }
        if ($Arguments.Clear) { $params.Clear = $Arguments.Clear }
        if ($Arguments.Remove) { $params.Remove = $Arguments.Remove }
        if ($Arguments.Replace) { $params.Replace = $Arguments.Replace }
        if ($Arguments.DisplayName) { $params.DisplayName = $Arguments.DisplayName }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.SamAccountName) { $params.SamAccountName = $Arguments.SamAccountName }
        if ($Arguments.GroupCategory) { $params.GroupCategory = $Arguments.GroupCategory }
        if ($Arguments.GroupScope) { $params.GroupScope = $Arguments.GroupScope }
        if ($Arguments.ManagedBy) { $params.ManagedBy = $Arguments.ManagedBy }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        
        Set-ADGroup @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Group identity (DN, GUID, SID, or SAM account name)" }
            Add = @{ type = "object"; description = "Attributes to add as hashtable" }
            Clear = @{ type = "array"; items = @{ type = "string" }; description = "Attributes to clear" }
            Remove = @{ type = "object"; description = "Attributes to remove as hashtable" }
            Replace = @{ type = "object"; description = "Attributes to replace as hashtable" }
            DisplayName = @{ type = "string"; description = "Display name for the group" }
            Description = @{ type = "string"; description = "Description of the group" }
            SamAccountName = @{ type = "string"; description = "SAM account name for the group" }
            GroupCategory = @{ type = "string"; enum = @("Distribution", "Security"); description = "Group category" }
            GroupScope = @{ type = "string"; enum = @("DomainLocal", "Global", "Universal"); description = "Group scope" }
            ManagedBy = @{ type = "string"; description = "Distinguished name of the group manager" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified group object" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
        }
        required = @("Identity")
    }

    # Remove-ADGroup - Removes an Active Directory group
    Register-McpTool -Name "Remove-ADGroup" -Description "Removes an Active Directory group from the directory." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.Confirm) { $params.Confirm = $Arguments.Confirm }
        if ($Arguments.WhatIf) { $params.WhatIf = $Arguments.WhatIf }
        
        Remove-ADGroup @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Group identity (DN, GUID, SID, or SAM account name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            Confirm = @{ type = "boolean"; description = "Prompt for confirmation before removing" }
            WhatIf = @{ type = "boolean"; description = "Show what would happen without executing" }
        }
        required = @("Identity")
    }

    # Add-ADGroupMember - Adds one or more members to an Active Directory group
    Register-McpTool -Name "Add-ADGroupMember" -Description "Adds one or more members to an Active Directory group." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Members) { $params.Members = $Arguments.Members }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Add-ADGroupMember @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Group identity (DN, GUID, SID, or SAM account name)" }
            Members = @{ type = "array"; items = @{ type = "string" }; description = "Array of member identities to add" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            PassThru = @{ type = "boolean"; description = "Return the group object" }
        }
        required = @("Identity", "Members")
    }

    # Remove-ADGroupMember - Removes one or more members from an Active Directory group
    Register-McpTool -Name "Remove-ADGroupMember" -Description "Removes one or more members from an Active Directory group." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Members) { $params.Members = $Arguments.Members }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        if ($Arguments.Confirm) { $params.Confirm = $Arguments.Confirm }
        
        Remove-ADGroupMember @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Group identity (DN, GUID, SID, or SAM account name)" }
            Members = @{ type = "array"; items = @{ type = "string" }; description = "Array of member identities to remove" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            PassThru = @{ type = "boolean"; description = "Return the group object" }
            Confirm = @{ type = "boolean"; description = "Prompt for confirmation before removing" }
        }
        required = @("Identity", "Members")
    }

    # Get-ADGroupMember - Gets the members of an Active Directory group
    Register-McpTool -Name "Get-ADGroupMember" -Description "Gets the members of an Active Directory group." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Recursive) { $params.Recursive = $Arguments.Recursive }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        
        Get-ADGroupMember @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Group identity (DN, GUID, SID, or SAM account name)" }
            Recursive = @{ type = "boolean"; description = "Get members recursively (including nested groups)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
        }
        required = @("Identity")
    }

    # Get-ADPrincipalGroupMembership - Gets the Active Directory groups that have a specified user, computer, group, or service account
    Register-McpTool -Name "Get-ADPrincipalGroupMembership" -Description "Gets the Active Directory groups that have a specified user, computer, group, or service account as a member." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.ResourceContextServer) { $params.ResourceContextServer = $Arguments.ResourceContextServer }
        if ($Arguments.ResourceContextPartition) { $params.ResourceContextPartition = $Arguments.ResourceContextPartition }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        
        Get-ADPrincipalGroupMembership @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Principal identity (DN, GUID, SID, or SAM account name)" }
            ResourceContextServer = @{ type = "string"; description = "Server for resource context" }
            ResourceContextPartition = @{ type = "string"; description = "Partition for resource context" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
        }
        required = @("Identity")
    }
}

# Function is available after dot-sourcing
