# PowerShell script to test the NetRag Service PowerShell command search functionality
# Updated to work with the current NetRag Service API endpoints (port 5000)
#
# Available endpoints tested:
# - /api/powershellcommand/search - PowerShell command semantic search
# - /api/mcptools/health - Service health check
# - /api/mcptools/statistics - Service statistics
# - /api/PowerShellExecution/health - PowerShell execution health check

param(
    [string]$BaseUrl = "http://localhost:5000",
    [string]$Query = "add user to group",
    [int]$Limit = 5
)

# Load System.Web for URL encoding
Add-Type -AssemblyName System.Web

Write-Host "Testing PowerShell Command Search Service" -ForegroundColor Green
Write-Host "Base URL: $BaseUrl" -ForegroundColor Yellow
Write-Host "=" * 50

# Test health check (using MCP Tools health endpoint since PowerShellCommand controller doesn't have health)
Write-Host "`n1. Testing Service Health Check..." -ForegroundColor Cyan
try {
    $healthResponse = Invoke-RestMethod -Uri "$BaseUrl/api/mcptools/health" -Method GET
    Write-Host "[PASS] Service Health Check Passed" -ForegroundColor Green
    Write-Host "Status: $($healthResponse.status)" -ForegroundColor White
    Write-Host "Service: $($healthResponse.service)" -ForegroundColor White
    Write-Host "Host: $($healthResponse.config.host):$($healthResponse.config.port)" -ForegroundColor White
}
catch {
    Write-Host "[FAIL] Service Health Check Failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Note: This may indicate the service is not running or not accessible" -ForegroundColor Yellow
}

# Test PowerShell execution health (alternative health check)
Write-Host "`n2. Testing PowerShell Execution Health..." -ForegroundColor Cyan
try {
    $psHealthResponse = Invoke-RestMethod -Uri "$BaseUrl/api/PowerShellExecution/health" -Method GET
    Write-Host "[PASS] PowerShell Execution Health Check Passed" -ForegroundColor Green
    Write-Host "Status: $($psHealthResponse.status)" -ForegroundColor White
    Write-Host "Test Execution Time: $($psHealthResponse.test_execution_time_ms)ms" -ForegroundColor White
}
catch {
    Write-Host "[FAIL] PowerShell Execution Health Check Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test statistics (using MCP Tools statistics endpoint)
Write-Host "`n3. Testing Service Statistics..." -ForegroundColor Cyan
try {
    $statsResponse = Invoke-RestMethod -Uri "$BaseUrl/api/mcptools/statistics" -Method GET
    Write-Host "[PASS] Service Statistics Retrieved" -ForegroundColor Green
    Write-Host "Total Tools: $($statsResponse.total_tools)" -ForegroundColor White
    Write-Host "Data Type: $($statsResponse.data_type)" -ForegroundColor White
    if ($statsResponse.vector_count) {
        Write-Host "Vector Count: $($statsResponse.vector_count)" -ForegroundColor White
    }
}
catch {
    Write-Host "[FAIL] Service Statistics Failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Note: Statistics endpoint may not be available or service may be empty" -ForegroundColor Yellow
}

# Test search functionality
Write-Host "`n4. Testing PowerShell Command Search..." -ForegroundColor Cyan
Write-Host "Query: '$Query'" -ForegroundColor Yellow
try {
    $searchUrl = "$BaseUrl/api/powershellcommand/search?query=$([System.Web.HttpUtility]::UrlEncode($Query))&limit=$Limit"
    $searchResponse = Invoke-RestMethod -Uri $searchUrl -Method GET

    Write-Host "[PASS] PowerShell Command Search Completed" -ForegroundColor Green
    Write-Host "Results Found: $($searchResponse.Count)" -ForegroundColor White

    if ($searchResponse.Count -gt 0) {
        Write-Host "`nTop Results:" -ForegroundColor White
        for ($i = 0; $i -lt $searchResponse.Count; $i++) {
            $result = $searchResponse[$i]
            Write-Host "  $($i + 1). $($result.commandName) (Score: $([math]::Round($result.score, 3)))" -ForegroundColor White

            # Display available properties dynamically
            if ($result.parameterCount) {
                Write-Host "     Parameters: $($result.parameterCount)" -ForegroundColor Gray
            }
            if ($result.exampleCount) {
                Write-Host "     Examples: $($result.exampleCount)" -ForegroundColor Gray
            }
            if ($result.parameterNames) {
                Write-Host "     Key Parameters: $($result.parameterNames)" -ForegroundColor Gray
            }
            if ($result.category) {
                Write-Host "     Category: $($result.category)" -ForegroundColor Gray
            }
            if ($result.verb) {
                Write-Host "     Verb: $($result.verb)" -ForegroundColor Gray
            }
            if ($result.id) {
                Write-Host "     ID: $($result.id)" -ForegroundColor DarkGray
            }

            # Show a snippet of the full text if available
            if ($result.fullText -and $result.fullText.Length -gt 0) {
                if ($result.fullText.Length -gt 150) {
                    $snippet = $result.fullText.Substring(0, 150) + "..."
                    Write-Host "     Preview: $snippet" -ForegroundColor DarkGray
                } else {
                    Write-Host "     Text: $($result.fullText)" -ForegroundColor DarkGray
                }
            }
            Write-Host ""
        }
    } else {
        Write-Host "No results found for query: '$Query'" -ForegroundColor Yellow
        Write-Host "Try a different search term like 'Get-User', 'Active Directory', or 'group management'" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "[FAIL] PowerShell Command Search Failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error Details: $($_.Exception)" -ForegroundColor Red
}

# Interactive Query Test
Write-Host "`n5. Interactive Query Test..." -ForegroundColor Cyan
Write-Host "Enter your own queries to search for PowerShell commands" -ForegroundColor White

do {
    Write-Host "`n" + "-" * 40
    $userQuery = Read-Host "Enter search query (or 'quit' to exit)"

    if ($userQuery -eq 'quit' -or $userQuery -eq 'q' -or [string]::IsNullOrWhiteSpace($userQuery)) {
        break
    }

    $userLimit = Read-Host "Enter number of results to return (default: 5, max: 20)"
    if ([string]::IsNullOrWhiteSpace($userLimit) -or -not [int]::TryParse($userLimit, [ref]$null)) {
        $userLimit = 5
    } else {
        $userLimit = [math]::Min([int]$userLimit, 20)
    }

    try {
        Write-Host "`nSearching for: '$userQuery' (limit: $userLimit)" -ForegroundColor Yellow
        $searchUrl = "$BaseUrl/api/powershellcommand/search?query=$([System.Web.HttpUtility]::UrlEncode($userQuery))&limit=$userLimit"
        $searchResponse = Invoke-RestMethod -Uri $searchUrl -Method GET

        Write-Host "[PASS] Search completed. Found $($searchResponse.Count) results" -ForegroundColor Green

        if ($searchResponse.Count -gt 0) {
            # Ask if user wants full JSON or summary
            $outputChoice = Read-Host "`nOutput format: (s)ummary or (j)son? (default: summary)"

            if ($outputChoice -eq 'j' -or $outputChoice -eq 'json') {
                # Output full JSON
                Write-Host "`nFull JSON Response:" -ForegroundColor White
                $searchResponse | ConvertTo-Json -Depth 10 | Write-Host -ForegroundColor Gray
            } else {
                # Output summary
                Write-Host "`nSearch Results:" -ForegroundColor White
                for ($i = 0; $i -lt $searchResponse.Count; $i++) {
                    $result = $searchResponse[$i]
                    Write-Host "`n$($i + 1). $($result.commandName)" -ForegroundColor White
                    Write-Host "   Score: $([math]::Round($result.score, 4))" -ForegroundColor Gray

                    # Display available properties dynamically
                    if ($result.parameterCount) {
                        Write-Host "   Parameters: $($result.parameterCount)" -ForegroundColor Gray
                    }
                    if ($result.exampleCount) {
                        Write-Host "   Examples: $($result.exampleCount)" -ForegroundColor Gray
                    }
                    if ($result.category) {
                        Write-Host "   Category: $($result.category)" -ForegroundColor Gray
                    }
                    if ($result.verb) {
                        Write-Host "   Verb: $($result.verb)" -ForegroundColor Gray
                    }
                    if ($result.id) {
                        Write-Host "   ID: $($result.id)" -ForegroundColor DarkGray
                    }
                    if ($result.parameterNames) {
                        Write-Host "   Key Parameters: $($result.parameterNames)" -ForegroundColor Gray
                    }

                    # Show first few lines of full text
                    if ($result.fullText -and $result.fullText.Length -gt 0) {
                        $lines = $result.fullText -split "`n" | Select-Object -First 3
                        Write-Host "   Preview:" -ForegroundColor Gray
                        foreach ($line in $lines) {
                            if (-not [string]::IsNullOrWhiteSpace($line)) {
                                Write-Host "     $($line.Trim())" -ForegroundColor DarkGray
                            }
                        }
                    }
                }
            }
        } else {
            Write-Host "No results found for query: '$userQuery'" -ForegroundColor Yellow
            Write-Host "Try different search terms like:" -ForegroundColor Yellow
            Write-Host "  - 'Get-User' or 'New-User' for user management" -ForegroundColor Yellow
            Write-Host "  - 'Active Directory' for AD-related commands" -ForegroundColor Yellow
            Write-Host "  - 'group management' for group operations" -ForegroundColor Yellow
            Write-Host "  - 'password' for password-related commands" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "[FAIL] Search failed: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Error Details: $($_.Exception)" -ForegroundColor Red
        Write-Host "Please check if the service is running and accessible at $BaseUrl" -ForegroundColor Yellow
    }

} while ($true)

Write-Host "`n" + "=" * 50
Write-Host "PowerShell Command Search Test Completed!" -ForegroundColor Green
