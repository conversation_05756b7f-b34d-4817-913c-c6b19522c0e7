/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    ragbuilder.js

Abstract:

    This module implements the JavaScript functionality for the RAG Builder UI.
    Handles user interactions, file selection, and communication with the native
    WebView host.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 03/23/2025

--*/

// Log that the script file has been loaded
console.log("RAG Builder JS file loaded");

// Self-invoking function to ensure code execution
(function() {
    console.log("RAG Builder self-invoking function executed");
    
    function initializeRagBuilder() {
        console.log("Initializing RAG Builder components");
        
        // Get main UI elements
        const progressBar = document.getElementById('progressBar');
        const statusText = document.getElementById('statusText');
        const buildButton = document.getElementById('buildButton');
        const logOutput = document.getElementById('logOutput');
        const docsDir = document.getElementById('docsDir');
        const pathDisplay = document.getElementById('pathDisplay');
        
        // Get configuration toggle elements
        const configToggle = document.getElementById('configToggle');
        const configContent = document.getElementById('configContent');
        
        // Get file selection mode elements
        const folderModeButton = document.getElementById('folderModeButton');
        const filesModeButton = document.getElementById('filesModeButton');
        
        // Get document list elements
        const documentList = document.getElementById('documentList');
        const documentCount = document.getElementById('documentCount');
        const documentItems = document.getElementById('documentItems');
        
        // Keep track of selected files
        let selectedFiles = [];
        let currentMode = 'folder'; // Default mode: 'folder' or 'files'

        // Debug function
        function debugLog(message) {
            console.log("[DEBUG] " + message);
            log(message);
        }
        
        // Setup configuration toggle
        configToggle.addEventListener('click', function() {
            this.classList.toggle('active');
            configContent.style.display = configContent.style.display === 'block' ? 'none' : 'block';
        });
        
        // Toggle file selection mode and trigger file selection dialogs
        folderModeButton.addEventListener('click', function() {
            if (!this.classList.contains('active')) {
                this.classList.add('active');
                filesModeButton.classList.remove('active');
                currentMode = 'folder';
                debugLog("Switched to folder selection mode");
            }
            // Request native folder browser instead of using input element
            sendToNative({
                type: "ragBuilder",
                action: "browse",
                mode: "folder"
            });
        });
        
        filesModeButton.addEventListener('click', function() {
            if (!this.classList.contains('active')) {
                this.classList.add('active');
                folderModeButton.classList.remove('active');
                currentMode = 'files';
                debugLog("Switched to files selection mode");
            }
            // Request native file browser instead of using input element
            sendToNative({
                type: "ragBuilder",
                action: "browse",
                mode: "files"
            });
        });
        
        // Register listener for messages from the native code
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.addEventListener('message', function(event) {
                const message = event.data;
                
                if (typeof message === 'string') {
                    try {
                        const data = JSON.parse(message);
                        handleNativeMessage(data);
                    } catch (e) {
                        log("ERROR: Failed to parse message from native code: " + e.message);
                    }
                } else {
                    handleNativeMessage(message);
                }
            });
            debugLog("WebView message listener registered");
        } else {
            debugLog("ERROR: WebView API not available!");
        }
        
        // Function to display the selected file list
        function displayFileList(files) {
            if (!files || files.length === 0) {
                documentList.style.display = 'none';
                return;
            }
            
            // Show the document list
            documentList.style.display = 'block';
            
            // Update count text
            documentCount.textContent = `${files.length} file(s) selected`;
            
            // Clear previous items
            documentItems.innerHTML = '';
            
            // Add each file to the list
            const maxFilesToShow = 100; // Prevent UI slowdown with too many files
            const filesToShow = files.length > maxFilesToShow ? files.slice(0, maxFilesToShow) : files;
            
            filesToShow.forEach((file, index) => {
                const item = document.createElement('div');
                item.className = 'document-item';
                
                // Get file name and details
                const fileName = file.name || (typeof file === 'string' ? file.split(/[\/\\]/).pop() : "Unknown");
                const filePath = file.path || (typeof file === 'string' ? file : "");
                
                // Determine file extension from name or path
                const extension = fileName.split('.').pop().toLowerCase();
                
                // Determine file size if available
                const size = file.size || 0;
                const sizeInKB = Math.round(size / 1024);
                const sizeText = sizeInKB < 1024 ? `${sizeInKB} KB` : `${(sizeInKB / 1024).toFixed(1)} MB`;
                
                item.innerHTML = `
                    <div class="document-name" title="${filePath}">${fileName}</div>
                    <div class="document-type">${extension}</div>
                    <div class="document-size">${sizeText}</div>
                    <div class="document-remove" data-index="${index}">✕</div>
                `;
                
                documentItems.appendChild(item);
            });
            
            // Show message if we're not showing all files
            if (files.length > maxFilesToShow) {
                const moreItem = document.createElement('div');
                moreItem.className = 'document-item';
                moreItem.innerHTML = `<div class="document-name">... and ${files.length - maxFilesToShow} more files</div>`;
                documentItems.appendChild(moreItem);
            }
        }
        
        // Add event delegation for remove buttons
        documentItems.addEventListener('click', function(e) {
            // Check if the clicked element is a remove button
            if (e.target.classList.contains('document-remove')) {
                const index = parseInt(e.target.dataset.index);
                removeFile(index);
            }
        });
        
        // Function to remove a file from the selection
        function removeFile(displayIndex) {
            // Get the file to remove from the displayed list
            const fileToRemove = selectedFiles[displayIndex];
            
            // Remove the file from the array
            if (displayIndex >= 0 && displayIndex < selectedFiles.length) {
                selectedFiles.splice(displayIndex, 1);
                
                // Update UI
                if (selectedFiles.length === 0) {
                    // Clear everything if no files left
                    docsDir.value = "";
                    pathDisplay.textContent = "";
                    pathDisplay.style.display = 'none';
                    documentList.style.display = 'none';
                } else {
                    // Update the input field text
                    docsDir.value = selectedFiles.length > 1 ? 
                        `${selectedFiles.length} files selected` : 
                        selectedFiles[0].name;
                    
                    // Update the path display
                    pathDisplay.textContent = `Selected ${selectedFiles.length} file(s)`;
                    
                    // Refresh the document list
                    displayFileList(selectedFiles);
                }
                
                debugLog(`File removed, ${selectedFiles.length} files remaining`);
            }
        }
        
        // Handle messages from native code
        function handleNativeMessage(message) {
            if (!message || !message.type) {
                log("ERROR: Received invalid message from native code");
                return;
            }
            
            // Only process RAG builder related messages
            if (message.type !== "ragBuilder") {
                return;
            }
            
            switch (message.action) {
                case "progress":
                    updateProgress(message.percent, message.status);
                    log(message.status);
                    
                    // When progress reaches 100%, mark as completed
                    if (message.percent >= 100) {
                        markProgressAsCompleted();
                    }
                    break;
                    
                case "buildStarted":
                    log("RAG database build started");
                    buildButton.disabled = true;
                    folderModeButton.disabled = true;
                    filesModeButton.disabled = true;
                    // Reset progress bar to active state
                    progressBar.classList.remove('completed');
                    break;
                    
                case "buildResponse":
                    buildButton.disabled = false;
                    folderModeButton.disabled = false;
                    filesModeButton.disabled = false;
                    
                    // Mark progress as completed regardless of success or failure
                    markProgressAsCompleted();
                    
                    if (message.success) {
                        log("SUCCESS: " + message.message);
                    } else {
                        log("ERROR: " + message.message);
                    }
                    break;
                    
                case "browseResponse":
                    if (message.success) {
                        if (message.files && Array.isArray(message.files)) {
                            // Handle multiple files selected from native browser
                            selectedFiles = message.files;
                            
                            const fileNames = selectedFiles.map(f => f.name);
                            const displayNames = fileNames.length > 3 ? 
                                                fileNames.slice(0, 3).join(", ") + "..." : 
                                                fileNames.join(", ");
                            
                            docsDir.value = displayNames;
                            pathDisplay.textContent = `Selected ${message.files.length} file(s)`;
                            pathDisplay.style.display = 'block';
                            
                            log(`Received ${message.files.length} files from native browser`);
                            
                            // Log a sample of the paths received
                            if (message.files.length > 0) {
                                log(`Sample path: ${message.files[0].path}`);
                            }
                            
                            // Display the file list in the UI
                            displayFileList(selectedFiles);
                            
                        } else if (message.path) {
                            // Handle folder path from native browser
                            docsDir.value = message.path;
                            pathDisplay.textContent = `Selected folder: ${message.path}`;
                            pathDisplay.style.display = 'block';
                            log("Selected directory: " + message.path);
                        }
                    } else {
                        log(message.message || "Browse operation cancelled");
                    }
                    break;
                    
                case "statusResponse":
                    log("Current build status: " + (message.building ? "Build in progress" : "Ready"));
                    if (message.building) {
                        buildButton.disabled = true;
                        folderModeButton.disabled = true;
                        filesModeButton.disabled = true;
                    }
                    break;
                    
                default:
                    log("Received unknown action: " + message.action);
            }
        }
        
        // Start the build process
        buildButton.addEventListener('click', startBuildProcess);
        
        function startBuildProcess() {
            // Get configuration values
            const outputDir = document.getElementById('outputDir').value;
            const databaseName = document.getElementById('databaseName').value;
            const chunkSize = parseInt(document.getElementById('chunkSize').value);
            
            if ((currentMode === 'folder' && !docsDir.value) || 
                (currentMode === 'files' && selectedFiles.length === 0)) {
                log("ERROR: Please select documents to process");
                Toastify({
                    text: "Please select documents to process",
                    duration: 3000,
                    backgroundColor: "linear-gradient(to right, #ff416c, #ff4b2b)"
                }).showToast();
                return;
            }
            
            if (isNaN(chunkSize) || chunkSize < 64 || chunkSize > 1024) {
                log("ERROR: Chunk size must be between 64 and 1024");
                return;
            }
            
            log("Starting RAG database build process");
            log(`Output directory: ${outputDir}`);
            log(`Chunk size: ${chunkSize}`);
            
            // Prepare the document path information
            let docsPaths;
            
            if (currentMode === 'folder') {
                // For folder mode, use the directory path
                docsPaths = docsDir.value;
                log(`Processing folder: ${docsPaths}`);
            } else {
                // For file mode, just pass the file paths - native code will handle them
                docsPaths = selectedFiles.map(file => file.path).join(';');
                if (selectedFiles.length === 1) {
                    docsPaths = docsPaths + ";";
                }
                log(`Processing ${selectedFiles.length} individual files`);
            }
            
            // Reset progress
            updateProgress(0, "Initializing...");
            
            // Send build request to native code
            sendToNative({
                type: "ragBuilder",
                action: "build",
                outputDir: outputDir,
                docsDir: docsPaths,
                databaseName: databaseName,
                chunkSize: chunkSize,
                fileCount: currentMode === 'files' ? selectedFiles.length : 0,
                mode: currentMode
            });
        }
        
        // Send a message to the native code
        function sendToNative(message) {
            if (window.chrome && window.chrome.webview) {
                window.chrome.webview.postMessage(message);
            } else {
                log("ERROR: WebView2 communication not available");
            }
        }
        
        // Update the progress bar and status text
        function updateProgress(percent, status) {
            progressBar.style.width = `${percent}%`;
            statusText.textContent = status;
            
            // If progress reaches 100%, mark as completed
            if (percent >= 100) {
                markProgressAsCompleted();
            }
        }
        
        // New function to mark progress as completed
        function markProgressAsCompleted() {
            progressBar.classList.add('completed');
            console.log("Progress marked as completed, animation stopped");
        }
        
        // Add a log message
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            if (logOutput) {
                logOutput.innerHTML += `[${timestamp}] ${message}\n`;
                logOutput.scrollTop = logOutput.scrollHeight;
            }
            console.log(message);
        }
        
        // Check build status on page load
        function checkBuildStatus() {
            sendToNative({
                type: "ragBuilder",
                action: "status"
            });
        }
        
        // Initialize by setting default states
        configContent.style.display = 'none'; // Hide configuration by default
        
        // Initialize by checking current status
        checkBuildStatus();
        
        debugLog("RAG Builder initialized successfully!");
    }

    // Wait for DOM to be ready, and then initialize
    if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", initializeRagBuilder);
        console.log("Added DOMContentLoaded listener for RAG Builder");
    } else {
        console.log("Document already loaded, initializing RAG Builder immediately");
        initializeRagBuilder();
    }
})();
