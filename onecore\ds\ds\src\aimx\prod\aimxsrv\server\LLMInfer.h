/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    LLMInfer.h

Abstract:

    Header file for the LLM Inference component that handles all LLM inference operations.

    - Standardized MCP tool and server communication. 
    - Processes user input with system prompts,
    - determines required MCP tools, and generates responses using LLM with context.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/11/2025

--*/

#pragma once

#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include <shared_mutex>
#include "AimxCommon.h"
#include "nlohmann/json.hpp"
#include "McpSvrMgr.h"
#include "AimxLlmConfig.h"
#include "SystemPromptManager.h"
#include "PshManager.h"

// Forward declarations
class ConversationSession;

// Tool requirement structure with confidence scoring
struct TOOL_REQUIREMENT
{
    std::wstring serverName;
    GUID serverId;
    std::wstring toolName;
    nlohmann::json parameters;
    float confidence;
    std::wstring reasoning;
    bool isRequired;
};

// LLM analysis result structure
struct LLM_ANALYSIS_RESULT
{
    std::vector<TOOL_REQUIREMENT> requiredTools;
    std::wstring analysisReasoning;
    float overallConfidence;
    bool requiresUserConfirmation;
    std::wstring suggestedApproach;
    std::vector<std::wstring> potentialRisks;
};

// LLM response generation result
struct LLM_RESPONSE_RESULT
{
    std::wstring response;
    std::wstring reasoning;
    bool isComplete;
    std::vector<std::wstring> followUpSuggestions;
    std::vector<std::wstring> additionalQuestions;
};

// LLM Inference component class
class LLMInfer
{
public:
    // Initialize the LLM Inference component (uses global AimxLlmConfig)
    static HRESULT Initialize();

    // Uninitialize and cleanup resources
    static void Uninitialize();

    // Analyze user query to determine required tools using Cline-style prompting
    static HRESULT AnalyzeUserQuery(
        _In_ const std::wstring& userQuery,
        _In_ const std::vector<MCP_TOOL_INFO>& availableTools,
        _Out_ LLM_ANALYSIS_RESULT& analysisResult,
        _In_opt_ std::shared_ptr<ConversationSession> conversationSession = nullptr
        );

    // Analyze PowerShell query to generate final command with rich context
    static HRESULT AnalyzePowerShellQuery(
        _In_ const std::wstring& userQuery,
        _In_ const std::wstring& selectedCommand,
        _In_ const nlohmann::json& commandContext,
        _Out_ LLM_ANALYSIS_RESULT& analysisResult,
        _In_opt_ std::shared_ptr<ConversationSession> conversationSession = nullptr
        );

    // Generate response from original query and tool results
    static HRESULT GenerateResponse(
        _In_ const std::wstring& originalQuery,
        _In_ const std::wstring& toolResults,
        _Out_ LLM_RESPONSE_RESULT& responseResult,
        _In_opt_ std::shared_ptr<ConversationSession> conversationSession = nullptr
        );

    // Generate response with structured tool results
    static HRESULT GenerateResponseFromStructuredResults(
        _In_ const std::wstring& originalQuery,
        _In_ const std::vector<nlohmann::json>& toolResults,
        _Out_ LLM_RESPONSE_RESULT& responseResult
        );

    // Validate and refine tool parameters using LLM
    static HRESULT RefineToolParameters(
        _In_ const std::wstring& userQuery,
        _In_ const MCP_TOOL_INFO& toolInfo,
        _In_ const nlohmann::json& initialParameters,
        _Out_ nlohmann::json& refinedParameters
        );
   
    // Test LLM connectivity
    static HRESULT TestLlmConnectivity(
        _Out_ std::wstring& testResult
        );

    // Common LLM communication methods
    HRESULT SendLlmRequest(
        _In_ const std::wstring& systemPrompt,
        _In_ const std::wstring& userPrompt,
        _Out_ std::wstring& response
        );


    // Get the singleton instance
    static LLMInfer* GetInstance();

private:
    // Private constructor for singleton pattern
    LLMInfer();
    
    // Private destructor
    ~LLMInfer();

    // Delete copy constructor and assignment operator
    LLMInfer(const LLMInfer&) = delete;
    LLMInfer& operator=(const LLMInfer&) = delete;

    // Internal initialization (uses global AimxLlmConfig)
    HRESULT InitializeInternal();

    // Helper method to extract LLM content from HTTP response JSON
    HRESULT ExtractLlmContentFromHttpResponse(
        _In_ const std::wstring& httpResponseBody,
        _Out_ std::wstring& llmContent
        );

    // Response parsing methods
    HRESULT ParseToolAnalysisResponse(
        _In_ const std::wstring& llmResponse,
        _Out_ LLM_ANALYSIS_RESULT& analysisResult,
        _In_opt_ std::shared_ptr<ConversationSession> conversationSession = nullptr
        );

    HRESULT ParsePowerShellAnalysisResponse(
        _In_ const std::wstring& llmResponse,
        _Out_ LLM_ANALYSIS_RESULT& analysisResult,
        _In_opt_ std::shared_ptr<ConversationSession> conversationSession = nullptr
        );

    // Helper methods for PowerShell analysis
    HRESULT ExtractPowerShellCommandFromJson(
        _In_ const nlohmann::json& responseJson,
        _Out_ std::string& psCommand,
        _Out_ float& confidence,
        _Out_ std::wstring& reasoning
        );

    HRESULT ExecutePowerShellCommandAndStoreResult(
        _In_ const std::string& psCommand,
        _In_ float confidence,
        _In_ const std::wstring& reasoning,
        _Out_ LLM_ANALYSIS_RESULT& analysisResult,
        _In_opt_ std::shared_ptr<ConversationSession> conversationSession = nullptr
        );

    // Helper method to extract JSON from markdown code blocks
    static std::string ExtractJsonFromMarkdownFences(
        _In_ const std::string& response
        );

    HRESULT ParseResponseGenerationResult(
        _In_ const std::wstring& llmResponse,
        _Out_ LLM_RESPONSE_RESULT& responseResult,
        _In_opt_ std::shared_ptr<ConversationSession> conversationSession = nullptr
        );

     // Validation methods
    HRESULT ValidateToolRequirements(
        _In_ const std::vector<TOOL_REQUIREMENT>& requirements,
        _In_ const std::vector<MCP_TOOL_INFO>& availableTools
        );

    // Internal data structures
    static LLMInfer* s_instance;
    static std::mutex s_instanceMutex;

    std::unique_ptr<SystemPromptManager> m_promptManager;
    bool m_initialized;
};
