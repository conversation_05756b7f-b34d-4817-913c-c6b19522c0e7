/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    InProcessMcpUtils.h

Abstract:

    Utility functions for in-process MCP servers.
    Provides JSON blob creation and parsing functions that maintain
    full MCP protocol compliance for direct API calls.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/12/2025

--*/

#pragma once

#include <nlohmann/json.hpp>
#include <vector>
#include <string>
#include <windows.h>
#include "../../McpProtocolLib/McpJsonRpc.h"

// Utility functions for common MCP JSON operations
namespace InProcessMcpUtils
{
    // Create MCP-compliant tools/list response
    nlohmann::json CreateToolsListResponse(
        _In_ const std::vector<nlohmann::json>& tools
        );

    // Create MCP-compliant tools/call success response
    nlohmann::json CreateToolCallSuccessResponse(
        _In_ const nlohmann::json& content
        );

    // Create MCP-compliant error response
    nlohmann::json CreateMcpErrorResponse(
        _In_ int errorCode,
        _In_ const std::string& message,
        _In_opt_ const nlohmann::json* data = nullptr
        );

    // Parse tools/call request to extract tool name and parameters
    HRESULT ParseToolCallRequest(
        _In_ const nlohmann::json& request,
        _Out_ std::string& toolName,
        _Out_ nlohmann::json& parameters
        );

    // Convert C++ exception to MCP error response
    nlohmann::json ExceptionToMcpError(
        _In_ const std::exception& ex
        );
}
