/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    main.cpp

Abstract:

    Main entry point for the RAG service Windows service.
    Handles service installation, uninstallation, and startup.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 04/13/2025

--*/

#include "RagService.h"
#include "../common/debug.h"
#include <windows.h>
#include <stdio.h>
#include <string>

// Service name
#define SERVICE_NAME L"RagService"
#define SERVICE_DISPLAY_NAME L"RAG Database Builder Service"
#define SERVICE_DESCRIPTION L"Provides HTTP endpoint for RAG database building and management"

// Application constants
const static std::string SRV_LOG_FILENAME = "ragsrv.log";

// Function prototypes
BOOL InstallService();
BOOL UninstallService();
void PrintUsage();

/*++

Routine Description:

    Main entry point for the RAG service.

Arguments:

    argc - Number of command-line arguments.
    argv - Array of command-line argument strings.

Return Value:

    int - Exit code.

--*/
int __cdecl
wmain(
    _In_ int argc, 
    _In_reads_(argc) wchar_t *argv[]
)
{
    // Initialize debug logging
    Debug::GetInstance(SRV_LOG_FILENAME);
    LOGINFO("RAG Service: Starting - log file: " + Debug::GetInstance().GetCurrentLogFileName());

    if (argc > 1)
    {
        if (_wcsicmp(argv[1], L"-install") == 0 || _wcsicmp(argv[1], L"/install") == 0)
        {
            return InstallService() ? 0 : 1;
        }
        else if (_wcsicmp(argv[1], L"-uninstall") == 0 || _wcsicmp(argv[1], L"/uninstall") == 0)
        {
            return UninstallService() ? 0 : 1;
        }
        else if (_wcsicmp(argv[1], L"-help") == 0 || _wcsicmp(argv[1], L"/help") == 0 || 
                _wcsicmp(argv[1], L"-h") == 0 || _wcsicmp(argv[1], L"/?") == 0)
        {
            PrintUsage();
            return 0;
        }
    }

    // Service table with our service entry
    SERVICE_TABLE_ENTRYW ServiceTable[] = 
    {
        { const_cast<LPWSTR>(SERVICE_NAME), RagService::ServiceMain },
        { NULL, NULL }
    };

    // Start the service dispatcher
    if (!StartServiceCtrlDispatcherW(ServiceTable))
    {
        DWORD error = GetLastError();
        
        if (error == ERROR_FAILED_SERVICE_CONTROLLER_CONNECT)
        {
            // Not started as a service, print usage
            printf("Error: Cannot start as standalone application.\n");
            PrintUsage();
        }
        else
        {
            LOGERROR("RAG Service: StartServiceCtrlDispatcher failed: %d", error);
            printf("Error: Service dispatcher failed with error %d\n", error);
        }
        
        return 1;
    }

    LOGINFO("RAG Service: Exiting normally");
    return 0;
}

/*++

Routine Description:

    Installs the RAG service.

Arguments:

    None.

Return Value:

    BOOL - TRUE if the service was installed, FALSE otherwise.

--*/
BOOL 
InstallService()
{
    WCHAR szPath[MAX_PATH];
    
    // Get the path to this executable
    if (GetModuleFileNameW(NULL, szPath, ARRAYSIZE(szPath)) == 0)
    {
        DWORD error = GetLastError();
        LOGERROR("RAG Service: GetModuleFileName failed: %d", error);
        printf("Error: Failed to get module filename: %d\n", error);
        return FALSE;
    }

    // Open the service control manager
    SC_HANDLE schSCManager = OpenSCManagerW(NULL, NULL, SC_MANAGER_CREATE_SERVICE);
    if (schSCManager == NULL)
    {
        DWORD error = GetLastError();
        LOGERROR("RAG Service: OpenSCManager failed: %d", error);
        printf("Error: Failed to open service control manager: %d\n", error);
        return FALSE;
    }

    // Create the service
    SC_HANDLE schService = CreateServiceW(
        schSCManager,                   // Service Control Manager
        SERVICE_NAME,                   // Service name
        SERVICE_DISPLAY_NAME,           // Service display name
        SERVICE_ALL_ACCESS,             // Desired access
        SERVICE_WIN32_OWN_PROCESS,      // Service type
        SERVICE_AUTO_START,             // Start type
        SERVICE_ERROR_NORMAL,           // Error control
        szPath,                         // Binary path
        NULL,                           // No load ordering group
        NULL,                           // No tag identifier
        NULL,                           // No dependencies
        NULL,                           // Local system account
        NULL                            // No password
    );

    if (schService == NULL)
    {
        DWORD error = GetLastError();
        CloseServiceHandle(schSCManager);
        
        if (error == ERROR_SERVICE_EXISTS)
        {
            LOGINFO("RAG Service: Service already exists");
            printf("The RAG service is already installed.\n");
            return TRUE;
        }
        else
        {
            LOGERROR("RAG Service: CreateService failed: %d", error);
            printf("Error: Failed to create service: %d\n", error);
            return FALSE;
        }
    }

    // Set the service description
    SERVICE_DESCRIPTIONW sd = {};
    sd.lpDescription = const_cast<LPWSTR>(SERVICE_DESCRIPTION);
    
    if (!ChangeServiceConfig2W(schService, SERVICE_CONFIG_DESCRIPTION, &sd))
    {
        LOGERROR("RAG Service: ChangeServiceConfig2 failed: %d", GetLastError());
        // Non-critical error, continue
    }

    // Close handles
    CloseServiceHandle(schService);
    CloseServiceHandle(schSCManager);

    LOGINFO("RAG Service: Service installed successfully");
    printf("The RAG service was installed successfully.\n");

    return TRUE;
}

/*++

Routine Description:

    Uninstalls the RAG service.

Arguments:

    None.

Return Value:

    BOOL - TRUE if the service was uninstalled, FALSE otherwise.

--*/
BOOL 
UninstallService()
{
    // Open the service control manager
    SC_HANDLE schSCManager = OpenSCManagerW(NULL, NULL, SC_MANAGER_ALL_ACCESS);
    if (schSCManager == NULL)
    {
        DWORD error = GetLastError();
        LOGERROR("RAG Service: OpenSCManager failed: %d", error);
        printf("Error: Failed to open service control manager: %d\n", error);
        return FALSE;
    }

    // Open the service
    SC_HANDLE schService = OpenServiceW(
        schSCManager,
        SERVICE_NAME,
        DELETE
    );

    if (schService == NULL)
    {
        DWORD error = GetLastError();
        CloseServiceHandle(schSCManager);
        
        if (error == ERROR_SERVICE_DOES_NOT_EXIST)
        {
            LOGINFO("RAG Service: Service does not exist");
            printf("The RAG service is not installed.\n");
            return TRUE;
        }
        else
        {
            LOGERROR("RAG Service: OpenService failed: %d", error);
            printf("Error: Failed to open service: %d\n", error);
            return FALSE;
        }
    }

    // Delete the service
    if (!DeleteService(schService))
    {
        DWORD error = GetLastError();
        CloseServiceHandle(schService);
        CloseServiceHandle(schSCManager);
        
        LOGERROR("RAG Service: DeleteService failed: %d", error);
        printf("Error: Failed to delete service: %d\n", error);
        return FALSE;
    }

    // Close handles
    CloseServiceHandle(schService);
    CloseServiceHandle(schSCManager);

    LOGINFO("RAG Service: Service uninstalled successfully");
    printf("The RAG service was uninstalled successfully.\n");

    return TRUE;
}

/*++

Routine Description:

    Prints the usage information.

Arguments:

    None.

Return Value:

    None.

--*/
void 
PrintUsage()
{
    printf("RAG Database Builder Service\n");
    printf("Usage:\n");
    printf("  ragsrv.exe                  - Run as a service\n");
    printf("  ragsrv.exe -install         - Install the service\n");
    printf("  ragsrv.exe -uninstall       - Uninstall the service\n");
    printf("  ragsrv.exe -help            - Display this help\n");
}