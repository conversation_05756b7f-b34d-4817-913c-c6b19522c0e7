using System;
using System.Collections.Generic;
using System.Management.Automation;
using System.Runtime.InteropServices;
using System.Threading;

namespace Microsoft.Windows.AIMX
{
    // Delegate for conversation message callbacks (exactly like AimxProcessPrompt - single string)
    [UnmanagedFunctionPointer(CallingConvention.StdCall)]
    public delegate void ConversationMessageDelegate(IntPtr messageStringPtr);

    // Message types enum
    public enum ConversationMessageType
    {
        UserInput = 1,
        AssistantResponse = 2,
        ProgressUpdate = 3,
        TaskBreakdown = 4,
        ApprovalRequest = 5,
        ToolExecution = 6,
        ErrorMessage = 7
    }

    [Serializable]
    public class AimxServerContext
    {
        public Guid ContextId { get; set; }

        public AimxServerContext(Guid contextId) { ContextId = contextId; }
    }

    // Callback handler class
    public class ConversationCallbackHandler
    {
        private ScriptBlock _callback;
        private Cmdlet _cmdlet;
        private ConversationMessageDelegate _nativeDelegate;
        private GCHandle _delegateHandle;

        public ConversationCallbackHandler(ScriptBlock callback, Cmdlet cmdlet)
        {
            _callback = callback;
            _cmdlet = cmdlet;
            _nativeDelegate = new ConversationMessageDelegate(OnMessage);
            _delegateHandle = GCHandle.Alloc(_nativeDelegate);
        }

        public IntPtr CallbackPtr => Marshal.GetFunctionPointerForDelegate(_nativeDelegate);

        public void OnMessage(IntPtr messageStringPtr)
        {
            try
            {
                // Extract message string (exactly like AimxProcessPrompt)
                string messageText = "";
                if (messageStringPtr != IntPtr.Zero)
                {
                    messageText = Marshal.PtrToStringUni(messageStringPtr);
                    // Free the allocated string (same as AimxProcessPrompt pattern)
                    NativeMethods.AimxFree(messageStringPtr);
                }

                // Parse the message string (could be JSON or simple text)
                var psMessage = new PSObject();
                psMessage.Properties.Add(new PSNoteProperty("Content", messageText ?? ""));

                // Invoke callback
                if (_callback != null)
                {
                    _callback.InvokeWithContext(null, new List<PSVariable>(), psMessage);
                }
            }
            catch (Exception ex)
            {
                _cmdlet?.WriteError(new ErrorRecord(ex, "CallbackError", ErrorCategory.NotSpecified, null));
            }
        }

        public void Dispose()
        {
            if (_delegateHandle.IsAllocated)
            {
                _delegateHandle.Free();
            }
        }
    }

    [Cmdlet(VerbsCommunications.Connect, "AimxServer")]
    [OutputType(typeof(AimxServerContext))]
    public class ConnectAimxServerCmdlet : PSCmdlet
    {
        protected override void ProcessRecord()
        {
            Guid contextId;
            uint hr = NativeMethods.AimxConnect(out contextId);
            if (hr != 0)
            {
                ThrowTerminatingError(new ErrorRecord(
                    new Exception($"AimxConnect failed: HRESULT=0x{hr:X8}"),
                    "AimxConnectFailed",
                    ErrorCategory.NotSpecified,
                    null));
            }
            WriteObject(new AimxServerContext(contextId));
        }
    }

    [Cmdlet(VerbsCommon.Close, "AimxServer")]
    [OutputType(typeof(AimxServerContext))]
    public class CloseAimxServerCmdlet : PSCmdlet
    {
        [Parameter(Mandatory = true, Position = 0, ValueFromPipeline = true)]
        public AimxServerContext Context { get; set; }

        protected override void ProcessRecord()
        {
            Guid contextId = Context?.ContextId ?? Guid.Empty;
            uint hr = NativeMethods.AimxClose(contextId);
            if (hr != 0)
            {
                ThrowTerminatingError(new ErrorRecord(
                    new Exception($"AimxClose failed: HRESULT=0x{hr:X8}"),
                    "AimxCloseFailed",
                    ErrorCategory.NotSpecified,
                    null));
            }
            // Optionally, return the closed contextId or null
            WriteObject(null);
        }
    }

    /// <summary>
    /// Processes a single AIMX prompt and returns the response
    /// </summary>
    [Cmdlet("Get", "AimxQuestionResponse", HelpUri = "https://aka.ms/aimx-prompt-help")]
    [OutputType(typeof(string))]
    public sealed class GetAimxQuestionResponse : PSCmdlet
    {
        [Parameter(
            Mandatory = true,
            ValueFromPipeline = true,
            ValueFromPipelineByPropertyName = true)]
        public string PromptText;

        [Parameter(Mandatory = true, Position = 1, ValueFromPipelineByPropertyName = true)]
        public AimxServerContext Context { get; set; }

        /// <summary>
        /// BeginProcessing
        /// </summary>
        protected override void BeginProcessing()
        {
            // Validate that the PromptText is not null or empty
            if (string.IsNullOrWhiteSpace(PromptText))
            {
                throw new ArgumentException("PromptText cannot be null or empty.", nameof(PromptText));
            }

            WriteVerbose("BeginProcessing completed.");
        }

        protected override void ProcessRecord()
        {
            uint hr;
            string responseText = null;

            Guid contextId = Context?.ContextId ?? Guid.Empty;
            IntPtr responsePtr = IntPtr.Zero;
            hr = NativeMethods.AimxProcessPrompt(contextId, PromptText, out responsePtr);
            if (hr == 0 && responsePtr != IntPtr.Zero)
            {
                responseText = Marshal.PtrToStringUni(responsePtr);
                if (responseText == null)
                {
                    WriteError(new ErrorRecord(new InvalidOperationException("Failed to convert response pointer to string."), "ConvertResponseFailed", ErrorCategory.InvalidData, null));
                }

                // free the responsePtr using AimxFree.
                NativeMethods.AimxFree(responsePtr);
            }
            else
            {
                WriteError(new ErrorRecord(new InvalidOperationException($"Failed to process AIMX prompt. hr:0x{hr:X}"), "ProcessPromptFailed", ErrorCategory.InvalidOperation, null));
            }

            // return the response text
            WriteObject(responseText);
            WriteVerbose("AIMX prompt processed successfully.");
        }

        /// <summary>
        /// EndProcessing
        /// </summary>
        protected override void EndProcessing()
        {
        }
    }

    /// <summary>
    /// Polls for new conversation messages
    /// </summary>
    [Cmdlet("Get", "AimxConversationMessages")]
    public class GetAimxConversationMessages : PSCmdlet
    {
        [Parameter(Mandatory = true)]
        public AimxServerContext Context { get; set; }

        protected override void ProcessRecord()
        {
            try
            {
                // Poll for new conversation messages
                IntPtr messagesPtr = IntPtr.Zero;
                uint hr = NativeMethods.AimxPollConversationMessages(Context.ContextId, out messagesPtr);

                if (hr != 0)
                {
                    WriteError(new ErrorRecord(new InvalidOperationException($"Failed to poll conversation messages. hr:0x{hr:X}"), "PollMessagesFailed", ErrorCategory.InvalidOperation, null));
                    return;
                }

                string messagesJson = "";
                if (messagesPtr != IntPtr.Zero)
                {
                    messagesJson = Marshal.PtrToStringUni(messagesPtr);
                    // Free the allocated string
                    NativeMethods.AimxFree(messagesPtr);
                }

                // Return the JSON string
                WriteObject(messagesJson ?? "[]");
            }
            catch (Exception ex)
            {
                WriteError(new ErrorRecord(ex, "PollMessagesException", ErrorCategory.InvalidOperation, null));
            }
        }
    }

    /// <summary>
    /// Gets the current status of the conversation session
    /// </summary>
    [Cmdlet("Get", "AimxConversationStatus")]
    public class GetAimxConversationStatus : PSCmdlet
    {
        [Parameter(Mandatory = true)]
        public AimxServerContext Context { get; set; }

        protected override void ProcessRecord()
        {
            try
            {
                // Get conversation status
                IntPtr statusPtr = IntPtr.Zero;
                uint hr = NativeMethods.AimxGetConversationStatus(Context.ContextId, out statusPtr);

                if (hr != 0)
                {
                    WriteError(new ErrorRecord(new InvalidOperationException($"Failed to get conversation status. hr:0x{hr:X}"), "GetStatusFailed", ErrorCategory.InvalidOperation, null));
                    return;
                }

                string statusJson = "";
                if (statusPtr != IntPtr.Zero)
                {
                    statusJson = Marshal.PtrToStringUni(statusPtr);
                    // Free the allocated string
                    NativeMethods.AimxFree(statusPtr);
                }

                // Return the JSON string
                WriteObject(statusJson ?? "{}");
            }
            catch (Exception ex)
            {
                WriteError(new ErrorRecord(ex, "GetStatusException", ErrorCategory.InvalidOperation, null));
            }
        }
    }

    /// <summary>
    /// Starts an interactive conversation with real-time updates
    /// </summary>
    [Cmdlet("Start", "AimxConversation")]
    public class StartAimxConversation : PSCmdlet
    {
        [Parameter(Mandatory = true)]
        public AimxServerContext Context { get; set; }

        [Parameter(Mandatory = true)]
        public string Query { get; set; }

        [Parameter]
        public int ExecutionMode { get; set; } = 2; // Default to interactive mode

        protected override void ProcessRecord()
        {
            try
            {
                // Start conversation
                uint hr = NativeMethods.AimxStartConversation(Context.ContextId, Query, ExecutionMode);
                if (hr != 0)
                {
                    WriteError(new ErrorRecord(new InvalidOperationException($"Failed to start conversation. hr:0x{hr:X}"), "StartConversationFailed", ErrorCategory.InvalidOperation, null));
                    return;
                }

                WriteObject("Conversation started successfully. Use Get-AimxConversationMessages to poll for updates.");
            }
            catch (Exception ex)
            {
                WriteError(new ErrorRecord(ex, "StartConversationException", ErrorCategory.InvalidOperation, null));
            }
        }
    }

    /// <summary>
    /// Stops the current conversation
    /// </summary>
    [Cmdlet("Stop", "AimxConversation")]
    public class StopAimxConversation : PSCmdlet
    {
        [Parameter(Mandatory = true)]
        public AimxServerContext Context { get; set; }

        protected override void ProcessRecord()
        {
            try
            {
                // No callback cleanup needed since we use polling
                WriteObject("Conversation stopped and callbacks cleaned up.");
            }
            catch (Exception ex)
            {
                WriteError(new ErrorRecord(ex, "StopConversationException", ErrorCategory.InvalidOperation, null));
            }
        }
    }

    /// <summary>
    /// Gets the current LLM service status and connectivity information
    /// </summary>
    [Cmdlet("Get", "AimxLlmStatus")]
    public class GetAimxLlmStatus : PSCmdlet
    {
        [Parameter(Mandatory = true)]
        public AimxServerContext Context { get; set; }

        protected override void ProcessRecord()
        {
            try
            {
                // Get LLM status
                IntPtr statusPtr = IntPtr.Zero;
                uint hr = NativeMethods.AimxGetLlmStatus(Context.ContextId, out statusPtr);

                if (hr != 0)
                {
                    WriteError(new ErrorRecord(new InvalidOperationException($"Failed to get LLM status. hr:0x{hr:X}"), "GetLlmStatusFailed", ErrorCategory.InvalidOperation, null));
                    return;
                }

                string statusJson = "";
                if (statusPtr != IntPtr.Zero)
                {
                    statusJson = Marshal.PtrToStringUni(statusPtr);
                    // Free the allocated string (same as other methods)
                    NativeMethods.AimxFree(statusPtr);
                }

                // Return the JSON string (PowerShell can parse it)
                WriteObject(statusJson ?? "{}");
            }
            catch (Exception ex)
            {
                WriteError(new ErrorRecord(ex, "GetLlmStatusException", ErrorCategory.InvalidOperation, null));
            }
        }
    }

    /// <summary>
    /// Gets information about registered MCP servers and their available tools
    /// </summary>
    [Cmdlet("Get", "AimxMcpServerInfo")]
    public class GetAimxMcpServerInfo : PSCmdlet
    {
        [Parameter(Mandatory = true)]
        public AimxServerContext Context { get; set; }

        protected override void ProcessRecord()
        {
            try
            {
                // Get MCP server information
                IntPtr serverInfoPtr = IntPtr.Zero;
                uint hr = NativeMethods.AimxGetMcpServerInfo(Context.ContextId, out serverInfoPtr);

                if (hr != 0)
                {
                    WriteError(new ErrorRecord(new InvalidOperationException($"Failed to get MCP server information. hr:0x{hr:X}"), "GetMcpServerInfoFailed", ErrorCategory.InvalidOperation, null));
                    return;
                }

                string serverInfoJson = "";
                if (serverInfoPtr != IntPtr.Zero)
                {
                    serverInfoJson = Marshal.PtrToStringUni(serverInfoPtr);
                    // Free the allocated string (same as other methods)
                    NativeMethods.AimxFree(serverInfoPtr);
                }

                // Return the JSON string (PowerShell can parse it)
                WriteObject(serverInfoJson ?? "{}");
            }
            catch (Exception ex)
            {
                WriteError(new ErrorRecord(ex, "GetMcpServerInfoException", ErrorCategory.InvalidOperation, null));
            }
        }
    }
}
