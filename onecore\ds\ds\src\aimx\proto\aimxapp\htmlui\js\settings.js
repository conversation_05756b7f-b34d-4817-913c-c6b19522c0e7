/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    settings.js

Abstract:

    This module implements the settings and configuration panel for AIMX.
    Manages LLM endpoint selection, model configuration, embedding settings,
    and other system parameters, communicating with the WebView host to
    persist and apply configuration changes.

Communication Protocol:
    
    Frontend to Backend:
        1. saveSettings: Save configuration settings
           Format: {type: "saveSettings", settings: {...}}
        
        2. getSettings: Request current configuration
           Format: {type: "getSettings", content: ""}
    
    Backend to Frontend:
        1. settings: Deliver configuration settings to UI
           Format: {type: "settings", settings: {...}}

Author:

    <PERSON><PERSON><PERSON> (rizhang) 03/29/2025

--*/

// Create a settings namespace within the aimx global object
window.aimx = window.aimx || {};
window.aimx.settings = (function () {
    // Private variables
    let initialized = false;

    // DOM element references - will be initialized lazily
    const elements = {};

    // Initialize settings functionality
    function initialize() {
        if (initialized) return;

        console.log("Initializing settings panel");

        // Get DOM elements only when initialize is called
        getElements();

        // Only set up event listeners if all required elements exist
        if (validateElements()) {
            setupEventListeners();
            initialized = true;
            console.log("Settings panel initialized successfully");
        } else {
            console.error("Cannot initialize settings panel - missing required elements");
        }
    }

    // Helper to get DOM elements
    function getElements() {
        elements.settingsButton = document.getElementById('settingsButton');
        elements.settingsModal = document.getElementById('settingsModal');
        elements.closeSettingsButton = document.getElementById('closeSettingsButton');
        elements.saveSettingsButton = document.getElementById('saveSettingsButton');
        elements.cancelSettingsButton = document.getElementById('cancelSettingsButton');
        elements.tabButtons = document.querySelectorAll('.tab-button');

        console.log("Settings elements found:",
            "Modal:", !!elements.settingsModal,
            "Close button:", !!elements.closeSettingsButton,
            "Save button:", !!elements.saveSettingsButton,
            "Cancel button:", !!elements.cancelSettingsButton,
            "Tab buttons:", elements.tabButtons.length > 0,
            "Settings button", !!elements.settingsButton
        );
    }

    // Validate that required elements exist
    function validateElements() {
        return elements.settingsModal &&
            elements.closeSettingsButton &&
            elements.saveSettingsButton &&
            elements.cancelSettingsButton;
    }

    // Setup event listeners for the settings panel
    function setupEventListeners() {
        // Show settings modal (only if button exists, as it might be handled elsewhere)
        if (elements.settingsButton) {
            elements.settingsButton.addEventListener('click', function () {
                elements.settingsModal.classList.add('active');

                // Check LLM health and fetch models when settings dialog opens
                checkLLMStatus();
                fetchLLMModels();
            });
        }

        // Close settings modal
        elements.closeSettingsButton.addEventListener('click', function () {
            elements.settingsModal.classList.remove('active');
        });

        // Cancel button
        elements.cancelSettingsButton.addEventListener('click', function () {
            elements.settingsModal.classList.remove('active');
        });

        // Save settings
        elements.saveSettingsButton.addEventListener('click', function () {
            // Collect settings from form fields
            const settings = collectSettings();

            // Send settings to host application
            try {
                if (window.chrome && window.chrome.webview) {
                    window.chrome.webview.postMessage({
                        type: 'saveSettings',
                        settings: settings
                    });
                    console.log("Settings sent to host application");
                }
            } catch (e) {
                console.error("Could not send settings to host:", e);
            }

            // Close the modal
            elements.settingsModal.classList.remove('active');
        });

        // Tab navigation
        elements.tabButtons.forEach(button => {
            button.addEventListener('click', function () {
                // Get the target tab
                const tabId = this.getAttribute('data-tab');

                // Remove active class from all tab buttons
                elements.tabButtons.forEach(btn => {
                    btn.classList.remove('active');
                });

                // Add active class to clicked tab button
                this.classList.add('active');

                // Hide all tab panes
                const tabPanes = document.querySelectorAll('.tab-pane');
                tabPanes.forEach(pane => {
                    pane.classList.remove('active');
                });

                // Show target tab pane
                document.getElementById(tabId).classList.add('active');
            });
        });

        // Toggle settings sections based on radio selection
        setupRadioToggles('llmType', [
            { value: 'local', target: '.local-llm-settings' },
            { value: 'openai', target: '.openai-settings' },
            { value: 'azure', target: '.azure-settings' },
            { value: 'claude', target: '.claude-settings' }
        ]);

        setupRadioToggles('embeddingType', [
            { value: 'local', target: '.local-embedding-settings' },
            { value: 'openai', target: '.openai-embedding-settings' },
            { value: 'azure', target: '.azure-embedding-settings' }
        ]);

        // Add event listener for model selection to update model info
        const modelSelect = document.getElementById('localModel');
        if (modelSelect) {
            modelSelect.addEventListener('change', function () {
                const selectedOption = this.options[this.selectedIndex];
                const parameters = selectedOption.dataset.parameters || '-';
                const context = selectedOption.dataset.context || '-';

                document.getElementById('modelParameters').textContent = parameters;
                document.getElementById('modelContext').textContent = context;
            });
        }

        // Add event listener for endpoint input changes
        const endpointInput = document.getElementById('localEndpoint');
        if (endpointInput) {
            endpointInput.addEventListener('blur', function () {
                checkLLMStatus();
                fetchLLMModels();
            });
        }
    }

    // Helper function to set up radio button toggles
    function setupRadioToggles(name, mappings) {
        const radioButtons = document.querySelectorAll(`input[name="${name}"]`);

        // Hide all target sections initially
        mappings.forEach(mapping => {
            const sections = document.querySelectorAll(mapping.target);
            sections.forEach(section => {
                section.style.display = 'none';
            });
        });

        // Show the section for the checked radio button
        const checkedButton = document.querySelector(`input[name="${name}"]:checked`);
        if (checkedButton) {
            const mapping = mappings.find(m => m.value === checkedButton.value);
            if (mapping) {
                const sections = document.querySelectorAll(mapping.target);
                sections.forEach(section => {
                    section.style.display = 'block';
                });
            }
        }

        // Add event listeners to radio buttons
        radioButtons.forEach(radio => {
            radio.addEventListener('change', function () {
                // Hide all sections
                mappings.forEach(mapping => {
                    const sections = document.querySelectorAll(mapping.target);
                    sections.forEach(section => {
                        section.style.display = 'none';
                    });
                });

                // Show the section for the selected radio button
                const mapping = mappings.find(m => m.value === this.value);
                if (mapping) {
                    const sections = document.querySelectorAll(mapping.target);
                    sections.forEach(section => {
                        section.style.display = 'block';
                    });
                }
            });
        });
    }

    // Function to collect settings from form fields
    function collectSettings() {
        const settings = {
            llm: {
                type: document.querySelector('input[name="llmType"]:checked').value
            },
            embedding: {
                type: document.querySelector('input[name="embeddingType"]:checked').value
            },
            advanced: {
                chunkSize: parseInt(document.getElementById('chunkSize').value),
                chunkOverlap: parseInt(document.getElementById('chunkOverlap').value),
                maxConcurrentRequests: parseInt(document.getElementById('maxConcurrentRequests').value),
                debugMode: document.getElementById('debugMode').value
            }
        };

        // Add LLM-specific settings
        switch (settings.llm.type) {
            case 'local':
                settings.llm.endpoint = document.getElementById('localEndpoint').value;
                settings.llm.model = document.getElementById('localModel').value;
                break;
            case 'openai':
                settings.llm.apiKey = document.getElementById('openaiKey').value;
                settings.llm.model = document.getElementById('openaiModel').value;
                break;
            case 'azure':
                settings.llm.endpoint = document.getElementById('azureEndpoint').value;
                settings.llm.apiKey = document.getElementById('azureKey').value;
                settings.llm.deploymentName = document.getElementById('azureDeployment').value;
                break;
            case 'claude':
                settings.llm.apiKey = document.getElementById('claudeKey').value;
                settings.llm.model = document.getElementById('claudeModel').value;
                break;
        }

        // Add embedding-specific settings
        switch (settings.embedding.type) {
            case 'local':
                settings.embedding.endpoint = document.getElementById('localEmbeddingEndpoint').value;
                settings.embedding.model = document.getElementById('localEmbeddingModel').value;
                break;
            case 'openai':
                settings.embedding.apiKey = document.getElementById('openaiEmbeddingKey').value;
                settings.embedding.model = document.getElementById('openaiEmbeddingModel').value;
                break;
            case 'azure':
                settings.embedding.endpoint = document.getElementById('azureEmbeddingEndpoint').value;
                settings.embedding.apiKey = document.getElementById('azureEmbeddingKey').value;
                settings.embedding.deploymentName = document.getElementById('azureEmbeddingDeployment').value;
                break;
        }

        return settings;
    }

    // Function to check LLM health status
    function checkLLMStatus() {
        const endpoint = document.getElementById('localEndpoint').value;
        const statusDot = document.querySelector('.status-indicator-dot');
        const statusText = document.querySelector('.status-text');

        if (!endpoint) {
            statusDot.classList.remove('online', 'offline');
            statusText.textContent = 'No endpoint specified';
            return;
        }

        // Set status to checking
        statusDot.classList.remove('online', 'offline');
        statusText.textContent = 'Checking...';

        // Make CORS request to the /health endpoint
        fetch(`${endpoint}/health`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json'
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Health check response:', data);

                // Update status indicator
                statusDot.classList.add('online');
                statusDot.classList.remove('offline');

                // Create status message based on response
                let statusMessage = 'Online';
                if (data.status) {
                    statusMessage = `${data.status}`;
                }
                if (data.version) {
                    statusMessage += ` (v${data.version})`;
                }
                statusText.textContent = statusMessage;
            })
            .catch(error => {
                console.error('Health check failed:', error);
                statusDot.classList.add('offline');
                statusDot.classList.remove('online');
                statusText.textContent = 'Offline or unreachable';
            });
    }

    // Function to fetch available models
    function fetchLLMModels() {
        const endpoint = document.getElementById('localEndpoint').value;
        const modelSelect = document.getElementById('localModel');
        const modelParameters = document.getElementById('modelParameters');
        const modelContext = document.getElementById('modelContext');

        // Reset model info
        modelParameters.textContent = '-';
        modelContext.textContent = '-';

        if (!endpoint) {
            modelSelect.innerHTML = '<option value="">No endpoint specified</option>';
            modelSelect.disabled = true;
            return;
        }

        // Set loading state
        modelSelect.innerHTML = '<option value="">Loading models...</option>';
        modelSelect.disabled = true;

        // Make CORS request to the /models endpoint
        fetch(`${endpoint}/models`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json'
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Models response:', data);

                modelSelect.innerHTML = ''; // Clear options

                // Check if the response is an array of models or has a data property
                const models = Array.isArray(data) ? data :
                    (data.data && Array.isArray(data.data) ? data.data : []);

                if (models.length > 0) {
                    // Populate model dropdown
                    models.forEach(model => {
                        // Use model.id or model.name depending on API response format
                        const modelId = model.id || model.name || model;
                        const option = document.createElement('option');
                        option.value = modelId;
                        option.textContent = modelId;
                        // Store model details as data attributes or in a dataset
                        if (model.parameters) {
                            option.dataset.parameters = model.parameters;
                        }
                        if (model.context_length || model.contextLength) {
                            option.dataset.context = model.context_length || model.contextLength;
                        }
                        modelSelect.appendChild(option);
                    });

                    // Enable the select element
                    modelSelect.disabled = false;

                    // Trigger change event to update model info
                    modelSelect.dispatchEvent(new Event('change'));
                } else {
                    modelSelect.innerHTML = '<option value="">No models found</option>';
                    modelSelect.disabled = true;
                }
            })
            .catch(error => {
                console.error('Failed to fetch models:', error);
                modelSelect.innerHTML = '<option value="">Failed to load models</option>';
                modelSelect.disabled = true;
            });
    }

    // Load settings from host
    function loadSettings(settings) {
        if (!settings) return;
        
        // Set app config settings before anything else as code can become unreachable
        if (settings.appConfig) {
            console.log("Setting knowledge base list from app config settings");
            window.aimx.core.setKnowledgeBaseList(settings.appConfig.ragDatabases);
            window.aimx.core.defaultKnowledgeBase = settings.appConfig.defaultRagDatabase;
        }
        
        try {
            console.log("Loading settings from host");

            // Ensure elements are initialized - reget elements if needed
            if (!validateElements()) {
                getElements();
                if (!validateElements()) {
                    console.error("Cannot load settings - modal elements not found");
                    return;
                }
            }

            // Set LLM type
            if (settings.llm && settings.llm.type) {
                const llmRadio = document.querySelector(`input[name="llmType"][value="${settings.llm.type}"]`);
                if (llmRadio) {
                    llmRadio.checked = true;
                    llmRadio.dispatchEvent(new Event('change'));
                }

                // Set LLM-specific fields
                switch (settings.llm.type) {
                    case 'local':
                        if (settings.llm.endpoint) document.getElementById('localEndpoint').value = settings.llm.endpoint;
                        if (settings.llm.model) document.getElementById('localModel').value = settings.llm.model;
                        break;
                    case 'openai':
                        if (settings.llm.apiKey) document.getElementById('openaiKey').value = settings.llm.apiKey;
                        if (settings.llm.model) document.getElementById('openaiModel').value = settings.llm.model;
                        break;
                    case 'azure':
                        if (settings.llm.endpoint) document.getElementById('azureEndpoint').value = settings.llm.endpoint;
                        if (settings.llm.apiKey) document.getElementById('azureKey').value = settings.llm.apiKey;
                        if (settings.llm.deploymentName) document.getElementById('azureDeployment').value = settings.llm.deploymentName;
                        break;
                    case 'claude':
                        if (settings.llm.apiKey) document.getElementById('claudeKey').value = settings.llm.apiKey;
                        if (settings.llm.model) document.getElementById('claudeModel').value = settings.llm.model;
                        break;
                }
            }

            // Set embedding type
            if (settings.embedding && settings.embedding.type) {
                const embeddingRadio = document.querySelector(`input[name="embeddingType"][value="${settings.embedding.type}"]`);
                if (embeddingRadio) {
                    embeddingRadio.checked = true;
                    embeddingRadio.dispatchEvent(new Event('change'));
                }

                // Set embedding-specific fields
                switch (settings.embedding.type) {
                    case 'local':
                        if (settings.embedding.endpoint) document.getElementById('localEmbeddingEndpoint').value = settings.embedding.endpoint;
                        if (settings.embedding.model) document.getElementById('localEmbeddingModel').value = settings.embedding.model;
                        break;
                    case 'openai':
                        if (settings.embedding.apiKey) document.getElementById('openaiEmbeddingKey').value = settings.embedding.apiKey;
                        if (settings.embedding.model) document.getElementById('openaiEmbeddingModel').value = settings.embedding.model;
                        break;
                    case 'azure':
                        if (settings.embedding.endpoint) document.getElementById('azureEmbeddingEndpoint').value = settings.embedding.endpoint;
                        if (settings.embedding.apiKey) document.getElementById('azureEmbeddingKey').value = settings.embedding.apiKey;
                        if (settings.embedding.deploymentName) document.getElementById('azureEmbeddingDeployment').value = settings.embedding.deploymentName;
                        break;
                }
            }

            // Set advanced settingS
            // TODO Move advanced settings to persistent app config settings
            if (settings.advanced) {
                if (settings.advanced.chunkSize) document.getElementById('chunkSize').value = settings.advanced.chunkSize;
                if (settings.advanced.chunkOverlap) document.getElementById('chunkOverlap').value = settings.advanced.chunkOverlap;
                if (settings.advanced.maxConcurrentRequests) document.getElementById('maxConcurrentRequests').value = settings.advanced.maxConcurrentRequests;
                if (settings.advanced.debugMode) document.getElementById('debugMode').value = settings.advanced.debugMode;
            }
            console.log("Settings loaded successfully");
        } catch (error) {
            console.error("Error loading settings:", error);
        }
    }

    // Public API
    return {
        initialize,
        loadSettings,
        checkLLMStatus,
        fetchLLMModels,
        getElements  // Expose this to allow reinitialization
    };
})();

// DO NOT automatically initialize on DOMContentLoaded
// Instead, wait for explicit initialization after dynamic content is loaded
