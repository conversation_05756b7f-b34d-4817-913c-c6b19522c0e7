/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    AimxRpcTests.cpp

Abstract:
    TAEF test suite for AIMXSRV RPC client methods.

Author:
    <PERSON> (SNAKE FIGHTER) (lindakup) 06/10/2025

--*/
#pragma once
#include "pch.hxx"
#include "aimxrpcclient.h"
#include "AimxRpcTests.h"

#include <nlohmann/json.hpp>
#include <combaseapi.h>
#include <ldap.h>
#include <ntdsapi.h>

void AimxRpcTests::TestAimxConnectAndClose()
{
    GUID contextId = {};
    HRESULT hr = S_OK;
    AimxRpcClient* rpcClient = new AimxRpcClient();
    if (rpcClient == nullptr)
    {
        VERIFY_FAIL(L"Failed to create AimxRpcClient instance");
        return;
    }
    hr = rpcClient->AimxConnect(&contextId);
    VERIFY_SUCCEEDED(hr, L"AimxConnect should succeed");
    VERIFY_IS_FALSE(IsEqualGUID(contextId, GUID{}), L"ContextId should not be GUID_NULL after connect");

    // dump the contextId for debugging
    wchar_t guidStr[40] = { 0 };
    StringFromGUID2(contextId, guidStr, 40);
    wprintf(L"ContextId: %ws\n", guidStr);
    hr = rpcClient->AimxClose(contextId);
    VERIFY_SUCCEEDED(hr, L"AimxClose should succeed");
    // report success
    wprintf(L"AimxRpcTests::TestAimxConnectAndClose passed.\n");
    if (rpcClient)
    {
        delete rpcClient;
    }
}

void AimxRpcTests::TestAimxProcessPrompt()
{
    GUID contextId = {};
    HRESULT hr = S_OK;
    AimxRpcClient* rpcClient = new AimxRpcClient();
    if (rpcClient == nullptr)
    {
        VERIFY_FAIL(L"Failed to create AimxRpcClient instance");
        return;
    }
    hr = rpcClient->AimxConnect(&contextId);
    VERIFY_SUCCEEDED(hr, L"AimxConnect should succeed");
    VERIFY_IS_FALSE(IsEqualGUID(contextId, GUID{}), L"ContextId should not be GUID_NULL after connect");
    // Test prompt processing. requestType: AIMX_CHATBOT_QUERY and executionMode: AIMX_MODE_AUTOMATED
    nlohmann::json request;
    request["requestType"] = 1; // AIMX_CHATBOT_QUERY
    request["query"] = "What is the current DC password policy";
    request["executionMode"] = 1; // AIMX_MODE_AUTOMATED
    std::string requestStr = request.dump();
    std::wstring requestWStr(requestStr.begin(), requestStr.end());
    LPWSTR response = nullptr;
    hr = rpcClient->AimxProcessPrompt(contextId, requestWStr.c_str(), &response);
    VERIFY_SUCCEEDED(hr, L"AimxProcessPrompt should succeed");
    VERIFY_IS_NOT_NULL(response, L"Response should not be null");
    wprintf(L"AimxProcessPrompt response: %ws\n", response);
    // Parse response
    std::wstring responseWStr(response);
    std::string responseStr(responseWStr.begin(), responseWStr.end());
    nlohmann::json responseJson = nlohmann::json::parse(responseStr);
    Log::Comment(L"Chatbot Query Response:");
    Log::Comment(String().Format(L"%ws", response));
    if (response) MIDL_user_free(response);
    hr = rpcClient->AimxClose(contextId);
    VERIFY_SUCCEEDED(hr, L"AimxClose should succeed");
    if (rpcClient) delete rpcClient;
    wprintf(L"AimxRpcTests::TestAimxProcessPrompt passed.\n");
}

void AimxRpcTests::TestOperationIdHijackProtection()
{
    // Open two different context handles
    AimxRpcClient* rpcClient1 = new AimxRpcClient();
    AimxRpcClient* rpcClient2 = new AimxRpcClient();
    VERIFY_IS_NOT_NULL(rpcClient1, L"rpcClient1 should not be null");
    VERIFY_IS_NOT_NULL(rpcClient2, L"rpcClient2 should not be null");

    GUID contextId1 = {};
    GUID contextId2 = {};
    HRESULT hr = rpcClient1->AimxConnect(&contextId1);
    VERIFY_SUCCEEDED(hr, L"rpcClient1 connect should succeed");
    hr = rpcClient2->AimxConnect(&contextId2);
    VERIFY_SUCCEEDED(hr, L"rpcClient2 connect should succeed");
    VERIFY_IS_FALSE(IsEqualGUID(contextId1, contextId2), L"Context handles should be unique");

    // Make a chatbot query with context1
    nlohmann::json request;
    request["requestType"] = 1; // AIMX_CHATBOT_QUERY
    request["query"] = "Test hijack protection";
    request["executionMode"] = 1;
    std::string requestStr = request.dump();
    std::wstring requestWStr(requestStr.begin(), requestStr.end());
    LPWSTR response = nullptr;
    hr = rpcClient1->AimxProcessPrompt(contextId1, requestWStr.c_str(), &response);
    VERIFY_SUCCEEDED(hr, L"Chatbot query should succeed");
    VERIFY_IS_NOT_NULL(response, L"Response should not be null");
    std::wstring responseWStr(response);
    std::string responseStr(responseWStr.begin(), responseWStr.end());
    nlohmann::json responseJson = nlohmann::json::parse(responseStr);
    VERIFY_IS_TRUE(responseJson["success"].get<bool>(), L"Chatbot query should succeed");
    std::string operationId = responseJson["operationId"].get<std::string>();
    if (response) MIDL_user_free(response);

    // Now try to use context2 to query the status of operationId from context1
    nlohmann::json statusRequest;
    statusRequest["requestType"] = 3; // AIMX_PLAN_STATUS
    statusRequest["operationId"] = operationId;
    std::string statusRequestStr = statusRequest.dump();
    std::wstring statusRequestWStr(statusRequestStr.begin(), statusRequestStr.end());
    response = nullptr;
    hr = rpcClient2->AimxProcessPrompt(contextId2, statusRequestWStr.c_str(), &response);
    // Should fail with error
    VERIFY_IS_TRUE(FAILED(hr) || response == nullptr, L"Hijack attempt should fail");
    if (response)
    {
        std::wstring hijackResponseWStr(response);
        std::string hijackResponseStr(hijackResponseWStr.begin(), hijackResponseWStr.end());
        nlohmann::json hijackResponseJson = nlohmann::json::parse(hijackResponseStr);
        VERIFY_IS_FALSE(hijackResponseJson.value("success", true), L"Hijack response should not be successful");
        if (response) MIDL_user_free(response);
    }

    // Cleanup
    rpcClient1->AimxClose(contextId1);
    rpcClient2->AimxClose(contextId2);
    delete rpcClient1;
    delete rpcClient2;
}

void AimxRpcTests::TestContextHandleSidProtection()
{
    /**
     Find a domain controller
    NTSTATUS status;
    ULONG ldapErr = 0;
    bool fRet = false;
    LDAP* pLdap = nullptr;
    PDOMAIN_CONTROLLER_INFOW pDCInfo = NULL;
    SEC_WINNT_AUTH_IDENTITY AuthIdentityDomain1;
    std::wstring DCName;

    // find a Domain Controller (DC) to connect to using DsGetDCName
    fRet = DsGetDcName(
        NULL,
        s_domainFqdn.c_str(),
        NULL,
        NULL,
        DS_DIRECTORY_SERVICE_REQUIRED | DS_WRITABLE_REQUIRED,
        &pDCInfo
    );

    if (!fRet)
    {
        VERIFY_FAIL(L"Failed to find a Domain Controller");
    }

    //save off the DCName
    DCName = pDCInfo->DomainControllerName;
    Log::Comment(String().Format(L"Found Domain Controller: %ws", DCName.c_str()));

    InitializeAuthIdentityStruct(
        s_domainFqdn.c_str(),
        L"Administrator", // Use a domain admin account
        s_domainAdminPassword.c_str(),
        &AuthIdentityDomain1
    );


    // Bind to the DC using LDAP
    fRet = DsBindWithCred(
        pDCInfo->DomainControllerName,
        s_domainFqdn.c_str(),
        &AuthIdentityDomain1,
        &pLdap
    );

    if (!fRet)
    {
        Log::Comment(L"Failed to bind to DC using LDAP");
        goto Exit;
    }

    //create a userA in the domain using ldap_add_s
    std::wstring userA = L"userA";
    std::wstring userB = L"userB";

    LDAPMod userMod;
    userMod.mod_op = LDAP_MOD_ADD;
    userMod.mod_type = L"samAccountName";
    userMod.mod_values = new PWSTR[2];
    userMod.mod_values[0] = userA.c_str();
    userMod.mod_values[1] = NULL;

    LDAPMod* mods[] = { &userMod, NULL };
    fRet = ldap_add_s(pLdap, userA.c_str(), mods);

    if (!fRet)
    {
        Log::Comment(L"Failed to create userA");
        goto Exit;
    }

    //create userB
    userMod.mod_values[0] = userB.c_str();
    fRet = ldap_add_s(pLdap, userB.c_str(), mods);

    if (!fRet)
    {
        Log::Comment(L"Failed to create userB");
        goto Exit;
    }

    // 3. Logon as userA, connect to aimxsrv, get contextA
    // 3. Logon as userB, connect to aimxsrv, get contextB
    // 4. Try to use contextA from userB, expect failure
    // 5. Try to use contextB from userA, expect failure
    // 6. Cleanup users
**/
    Log::Comment(L"TestContextHandleSidProtection: This test requires domain and user impersonation setup.");
    VERIFY_IS_TRUE(true, L"Stub for SID/context handle protection test");
//Exit:
    /**
    if (pLdap)
    {
        ldap_unbind(pLdap);
    }
    if (pDCInfo)
    {
        NetApiBufferFree(pDCInfo);
    }
    // if fRest is true its success.
    if (fRet)
    {
        VERIFY_SUCCEEDED(hr, L"TestContextHandleSidProtection should succeed");
    }
    else
    {
        VERIFY_FAIL(L"TestContextHandleSidProtection failed to setup environment");
    }
    **/
}

