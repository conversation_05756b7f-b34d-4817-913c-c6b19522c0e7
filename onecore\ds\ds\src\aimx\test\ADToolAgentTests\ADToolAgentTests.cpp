/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    ADToolAgentTests.cpp

Abstract:
    TAEF test suite for ADToolAgent component.

Author:
    <PERSON><PERSON><PERSON><PERSON> (pumathur) 06/18/2025

--*/

#include "pch.hxx"
#include "ADToolAgentTests.h"
#include <nlohmann/json.hpp>

void ADToolAgentTests::TestSingletonInstance()
{
    Log::Comment(L"====== Starting TestSingletonInstance ======");
    Log::Comment(L"Testing ADToolAgent singleton instance");
    
    // Get instance of ADToolAgent
    ADToolAgent& agent1 = ADToolAgent::GetInstance();
    Log::Comment(L"First instance obtained");
    
    ADToolAgent& agent2 = ADToolAgent::GetInstance();
    Log::Comment(L"Second instance obtained");
    
    // Verify that both references point to the same instance
    VERIFY_ARE_EQUAL(&agent1, &agent2, L"Singleton should return the same instance");
    Log::Comment(String().Format(L"Instance 1 address: 0x%p, Instance 2 address: 0x%p", &agent1, &agent2));
    
    Log::Comment(L"ADToolAgent singleton instance verified");
    Log::Comment(L"====== Finished TestSingletonInstance ======");
}

void ADToolAgentTests::TestExecuteAction()
{
    
    Log::Comment(L"====== Starting TestExecuteAction ======");
    Log::Comment(L"Testing ADToolAgent::ExecuteAction with various input combinations");
    
    ADToolAgent& agent = ADToolAgent::GetInstance();
    Log::Comment(L"ADToolAgent instance obtained");
    
    // Test case 1: Basic AD_DC_DIAGNOSTICS request
    {
        Log::Comment(L"Test case 1: Basic AD_DC_DIAGNOSTICS request");
        
        nlohmann::json request;
        request[AimxConstants::JsonFields::AIMX_AD_AREA] = AimxConstants::ADAreas::AIMX_DC_DIAGNOSTICS;
        request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";

        std::string requestStr = request.dump();
        std::string responseStr;
        bool result = agent.ExecuteAction(requestStr, responseStr);
        VERIFY_IS_TRUE(result, L"ExecuteAction should succeed for basic request");
        
        // Verify response format
        VERIFY_IS_TRUE(nlohmann::json::accept(responseStr), L"Response should be valid JSON");
        
        nlohmann::json response = nlohmann::json::parse(responseStr);
        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");

        if (0 != strcmp(response[AimxConstants::JsonFields::AIMX_RESULT].get<std::string>().c_str(), AimxConstants::JsonFields::AIMX_SUCCESS))
        {
            VERIFY_FAIL(L"Testing ADToolAgent::ExecuteAction failed for the area AD_DC_DIAGNOSTICS");
        }
    }
    
    // Test case 2: AD_REPLICATION request
    {
        Log::Comment(L"Test case 2: AD_REPLICATION request");
        
        nlohmann::json request;
        request[AimxConstants::JsonFields::AIMX_AD_AREA] = AimxConstants::ADAreas::AIMX_REPLICATION;
        request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";
        request[AimxConstants::JsonFields::AIMX_ACTION] = AimxConstants::ActionTypes::AIMX_GET_REPLICATION_STATUS_INDIVIDUAL_DC;

        std::string requestStr = request.dump();
        std::string responseStr;

        bool result = agent.ExecuteAction(requestStr, responseStr);

        // We don't verify success here because it depends on AD environment
        // Just verify that we get a proper JSON response
        VERIFY_IS_TRUE(nlohmann::json::accept(responseStr), L"Response should be valid JSON");
        
        nlohmann::json response = nlohmann::json::parse(responseStr);
        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
    }
    
    // Test case 3: Invalid ADArea
    {
        Log::Comment(L"Test case 3: Invalid ADArea");
        
        nlohmann::json request;
        request[AimxConstants::JsonFields::AIMX_AD_AREA] = AimxConstants::ADAreas::AIMX_UNKNOWN;
        request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";

        std::string requestStr = request.dump();
        std::string responseStr;

        bool result = agent.ExecuteAction(requestStr, responseStr);
        VERIFY_IS_FALSE(result, L"ExecuteAction should fail for invalid ADArea");
        
        nlohmann::json response = nlohmann::json::parse(responseStr);
        if (0 != strcmp(response[AimxConstants::JsonFields::AIMX_RESULT].get<std::string>().c_str(), AimxConstants::JsonFields::AIMX_FAILURE))
        {
            VERIFY_FAIL(L"Testing ADToolAgent::ExecuteAction failed for the case of INVALID_AREA");
        }
    }
      // Test case 4: Missing required fields
    {
        Log::Comment(L"Test case 4: Missing required fields");
        
        nlohmann::json request;
        // Missing ADArea field
        request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";

        std::string requestStr = request.dump();
        std::string responseStr;

        bool result = agent.ExecuteAction(requestStr, responseStr);
        VERIFY_IS_FALSE(result, L"ExecuteAction should fail for missing required fields");
        Log::Comment(String().Format(L"ExecuteAction result: %s", result ? L"true" : L"false"));
        
        nlohmann::json response = nlohmann::json::parse(responseStr);
        Log::Comment(String().Format(L"Response: %hs", responseStr.c_str()));

        if (0 != strcmp(response[AimxConstants::JsonFields::AIMX_RESULT].get<std::string>().c_str(), AimxConstants::JsonFields::AIMX_FAILURE))
        {
            VERIFY_FAIL(L"Testing ADToolAgent::ExecuteAction failed for the case of Missing required fields");
        }
        else
        {
            Log::Comment(L"Missing required fields test passed successfully");
        }
    }
    
    Log::Comment(L"ADToolAgent::ExecuteAction tests completed");
    Log::Comment(L"====== Finished TestExecuteAction ======");
}

void ADToolAgentTests::TestADAreaClassification()
{
    
    Log::Comment(L"====== Starting TestADAreaClassification ======");
    Log::Comment(L"Testing AD Area classification to verify correct parsing of AD area strings");

    // This test requires access to the private method GetADAreaFromString
    // We can only test indirectly through ExecuteAction
    
    ADToolAgent& agent = ADToolAgent::GetInstance();
    Log::Comment(L"ADToolAgent instance obtained");
    
    struct TestCase {
        std::string adArea;
        bool shouldSucceed;
    };
    
    std::vector<TestCase> testCases = {
        {"AD_DC_DIAGNOSTICS", true},
        {"AD_REPLICATION", true},
        {"AD_SECURITY_INSIGHTS_ACCOUNTS", true},
        {"AD_SECURITY_INSIGHTS_COMPLIANCE", true},
        {"AD_PERFORMANCE", true},
        {"AD_DATABASE_HEALTH", true},
        {"AD_DEPENDENCIES", true},
        {"AD_DFSR", true},
        {"AD_GROUP_POLICY", true},
        {"UNKNOWN_AREA", false},
        {"", false}
    };
    
    Log::Comment(String().Format(L"Testing %d AD area classifications", static_cast<int>(testCases.size())));
    int passCount = 0;
    
    for (const auto& testCase : testCases)
    {
        Log::Comment(String().Format(L"Testing ADArea: %hs (Expected: %hs)", 
                                  testCase.adArea.c_str(), 
                                  testCase.shouldSucceed ? AimxConstants::JsonFields::AIMX_SUCCESS : AimxConstants::JsonFields::AIMX_FAILURE));
        
        nlohmann::json request;
        request[AimxConstants::JsonFields::AIMX_AD_AREA] = testCase.adArea;
        request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";

        std::string requestStr = request.dump();
        std::string responseStr;
        bool result = agent.ExecuteAction(requestStr, responseStr);
        
        if (testCase.shouldSucceed)
        {
            // For valid areas, we don't necessarily expect success as it depends on implementation
            // Just verify we get a valid JSON response
            VERIFY_IS_TRUE(nlohmann::json::accept(responseStr), L"Response should be valid JSON");
            
            nlohmann::json response = nlohmann::json::parse(responseStr);
            VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
            
            Log::Comment(String().Format(L"Valid area '%hs' test result: %hs", 
                                      testCase.adArea.c_str(),
                                      response[AimxConstants::JsonFields::AIMX_RESULT].get<std::string>().c_str()));
            passCount++;
        }
        else
        {
            // For invalid areas, we expect failure
            VERIFY_IS_FALSE(result, L"ExecuteAction should fail for invalid ADArea");
            
            nlohmann::json response = nlohmann::json::parse(responseStr);

            if (0 != strcmp(response[AimxConstants::JsonFields::AIMX_RESULT].get<std::string>().c_str(), AimxConstants::JsonFields::AIMX_FAILURE))
            {
                VERIFY_FAIL(L"Testing ADToolAgentTests::TestADAreaClassification");
            }
            else
            {
                Log::Comment(L"Invalid area test passed: ExecuteAction correctly returned failure");
                passCount++;
            }
        }
    }
    
    Log::Comment(String().Format(L"AD Area classification tests completed: %d/%d passed", 
                              passCount, static_cast<int>(testCases.size())));
    Log::Comment(L"====== Finished TestADAreaClassification ======");
}


void ADToolAgentTests::TestRunCommand()
{
    Log::Comment(L"====== Starting TestRunCommand ======");
    Log::Comment(L"Testing RunCommand functionality to verify command execution and output capture");
    
    ADToolAgent& agent = ADToolAgent::GetInstance();
    std::wstring output;
    Log::Comment(L"ADToolAgent instance obtained successfully");

    // Test case 1: Simple command
    {
        Log::Comment(L"Test case 1: Simple command (hostname)");
        bool result = agent.RunCommand(L"hostname", output);
        VERIFY_IS_TRUE(result, L"RunCommand should succeed for simple command");
        VERIFY_IS_FALSE(output.empty(), L"Output should not be empty");
        Log::Comment(String().Format(L"Command output: %s", output.c_str()));
        Log::Comment(String().Format(L"Test case 1 completed. Success: %s", result ? L"True" : L"False"));
    }

    // Test case 2: Command with parameters
    {
        Log::Comment(L"Test case 2: Command with parameters (echo test)");
        bool result = agent.RunCommand(L"cmd.exe /c echo test", output);
        VERIFY_IS_TRUE(result, L"RunCommand should succeed for command with parameters");
        VERIFY_IS_TRUE(output.find(L"test") != std::wstring::npos, L"Output should contain expected text");
        Log::Comment(String().Format(L"Command output: %s", output.c_str()));
    }
    
    // Test case 3: PowerShell get system info
    {
        Log::Comment(L"Test case 3: PowerShell Get-ComputerInfo");
        bool result = agent.RunCommand(L"powershell.exe -Command \"Get-ComputerInfo -Property CsName,OsName,OsVersion\"", output);
        VERIFY_IS_TRUE(result, L"RunCommand should succeed for PowerShell system info command");
        VERIFY_IS_FALSE(output.empty(), L"Output should not be empty");
        Log::Comment(String().Format(L"PowerShell Get-ComputerInfo output: %s", output.c_str()));
    }
    
    // Test case 4: PowerShell get environment variable
    {
        Log::Comment(L"Test case 4: PowerShell environment variable");
        bool result = agent.RunCommand(L"powershell.exe -Command \"$env:COMPUTERNAME\"", output);
        VERIFY_IS_TRUE(result, L"RunCommand should succeed for PowerShell environment variable");
        VERIFY_IS_FALSE(output.empty(), L"Output should not be empty");
        Log::Comment(String().Format(L"PowerShell environment variable output: %s", output.c_str()));
    }

    // Test case 5: PowerShell Get-ADDefaultDomainPasswordPolicy
    {
        Log::Comment(L"Test case 5: PowerShell Get-ADDefaultDomainPasswordPolicy");
        bool result = agent.RunCommand(L"powershell.exe -Command \"try { Import-Module ActiveDirectory; $p = Get-ADDefaultDomainPasswordPolicy; Write-Output \\\"ComplexityEnabled: $($p.ComplexityEnabled)\\\"; Write-Output \\\"MinPasswordLength: $($p.MinPasswordLength)\\\"; Write-Output \\\"PasswordHistoryCount: $($p.PasswordHistoryCount)\\\" } catch { Write-Output 'Command failed: Active Directory module may not be installed or not in a domain' }\"", output);
        VERIFY_IS_TRUE(result, L"RunCommand should succeed for PowerShell Get-ADDefaultDomainPasswordPolicy command");
        VERIFY_IS_FALSE(output.empty(), L"Output should not be empty");
        Log::Comment(String().Format(L"PowerShell Get-ADDefaultDomainPasswordPolicy output: %s", output.c_str()));
    }

    // Test case 6: PowerShell get network info
    {
        Log::Comment(L"Test case 6: PowerShell network info");
        // Using a basic command without advanced PowerShell cmdlets
        bool result = agent.RunCommand(L"powershell.exe -Command \"try { $net = @(Get-NetIPConfiguration)[0]; if($net) { Write-Output \\\"Network Info Available\\\"; Write-Output \\\"InterfaceAlias: $($net.InterfaceAlias)\\\" } else { Write-Output 'No network interfaces found' } } catch { Write-Output 'Command failed: Network info not available' }\"", output);
        VERIFY_IS_TRUE(result, L"RunCommand should succeed for PowerShell network command");
        VERIFY_IS_FALSE(output.empty(), L"Output should not be empty");
        Log::Comment(String().Format(L"PowerShell network info output: %s", output.c_str()));
    }

    // Test case 7: PowerShell with JSON output
    {
        Log::Comment(L"Test case 7: PowerShell with JSON-like output");
        // Create a custom formatted output manually without using ConvertTo-Json
        bool result = agent.RunCommand(L"powershell.exe -Command \"$service = Get-Service -Name 'W32Time'; Write-Output '{'; Write-Output '  \"Name\": \"' + $service.Name + '\",'; Write-Output '  \"DisplayName\": \"' + $service.DisplayName + '\",'; Write-Output '  \"Status\": \"' + $service.Status.ToString() + '\"'; Write-Output '}'\"", output);
        VERIFY_IS_TRUE(result, L"RunCommand should succeed for PowerShell JSON-like command");
        VERIFY_IS_FALSE(output.empty(), L"Output should not be empty");
        VERIFY_IS_TRUE(output.find(L"W32Time") != std::wstring::npos, L"Output should contain W32Time service");
        VERIFY_IS_TRUE(output.find(L"Name") != std::wstring::npos, L"Output should be in JSON-like format");
        Log::Comment(String().Format(L"PowerShell JSON-like output: %s", output.c_str()));
    }
    
    // Test case 8: PowerShell Get-ADDomain command
    {
        Log::Comment(L"Test case 8: PowerShell Get-ADDomain");
        bool result = agent.RunCommand(L"powershell.exe -Command \"try { Import-Module ActiveDirectory; $domain = Get-ADDomain; Write-Output '{'; Write-Output '  \"Name\": \"' + $domain.Name + '\",'; Write-Output '  \"Forest\": \"' + $domain.Forest + '\",'; Write-Output '  \"DomainMode\": \"' + $domain.DomainMode.ToString() + '\",'; Write-Output '  \"PDCEmulator\": \"' + $domain.PDCEmulator + '\"'; Write-Output '}' } catch { Write-Output 'Command failed: Active Directory module may not be installed or not in a domain' }\"", output);
        VERIFY_IS_TRUE(result, L"RunCommand should succeed for PowerShell Get-ADDomain command");
        VERIFY_IS_FALSE(output.empty(), L"Output should not be empty");
        Log::Comment(String().Format(L"PowerShell Get-ADDomain output: %s", output.c_str()));
    }

    // Test case 9: PowerShell Get-ADComputer command
    {
        Log::Comment(L"Test case 9: PowerShell Get-ADComputer");
        bool result = agent.RunCommand(L"powershell.exe -Command \"try { Import-Module ActiveDirectory; $computer = Get-ADComputer -Filter * -Properties Name,OperatingSystem,OperatingSystemVersion -ResultSetSize 1; Write-Output '{'; Write-Output '  \"Name\": \"' + $computer.Name + '\",'; Write-Output '  \"OperatingSystem\": \"' + $computer.OperatingSystem + '\",'; Write-Output '  \"OperatingSystemVersion\": \"' + $computer.OperatingSystemVersion + '\"'; Write-Output '}' } catch { Write-Output 'Command failed: Active Directory module may not be installed or not in a domain' }\"", output);
        VERIFY_IS_TRUE(result, L"RunCommand should succeed for PowerShell Get-ADComputer command");
        VERIFY_IS_FALSE(output.empty(), L"Output should not be empty");
        Log::Comment(String().Format(L"PowerShell Get-ADComputer output: %s", output.c_str()));
    }

    // Test case 10: PowerShell Get-ADGroup command
    {
        Log::Comment(L"Test case 10: PowerShell Get-ADGroup");
        bool result = agent.RunCommand(L"powershell.exe -Command \"try { Import-Module ActiveDirectory; $groups = Get-ADGroup -Filter \\\"GroupCategory -eq 'Security'\\\" -ResultSetSize 3; if ($groups) { Write-Output 'Security Groups:'; foreach ($group in $groups) { Write-Output (\"Name: \" + $group.Name + \", SamAccountName: \" + $group.SamAccountName + \", GroupScope: \" + $group.GroupScope) } } else { Write-Output 'No security groups found' } } catch { Write-Output 'Command failed: Active Directory module may not be installed or not in a domain' }\"", output);
        VERIFY_IS_TRUE(result, L"RunCommand should succeed for PowerShell Get-ADGroup command");
        VERIFY_IS_FALSE(output.empty(), L"Output should not be empty");
        Log::Comment(String().Format(L"PowerShell Get-ADGroup output: %s", output.c_str()));
    }

    // Test case 11: PowerShell Get-ADDomainController command
    {
        Log::Comment(L"Test case 11: PowerShell Get-ADDomainController");
        bool result = agent.RunCommand(L"powershell.exe -Command \"try { Import-Module ActiveDirectory; $dc = Get-ADDomainController -Discover -Service 'GlobalCatalog'; Write-Output '{'; Write-Output '  \"Name\": \"' + $dc.Name + '\",'; Write-Output '  \"Domain\": \"' + $dc.Domain + '\",'; Write-Output '  \"Forest\": \"' + $dc.Forest + '\",'; Write-Output '  \"IPv4Address\": \"' + $dc.IPv4Address + '\",'; Write-Output '  \"IsGlobalCatalog\": \"' + $dc.IsGlobalCatalog + '\"'; Write-Output '}' } catch { Write-Output 'Command failed: Active Directory module may not be installed or not in a domain' }\"", output);
        VERIFY_IS_TRUE(result, L"RunCommand should succeed for PowerShell Get-ADDomainController command");
        VERIFY_IS_FALSE(output.empty(), L"Output should not be empty");
        Log::Comment(String().Format(L"PowerShell Get-ADDomainController output: %s", output.c_str()));
    }

    // Test case 12: PowerShell repadmin command
    {
        Log::Comment(L"Test case 12: PowerShell execute repadmin command");
        bool result = agent.RunCommand(L"powershell.exe -Command \"try { & repadmin /showrepl } catch { Write-Output 'Command failed: repadmin may not be available or insufficient permissions' }\"", output);
        VERIFY_IS_TRUE(result, L"RunCommand should succeed for PowerShell repadmin command");
        VERIFY_IS_FALSE(output.empty(), L"Output should not be empty");
        Log::Comment(String().Format(L"PowerShell repadmin output: %s", output.c_str()));
    }
    
    // Test case 13: PowerShell Get-ADForest command
    {
        Log::Comment(L"Test case 13: PowerShell Get-ADForest");
        bool result = agent.RunCommand(L"powershell.exe -Command \"try { Import-Module ActiveDirectory; $forest = Get-ADForest -Current LocalComputer; Write-Output '{'; Write-Output '  \"Name\": \"' + $forest.Name + '\",'; Write-Output '  \"RootDomain\": \"' + $forest.RootDomain + '\",'; Write-Output '  \"ForestMode\": \"' + $forest.ForestMode + '\"'; Write-Output '}' } catch { Write-Output 'Command failed: Active Directory module may not be installed or not in a domain' }\"", output);
        VERIFY_IS_TRUE(result, L"RunCommand should succeed for PowerShell Get-ADForest command");
        VERIFY_IS_FALSE(output.empty(), L"Output should not be empty");
        Log::Comment(String().Format(L"PowerShell Get-ADForest output: %s", output.c_str()));
    }

    // Test case 14: Invalid command
    {
        Log::Comment(L"Test case 14: Invalid command");
        // Use cmd.exe with the exit command which will produce empty output but exit successfully
        bool result = agent.RunCommand(L"cmd.exe /c exit 0", output);
        VERIFY_IS_FALSE(result, L"RunCommand should fail for commands with empty output");
        Log::Comment(String().Format(L"Empty output command result: %s", result ? L"true" : L"false"));
        Log::Comment(String().Format(L"Output length: %zu", output.size()));
        Log::Comment(String().Format(L"Test case 14 completed. Success: %s", !result ? L"True" : L"False"));
    }
    
    // Test case 15: Command with special characters
    {
        Log::Comment(L"Test case 15: Command with special characters");
        // This command should generate non-empty output, which ensures RunCommand returns true
        bool result = agent.RunCommand(L"powershell.exe -Command \"Write-Output 'Test with special chars: !@#$%^&*()'\"", output);
        VERIFY_IS_TRUE(result, L"RunCommand should succeed for command with special characters that produces non-empty output");
        VERIFY_IS_FALSE(output.empty(), L"Output should not be empty");
        VERIFY_IS_TRUE(output.find(L"special chars") != std::wstring::npos, L"Output should contain expected text");
        Log::Comment(String().Format(L"Special characters output: %s", output.c_str()));
    }
    
    // Test case 16: Long running command with timeout
    {
        Log::Comment(L"Test case 16: Long running command with timeout");
        auto start = std::chrono::high_resolution_clock::now();
        
        // Sleep for 5 seconds command
        bool result = agent.RunCommand(L"powershell.exe -Command \"Start-Sleep -s 5; Write-Output 'Completed after sleep'\"", output);
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(end - start).count();
        
        VERIFY_IS_TRUE(result, L"RunCommand should succeed for long running command");
        VERIFY_IS_FALSE(output.empty(), L"Output should not be empty");
        VERIFY_IS_TRUE(output.find(L"sleep") != std::wstring::npos, L"Output should contain expected text");
        VERIFY_IS_GREATER_THAN_OR_EQUAL(duration, 5LL, L"Command should take at least 5 seconds");
        
        Log::Comment(String().Format(L"Long running command output: %s", output.c_str()));
        Log::Comment(String().Format(L"Command took %lld seconds", duration));
    }
    
    // Test case 17: Command with large output
    {
        Log::Comment(L"Test case 17: Command with large output");
        
        // Generate a command that produces large output without using ForEach-Object
        bool result = agent.RunCommand(L"powershell.exe -Command \"$output = ''; for ($i = 1; $i -le 1000; $i++) { $output += 'Line ' + $i + [Environment]::NewLine }; $output\"", output);
        
        VERIFY_IS_TRUE(result, L"RunCommand should succeed for command with large output");
        VERIFY_IS_FALSE(output.empty(), L"Output should not be empty");
        
        // Verify output size
        Log::Comment(String().Format(L"Large output size: %zu characters", output.size()));
        VERIFY_IS_GREATER_THAN(output.size(), 5000ULL, L"Output should be at least 5000 characters");
        
        // Log a sample of the output
        Log::Comment(String().Format(L"Large output sample: %s", output.substr(0, 100).c_str()));
    }
    
    Log::Comment(L"RunCommand tests completed");
    Log::Comment(L"====== Finished TestRunCommand ======");
}

void ADToolAgentTests::TestConcurrentExecution()
{
    Log::Comment(L"====== Starting TestConcurrentExecution ======");
    Log::Comment(L"Testing ADToolAgent thread safety with concurrent requests");
    
    ADToolAgent& agent = ADToolAgent::GetInstance();
    const int NUM_THREADS = 5;
    std::vector<std::thread> threads;
    std::atomic<int> successCount(0);
    
    Log::Comment(String().Format(L"Launching %d concurrent requests", NUM_THREADS));
    
    // Create and start multiple threads
    for (int i = 0; i < NUM_THREADS; i++)
    {
        threads.push_back(std::thread([&agent, &successCount, i]()
        {
            // Alternate between different types of operations in different threads
            if (i % 3 == 0) {
                // Execute RunCommand
                std::wstring output;
                bool result = agent.RunCommand(L"hostname", output);
                if (result && !output.empty()) {
                    successCount++;
                }
            } else if (i % 3 == 1) {
                // Execute AD_DC_DIAGNOSTICS request
                nlohmann::json request;
                request[AimxConstants::JsonFields::AIMX_AD_AREA] = AimxConstants::ADAreas::AIMX_DC_DIAGNOSTICS;
                request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";
                
                std::string requestStr = request.dump();
                std::string responseStr;
                
                bool result = agent.ExecuteAction(requestStr, responseStr);
                if (result) {
                    successCount++;
                }
            } else {
                // Execute AD_REPLICATION request
                nlohmann::json request;
                request[AimxConstants::JsonFields::AIMX_AD_AREA] = AimxConstants::ADAreas::AIMX_REPLICATION;
                request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";
                request[AimxConstants::JsonFields::AIMX_ACTION] = AimxConstants::ActionTypes::AIMX_GET_REPLICATION_STATUS_INDIVIDUAL_DC;
                
                std::string requestStr = request.dump();
                std::string responseStr;
                
                bool result = agent.ExecuteAction(requestStr, responseStr);
                if (result) {
                    successCount++;
                }
            }
        }));
    }
    
    // Wait for all threads to finish
    for (auto& thread : threads)
    {
        thread.join();
    }
    
    Log::Comment(String().Format(L"All threads completed. Success count: %d/%d", 
                              successCount.load(), NUM_THREADS));
    
    // At least 2/3 of the operations should succeed (some may fail due to environment)
    VERIFY_IS_TRUE(successCount.load() >= (NUM_THREADS * 2 / 3), 
                  L"At least 2/3 of concurrent operations should succeed");
    
    Log::Comment(L"Thread safety test completed");
    Log::Comment(L"====== Finished TestConcurrentExecution ======");
}

void ADToolAgentTests::TestExecuteActionEdgeCases()
{
    Log::Comment(L"====== Starting TestExecuteActionEdgeCases ======");
    Log::Comment(L"Testing ADToolAgent::ExecuteAction with edge cases and unusual inputs");
    
    ADToolAgent& agent = ADToolAgent::GetInstance();
    Log::Comment(L"ADToolAgent instance obtained");
    
    // Test case 1: Empty JSON
    {
        Log::Comment(L"Test case 1: Empty JSON");
        
        std::string requestStr = "{}";
        std::string responseStr;
        
        bool result = agent.ExecuteAction(requestStr, responseStr);
        VERIFY_IS_FALSE(result, L"ExecuteAction should fail for empty JSON");
        
        // Verify response format
        VERIFY_IS_TRUE(nlohmann::json::accept(responseStr), L"Response should be valid JSON");
        
        nlohmann::json response = nlohmann::json::parse(responseStr);
        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
        VERIFY_IS_TRUE(response[AimxConstants::JsonFields::AIMX_RESULT] == AimxConstants::JsonFields::AIMX_FAILURE, L"Result should be Failure for empty JSON");
        
        Log::Comment(String().Format(L"Response: %hs", responseStr.c_str()));
    }
    
    // Test case 2: Invalid JSON
    {
        Log::Comment(L"Test case 2: Invalid JSON");
        
        std::string requestStr = "{ invalid json }";
        std::string responseStr;
        
        bool result = agent.ExecuteAction(requestStr, responseStr);
        VERIFY_IS_FALSE(result, L"ExecuteAction should fail for invalid JSON");
        
        // The response should still be valid JSON even when input is invalid
        VERIFY_IS_TRUE(nlohmann::json::accept(responseStr), L"Response should be valid JSON");
        
        nlohmann::json response = nlohmann::json::parse(responseStr);
        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
        VERIFY_IS_TRUE(response[AimxConstants::JsonFields::AIMX_RESULT] == AimxConstants::JsonFields::AIMX_FAILURE, L"Result should be Failure for invalid JSON");
        
        Log::Comment(String().Format(L"Response: %hs", responseStr.c_str()));
    }
    
    // Test case 3: Extra unexpected fields
    {
        Log::Comment(L"Test case 3: Extra unexpected fields");
        
        nlohmann::json request;
        request[AimxConstants::JsonFields::AIMX_AD_AREA] = AimxConstants::ADAreas::AIMX_DC_DIAGNOSTICS;
        request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";
        request["UnexpectedField1"] = "value1";
        request["UnexpectedField2"] = 123;
        request["UnexpectedField3"] = true;
        
        std::string requestStr = request.dump();
        std::string responseStr;
        
        bool result = agent.ExecuteAction(requestStr, responseStr);
        // Should succeed despite extra fields (robustness)
        VERIFY_IS_TRUE(result, L"ExecuteAction should succeed with extra unexpected fields");
        
        nlohmann::json response = nlohmann::json::parse(responseStr);
        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
        
        Log::Comment(String().Format(L"Response: %hs", responseStr.c_str()));
    }
    
    // Test case 4: Extremely long field values
    {
        Log::Comment(L"Test case 4: Extremely long field values");
        
        nlohmann::json request;
        request[AimxConstants::JsonFields::AIMX_AD_AREA] = AimxConstants::ADAreas::AIMX_DC_DIAGNOSTICS;
        
        // Create a very long DC name (10,000 characters)
        std::string longDCName = "long-dc-name";
        for (int i = 0; i < 1000; i++) {
            longDCName += "-extension" + std::to_string(i);
        }
        request[AimxConstants::JsonFields::AIMX_DC_NAME] = longDCName;
        
        std::string requestStr = request.dump();
        std::string responseStr;
        
        bool result = agent.ExecuteAction(requestStr, responseStr);
        // Should handle this without crashing, though may fail due to validation
        
        // We don't verify specific result because behavior may vary based on implementation
        // Just ensure we get a valid JSON response
        VERIFY_IS_TRUE(nlohmann::json::accept(responseStr), L"Response should be valid JSON");
        
        nlohmann::json response = nlohmann::json::parse(responseStr);
        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
        
        Log::Comment(String().Format(L"Result for extremely long field values: %hs", 
                                  response[AimxConstants::JsonFields::AIMX_RESULT].get<std::string>().c_str()));
    }
    
    // Test case 5: Case sensitivity in ADArea
    {
        Log::Comment(L"Test case 5: Case sensitivity in ADArea");
        
        nlohmann::json request;
        request[AimxConstants::JsonFields::AIMX_AD_AREA] = "ad_dc_diagnostics"; // lowercase
        request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";
        
        std::string requestStr = request.dump();
        std::string responseStr;
        
        bool result = agent.ExecuteAction(requestStr, responseStr);
        
        nlohmann::json response = nlohmann::json::parse(responseStr);
        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
        
        std::string resultStr = response[AimxConstants::JsonFields::AIMX_RESULT].get<std::string>();
        Log::Comment(String().Format(L"Result for lowercase ADArea: %hs", resultStr.c_str()));
        
        // Document whether API is case-sensitive or case-insensitive
        if (resultStr == AimxConstants::JsonFields::AIMX_SUCCESS)
        {
            Log::Comment(L"ADArea appears to be case-insensitive");
        } 
        else
        {
            Log::Comment(L"ADArea appears to be case-sensitive");
        }
    }
    
    Log::Comment(L"ADToolAgent::ExecuteActionEdgeCases tests completed");
    Log::Comment(L"====== Finished TestExecuteActionEdgeCases ======");
}

void ADToolAgentTests::TestErrorRecovery()
{
    Log::Comment(L"====== Starting TestErrorRecovery ======");
    Log::Comment(L"Testing ADToolAgent's ability to recover from errors");
    
    ADToolAgent& agent = ADToolAgent::GetInstance();
    Log::Comment(L"ADToolAgent instance obtained");
    
    // Test case 1: Recovery after invalid JSON
    {
        Log::Comment(L"Test case 1: Recovery after invalid JSON");
        
        // First, send an invalid JSON to potentially cause an error
        std::string invalidRequestStr = "{ invalid json }";
        std::string responseStr;
        
        bool result = agent.ExecuteAction(invalidRequestStr, responseStr);
        VERIFY_IS_FALSE(result, L"ExecuteAction should fail for invalid JSON");
        Log::Comment(L"Invalid JSON request completed");
        
        // Now, send a valid request to check recovery
        nlohmann::json validRequest;
        validRequest[AimxConstants::JsonFields::AIMX_AD_AREA] = AimxConstants::ADAreas::AIMX_DC_DIAGNOSTICS;
        validRequest[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";
        
        std::string validRequestStr = validRequest.dump();
        std::string validResponseStr;
        
        bool validResult = agent.ExecuteAction(validRequestStr, validResponseStr);
        VERIFY_IS_TRUE(validResult, L"ExecuteAction should succeed after invalid JSON recovery");
        
        nlohmann::json response = nlohmann::json::parse(validResponseStr);
        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
        VERIFY_IS_TRUE(response[AimxConstants::JsonFields::AIMX_RESULT] == AimxConstants::JsonFields::AIMX_SUCCESS, L"Valid request after invalid should succeed");
        
        Log::Comment(L"Successfully recovered from invalid JSON");
    }
    
    // Test case 2: Recovery after invalid ADArea
    {
        Log::Comment(L"Test case 2: Recovery after invalid ADArea");
        
        // First, send a request with invalid ADArea
        nlohmann::json invalidRequest;
        invalidRequest[AimxConstants::JsonFields::AIMX_AD_AREA] = "INVALID_AREA";
        invalidRequest[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";
        
        std::string invalidRequestStr = invalidRequest.dump();
        std::string responseStr;
        
        bool result = agent.ExecuteAction(invalidRequestStr, responseStr);
        VERIFY_IS_FALSE(result, L"ExecuteAction should fail for invalid ADArea");
        Log::Comment(L"Invalid ADArea request completed");
        
        // Now, send a valid request to check recovery
        nlohmann::json validRequest;
        validRequest[AimxConstants::JsonFields::AIMX_AD_AREA] = AimxConstants::ADAreas::AIMX_DC_DIAGNOSTICS;
        validRequest[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";
        
        std::string validRequestStr = validRequest.dump();
        std::string validResponseStr;
        
        bool validResult = agent.ExecuteAction(validRequestStr, validResponseStr);
        VERIFY_IS_TRUE(validResult, L"ExecuteAction should succeed after invalid ADArea recovery");
        
        nlohmann::json response = nlohmann::json::parse(validResponseStr);
        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
        VERIFY_IS_TRUE(response[AimxConstants::JsonFields::AIMX_RESULT] == AimxConstants::JsonFields::AIMX_SUCCESS, L"Valid request after invalid should succeed");
        
        Log::Comment(L"Successfully recovered from invalid ADArea");
    }
    
    // Test case 3: Recovery after failed command execution
    {
        Log::Comment(L"Test case 3: Recovery after failed command execution");
        
        // First, run an invalid command to cause failure
        std::wstring output;
        // Use a command with invalid syntax that will definitely fail
        bool cmdResult = agent.RunCommand(L"cmd.exe /c exit 1", output);
        // We don't verify the return value here since we just want to ensure recovery works
        Log::Comment(L"Potentially failing command execution completed");
        
        // Now, run a valid command to check recovery
        bool validCmdResult = agent.RunCommand(L"hostname", output);
        VERIFY_IS_TRUE(validCmdResult, L"RunCommand should succeed after failed command recovery");
        VERIFY_IS_FALSE(output.empty(), L"Output should not be empty for valid command");
        
        Log::Comment(String().Format(L"Valid command output after recovery: %s", output.c_str()));
        Log::Comment(L"Successfully recovered from failed command execution");
    }
    
    // Test case 4: Multiple consecutive failures followed by success
    {
        Log::Comment(L"Test case 4: Multiple consecutive failures followed by success");
        
        // Send multiple invalid requests in succession
        for (int i = 0; i < 5; i++)
        {
            std::string invalidRequestStr = "{ invalid json " + std::to_string(i) + " }";
            std::string responseStr;
            
            bool result = agent.ExecuteAction(invalidRequestStr, responseStr);
            VERIFY_IS_FALSE(result, L"ExecuteAction should fail for invalid JSON");
            Log::Comment(String().Format(L"Invalid request %d completed", i + 1));
        }
        
        // Now, send a valid request to check recovery
        nlohmann::json validRequest;
        validRequest[AimxConstants::JsonFields::AIMX_AD_AREA] = AimxConstants::ADAreas::AIMX_DC_DIAGNOSTICS;
        validRequest[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";
        
        std::string validRequestStr = validRequest.dump();
        std::string validResponseStr;
        
        bool validResult = agent.ExecuteAction(validRequestStr, validResponseStr);
        VERIFY_IS_TRUE(validResult, L"ExecuteAction should succeed after multiple failures");
        
        nlohmann::json response = nlohmann::json::parse(validResponseStr);
        VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
        VERIFY_IS_TRUE(response[AimxConstants::JsonFields::AIMX_RESULT] == AimxConstants::JsonFields::AIMX_SUCCESS, L"Valid request after multiple failures should succeed");
        
        Log::Comment(L"Successfully recovered from multiple consecutive failures");
    }
    
    Log::Comment(L"ADToolAgent error recovery tests completed");
    Log::Comment(L"====== Finished TestErrorRecovery ======");
}

void ADToolAgentTests::TestPerformance()
{
    Log::Comment(L"====== Starting TestPerformance ======");
    Log::Comment(L"Testing performance of ADToolAgent operations");
    
    ADToolAgent& agent = ADToolAgent::GetInstance();
    Log::Comment(L"ADToolAgent instance obtained");
    
    // Test case 1: ExecuteAction performance
    {
        Log::Comment(L"Test case 1: ExecuteAction performance");
        
        nlohmann::json request;
        request[AimxConstants::JsonFields::AIMX_AD_AREA] = AimxConstants::ADAreas::AIMX_DC_DIAGNOSTICS;
        request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";
        
        std::string requestStr = request.dump();
        std::string responseStr;
        
        const int NUM_ITERATIONS = 5;
        std::vector<long long> durations;
        
        Log::Comment(String().Format(L"Running ExecuteAction performance test with %d iterations", NUM_ITERATIONS));
        
        // Perform multiple iterations and measure time
        for (int i = 0; i < NUM_ITERATIONS; i++) {
            auto start = std::chrono::high_resolution_clock::now();
            
            bool result = agent.ExecuteAction(requestStr, responseStr);
            VERIFY_IS_TRUE(result, L"ExecuteAction should succeed");
            
            auto end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
            
            durations.push_back(duration);
            Log::Comment(String().Format(L"Iteration %d completed in %lld ms", i + 1, duration));
        }
        
        // Calculate statistics
        long long totalDuration = 0;
        long long minDuration = LLONG_MAX;
        long long maxDuration = 0;
        
        for (long long duration : durations) {
            totalDuration += duration;
            minDuration = min(minDuration, duration);
            maxDuration = max(maxDuration, duration);
        }
        
        long long avgDuration = totalDuration / NUM_ITERATIONS;
        
        Log::Comment(String().Format(L"ExecuteAction performance results:"));
        Log::Comment(String().Format(L"  Average duration: %lld ms", avgDuration));
        Log::Comment(String().Format(L"  Minimum duration: %lld ms", minDuration));
        Log::Comment(String().Format(L"  Maximum duration: %lld ms", maxDuration));
        
        // Set an appropriate threshold based on expected performance
        const long long ACCEPTABLE_THRESHOLD_MS = 5000; // 5 seconds
        
        VERIFY_IS_LESS_THAN(avgDuration, ACCEPTABLE_THRESHOLD_MS, 
                          L"Average ExecuteAction time should be less than threshold");
    }
    
    // Test case 2: RunCommand performance
    {
        Log::Comment(L"Test case 2: RunCommand performance");
        
        std::wstring command = L"hostname";
        std::wstring output;
        
        const int NUM_ITERATIONS = 5;
        std::vector<long long> durations;
        
        Log::Comment(String().Format(L"Running RunCommand performance test with %d iterations", NUM_ITERATIONS));
        
        // Perform multiple iterations and measure time
        for (int i = 0; i < NUM_ITERATIONS; i++) {
            auto start = std::chrono::high_resolution_clock::now();
            
            bool result = agent.RunCommand(command, output);
            VERIFY_IS_TRUE(result, L"RunCommand should succeed");
            
            auto end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
            
            durations.push_back(duration);
            Log::Comment(String().Format(L"Iteration %d completed in %lld ms", i + 1, duration));
        }
        
        // Calculate statistics
        long long totalDuration = 0;
        long long minDuration = LLONG_MAX;
        long long maxDuration = 0;
        
        for (long long duration : durations) {
            totalDuration += duration;
            minDuration = min(minDuration, duration);
            maxDuration = max(maxDuration, duration);
        }
        
        long long avgDuration = totalDuration / NUM_ITERATIONS;
        
        Log::Comment(String().Format(L"RunCommand performance results:"));
        Log::Comment(String().Format(L"  Average duration: %lld ms", avgDuration));
        Log::Comment(String().Format(L"  Minimum duration: %lld ms", minDuration));
        Log::Comment(String().Format(L"  Maximum duration: %lld ms", maxDuration));
        
        // Set an appropriate threshold based on expected performance
        const long long ACCEPTABLE_THRESHOLD_MS = 1000; // 1 second
        
        VERIFY_IS_LESS_THAN(avgDuration, ACCEPTABLE_THRESHOLD_MS, 
                          L"Average RunCommand time should be less than threshold");
    }

    // Test case 3: Memory consumption during operations
    {
        Log::Comment(L"Test case 3: Memory consumption during operations");
        
        // Get current memory usage
        PROCESS_MEMORY_COUNTERS pmcEx;
        BOOL result = GetProcessMemoryInfo(GetCurrentProcess(), &pmcEx, sizeof(pmcEx));
        
        if (result)
        {
            SIZE_T initialMemory = pmcEx.WorkingSetSize; // Using WorkingSetSize instead of PrivateUsage
            Log::Comment(String().Format(L"Initial memory usage: %zu bytes", initialMemory));
            
            // Perform a series of operations that might affect memory usage
            const int NUM_OPERATIONS = 100;
            
            for (int i = 0; i < NUM_OPERATIONS; i++)
            {
                nlohmann::json request;
                request[AimxConstants::JsonFields::AIMX_AD_AREA] = AimxConstants::ADAreas::AIMX_DC_DIAGNOSTICS;
                request[AimxConstants::JsonFields::AIMX_DC_NAME] = "localhost";
                
                std::string requestStr = request.dump();
                std::string responseStr;
                
                agent.ExecuteAction(requestStr, responseStr);            
            }
            
            // Get memory usage after operations
            result = GetProcessMemoryInfo(GetCurrentProcess(), &pmcEx, sizeof(pmcEx));
            
            if (result)
            {
                SIZE_T finalMemory = pmcEx.WorkingSetSize; // Using WorkingSetSize instead of PrivateUsage
                SIZE_T memoryDelta = finalMemory > initialMemory ? finalMemory - initialMemory : 0;
                
                Log::Comment(String().Format(L"Final memory usage: %zu bytes", finalMemory));
                Log::Comment(String().Format(L"Memory increase: %zu bytes", memoryDelta));
                
                // Set an appropriate threshold for memory increase
                const SIZE_T ACCEPTABLE_MEMORY_INCREASE = 10 * 1024 * 1024; // 10 MB
                
                VERIFY_IS_LESS_THAN(memoryDelta, ACCEPTABLE_MEMORY_INCREASE, 
                                  L"Memory usage increase should be within acceptable limits");
            }
            else
        {
                Log::Warning(L"Could not get final memory usage information");
            }
        }
        else
        {
            Log::Warning(L"Could not get initial memory usage information");
        }
    }
    
    Log::Comment(L"Performance tests completed");
    Log::Comment(L"====== Finished TestPerformance ======");
}

bool ADToolAgentTests::GetDomainControllerInfo(ADToolAgent& agent, std::wstring& dcName)
{
    Log::Comment(L"Attempting to discover domain controller info");
    
    std::wstring output;
    bool result = agent.RunCommand(
        L"powershell.exe -Command \"try { "
        L"Import-Module ActiveDirectory; "
        L"$dc = Get-ADDomainController -Discover -Service 'GlobalCatalog'; "
        L"if ($dc -and $dc.Count -gt 0) { $dc[0].Name } else { 'No DC found' } "
        L"} catch { Write-Output 'Failed to get DC info' }\"",
        output);
    
    if (!result || output.empty() || output.find(L"Failed") != std::wstring::npos || output.find(L"No DC found") != std::wstring::npos)
    {
        Log::Comment(L"Could not discover domain controller - AD module may not be available or not in a domain");
        return false;
    }
    
    // Trim whitespace
    output.erase(0, output.find_first_not_of(L" \t\n\r\f\v"));
    output.erase(output.find_last_not_of(L" \t\n\r\f\v") + 1);
    
    dcName = output;
    Log::Comment(String().Format(L"Discovered domain controller: %s", dcName.c_str()));
    
    return true;
}

void ADToolAgentTests::TestADEnvironmentDiscovery()
{
    Log::Comment(L"====== Starting TestADEnvironmentDiscovery ======");
    Log::Comment(L"Testing discovery of AD environment and integration with AD components");
    
    ADToolAgent& agent = ADToolAgent::GetInstance();
    Log::Comment(L"ADToolAgent instance obtained");
    
    // Test case 1: Discover domain controller info
    {
        Log::Comment(L"Test case 1: Discover domain controller info");
        
        std::wstring dcName;
        bool hasAD = GetDomainControllerInfo(agent, dcName);
        
        if (hasAD) 
        {
            Log::Comment(String().Format(L"Test running in domain environment with DC: %s", dcName.c_str()));
            
            // Test case 1.1: Run repadmin against discovered DC
            {
                Log::Comment(L"Test case 1.1: Run repadmin against discovered DC");
                
                std::wstring repadminOutput;
                std::wstring repadminCommand = L"powershell.exe -Command \"try { & repadmin /showrepl '" + dcName + L"' } catch { Write-Output 'Command failed: repadmin may not be available' }\"";
                
                bool repadminResult = agent.RunCommand(repadminCommand, repadminOutput);
                VERIFY_IS_TRUE(repadminResult, L"RunCommand should succeed for repadmin command");
                VERIFY_IS_FALSE(repadminOutput.empty(), L"Repadmin output should not be empty");
                
                // Check if repadmin was successful
                bool repadminSucceeded = repadminOutput.find(L"Command failed") == std::wstring::npos;
                
                if (repadminSucceeded) {
                    Log::Comment(L"Repadmin command executed successfully");
                    
                    // Log sample of repadmin output for verification
                    Log::Comment(String().Format(L"Repadmin output sample: %s", 
                        repadminOutput.substr(0, min(200, static_cast<int>(repadminOutput.length()))).c_str()));
                    
                    // Check for expected patterns in repadmin output
                    bool hasReplicationData = 
                        repadminOutput.find(L"DSA") != std::wstring::npos || 
                        repadminOutput.find(L"DC") != std::wstring::npos || 
                        repadminOutput.find(L"replication") != std::wstring::npos;
                    
                    VERIFY_IS_TRUE(hasReplicationData, L"Repadmin output should contain replication data");
                } else {
                    Log::Comment(L"Repadmin command failed - tool may not be available or insufficient permissions");
                }
            }
            
            // Test case 1.2: Query AD info via ExecuteAction
            {
                Log::Comment(L"Test case 1.2: Query AD info via ExecuteAction");
                
                nlohmann::json request;
                request[AimxConstants::JsonFields::AIMX_AD_AREA] = AimxConstants::ADAreas::AIMX_DC_DIAGNOSTICS;
                request[AimxConstants::JsonFields::AIMX_DC_NAME] = std::string(dcName.begin(), dcName.end());
                
                std::string requestStr = request.dump();
                std::string responseStr;
                
                bool result = agent.ExecuteAction(requestStr, responseStr);
                VERIFY_IS_TRUE(result, L"ExecuteAction should succeed for discovered DC");
                
                nlohmann::json response = nlohmann::json::parse(responseStr);
                VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
                VERIFY_IS_TRUE(response[AimxConstants::JsonFields::AIMX_RESULT] == AimxConstants::JsonFields::AIMX_SUCCESS, L"Result should be Success for discovered DC");
                
                Log::Comment(String().Format(L"AD_DC_DIAGNOSTICS response for discovered DC: %hs", 
                                          responseStr.c_str()));
            }
            
            // Test case 1.3: Get replication info via ExecuteAction
            {
                Log::Comment(L"Test case 1.3: Get replication info via ExecuteAction");
                
                nlohmann::json request;
                request[AimxConstants::JsonFields::AIMX_AD_AREA] = AimxConstants::ADAreas::AIMX_REPLICATION;
                request[AimxConstants::JsonFields::AIMX_DC_NAME] = std::string(dcName.begin(), dcName.end());
                request[AimxConstants::JsonFields::AIMX_ACTION] = AimxConstants::ActionTypes::AIMX_GET_REPLICATION_STATUS_INDIVIDUAL_DC;
                
                std::string requestStr = request.dump();
                std::string responseStr;
                
                bool result = agent.ExecuteAction(requestStr, responseStr);
                
                // We don't verify success here because it depends on AD environment
                // Just verify that we get a proper JSON response
                VERIFY_IS_TRUE(nlohmann::json::accept(responseStr), L"Response should be valid JSON");
                
                nlohmann::json response = nlohmann::json::parse(responseStr);
                VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_RESULT), L"Response should contain Result field");
                
                if (response[AimxConstants::JsonFields::AIMX_RESULT] == AimxConstants::JsonFields::AIMX_SUCCESS) {
                    Log::Comment(L"Successfully retrieved replication info via ExecuteAction");
                    
                    // Verify that we have ReplicationData field
                    VERIFY_IS_TRUE(response.contains(AimxConstants::JsonFields::AIMX_REPLICATION_DATA), 
                                  L"Response should contain ReplicationData field");
                } else {
                    Log::Comment(L"Could not retrieve replication info - this may be expected depending on environment");
                }
                
                Log::Comment(String().Format(L"AD_REPLICATION response sample: %hs", 
                                          responseStr.substr(0, min(200, static_cast<int>(responseStr.length()))).c_str()));
            }
        }
        else
        {
            Log::Comment(L"Test not running in Active Directory domain environment - limited testing possible");
            
            // If not in AD domain, still run a basic test that should work
            std::wstring computerNameOutput;
            bool computerNameResult = agent.RunCommand(L"hostname", computerNameOutput);
            VERIFY_IS_TRUE(computerNameResult, L"RunCommand should succeed for hostname command");
            VERIFY_IS_FALSE(computerNameOutput.empty(), L"Hostname output should not be empty");
            
            Log::Comment(String().Format(L"Running on computer: %s", computerNameOutput.c_str()));
        }
    }
    
    Log::Comment(L"AD environment discovery tests completed");
    Log::Comment(L"====== Finished TestADEnvironmentDiscovery ======");
}