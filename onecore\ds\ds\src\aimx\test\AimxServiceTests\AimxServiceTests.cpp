/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    AimxServiceTests.cpp

Abstract:
    Comprehensive test suite for AIMX Service component testing.
    Tests service lifecycle, RPC server, and component integration.

--*/

#include "pch.hxx"
#include "aimxrpcclient.h"
#include "AimxServiceTests.h"
#include <nlohmann/json.hpp>
#include <thread>
#include <chrono>
#include <vector>

void AimxServiceTests::TestServiceInitialization()
{
    Log::Comment(L"Testing AIMX Service Initialization");
    
    // This test validates that the service properly initializes
    // the RPC server and accepts connections
    
    GUID contextId = {};
    HRESULT hr = S_OK;
    AimxRpcClient* rpcClient = new AimxRpcClient();
    
    VERIFY_IS_NOT_NULL(rpcClient, L"RPC client creation should succeed");

    // Test 1: Basic connectivity
    Log::Comment(L"Test 1: Basic RPC connectivity");
    hr = rpcClient->AimxConnect(&contextId);
    VERIFY_SUCCEEDED(hr, L"Service should accept RPC connections");
    VERIFY_IS_FALSE(IsEqualGUID(contextId, GUID{}), L"ContextId should not be GUID_NULL after connect");
    
    Log::Comment(L"✓ Service RPC server is running and accepting connections");

    // Test 2: Context handle uniqueness
    Log::Comment(L"Test 2: Context handle uniqueness");
    GUID contextId2 = {};
    hr = rpcClient->AimxConnect(&contextId2);
    VERIFY_SUCCEEDED(hr, L"Service should handle multiple connections");
    VERIFY_IS_FALSE(IsEqualGUID(contextId, contextId2), L"Context handles should be unique");
    
    Log::Comment(L"✓ Service creates unique context handles");

    // Test 3: Proper cleanup
    Log::Comment(L"Test 3: Context cleanup");
    hr = rpcClient->AimxClose(contextId);
    VERIFY_SUCCEEDED(hr, L"Context cleanup should succeed");
    hr = rpcClient->AimxClose(contextId2);
    VERIFY_SUCCEEDED(hr, L"Second context cleanup should succeed");
    
    Log::Comment(L"✓ Service properly cleans up context handles");

    delete rpcClient;
    Log::Comment(L"AIMX Service Initialization test completed successfully");
}

void AimxServiceTests::TestRequestHandlerComponent()
{
    Log::Comment(L"Testing RequestHandler Component");
    
    GUID contextId = {};
    HRESULT hr = S_OK;
    AimxRpcClient* rpcClient = new AimxRpcClient();
    
    VERIFY_IS_NOT_NULL(rpcClient, L"RPC client creation should succeed");

    hr = rpcClient->AimxConnect(&contextId);
    VERIFY_SUCCEEDED(hr, L"Connection should succeed");

    // Test 1: Request routing validation
    Log::Comment(L"Test 1: Request routing to RequestHandler static methods");
    
    struct RequestTypeTest {
        int requestType;
        std::string typeName;
        nlohmann::json additionalFields;
        bool shouldSucceed;
    };

    std::vector<RequestTypeTest> requestTests = {
        {1, "AIMX_CHATBOT_QUERY", {{"query", "test"}, {"executionMode", 2}}, true},
        {2, "AIMX_DIRECT_QUERY", {{"command", "test"}, {"executionMode", 1}}, true},
        {3, "AIMX_PLAN_STATUS", {{"operationId", "{********-1234-1234-1234-********9012}"}}, false}, // No such operation
        {4, "AIMX_EXECUTE_PLAN", {{"operationId", "{********-1234-1234-1234-********9012}"}}, false}, // No such operation
        {5, "AIMX_CANCEL_OPERATION", {{"operationId", "{********-1234-1234-1234-********9012}"}}, false}, // No such operation
        {999, "INVALID_TYPE", {}, false}
    };

    for (const auto& test : requestTests)
    {
        Log::Comment(String().Format(L"Testing request type %d (%hs)", 
            test.requestType, test.typeName.c_str()));

        nlohmann::json request;
        request["requestType"] = test.requestType;
        
        for (auto& [key, value] : test.additionalFields.items())
        {
            request[key] = value;
        }

        std::string requestStr = request.dump();
        std::wstring requestWStr(requestStr.begin(), requestStr.end());

        LPWSTR response = nullptr;
        hr = rpcClient->AimxProcessPrompt(contextId, requestWStr.c_str(), &response);

        bool requestSucceeded = false;
        
        if (SUCCEEDED(hr) && response)
        {
            std::wstring responseWStr(response);
            std::string responseStr(responseWStr.begin(), responseWStr.end());
            
            if (nlohmann::json::accept(responseStr))
            {
                nlohmann::json responseJson = nlohmann::json::parse(responseStr);
                requestSucceeded = responseJson.value("success", false);
                
                if (!requestSucceeded)
                {
                    Log::Comment(String().Format(L"  Error: %hs", 
                        responseJson.value("errorMessage", "Unknown error").c_str()));
                }
            }
        }

        if (test.shouldSucceed)
        {
            VERIFY_IS_TRUE(requestSucceeded, L"Valid request should succeed");
            Log::Comment(L"  ✓ Request processed successfully");
        }
        else
        {
            VERIFY_IS_FALSE(requestSucceeded, L"Invalid request should fail");
            Log::Comment(L"  ✓ Invalid request properly rejected");
        }

        if (response) MIDL_user_free(response);
    }

    // Test 2: Field validation
    Log::Comment(L"Test 2: RequestHandler field validation");
    
    struct ValidationTest {
        std::string name;
        nlohmann::json request;
        bool expectSuccess;
    };

    std::vector<ValidationTest> validationTests = {
        // Valid chatbot query
        {"Valid chatbot query", {{"requestType", 1}, {"query", "test"}, {"executionMode", 2}}, true},
        
        // Missing required fields
        {"Missing query field", {{"requestType", 1}, {"executionMode", 2}}, false},
        {"Missing executionMode", {{"requestType", 1}, {"query", "test"}}, false},
        {"Missing command field", {{"requestType", 2}, {"executionMode", 1}}, false},
        {"Missing operationId", {{"requestType", 3}}, false},
        
        // Invalid field types
        {"Non-string query", {{"requestType", 1}, {"query", 123}, {"executionMode", 2}}, false},
        {"Non-integer executionMode", {{"requestType", 1}, {"query", "test"}, {"executionMode", "invalid"}}, false},
        {"Non-string operationId", {{"requestType", 3}, {"operationId", 123}}, false},

        // Invalid field values (note some request types ignore the execution mode e.g. 1. AIMX_CHATBOT_QUERY)
        // but we can validate it for other request types 
        {"Invalid requestType", {{"requestType", 999}, {"query", "test"}, {"executionMode", 2}}, false},
        {"Invalid executionMode value", {{"requestType", 2}, {"query", "test"}, {"executionMode", 999}}, false},
        {"Invalid GUID format", {{"requestType", 3}, {"operationId", "not-a-guid"}}, false},
    };

    for (const auto& test : validationTests)
    {
        Log::Comment(String().Format(L"Validation test: %hs", test.name.c_str()));

        std::string requestStr = test.request.dump();
        std::wstring requestWStr(requestStr.begin(), requestStr.end());

        LPWSTR response = nullptr;
        hr = rpcClient->AimxProcessPrompt(contextId, requestWStr.c_str(), &response);

        bool validationPassed = false;
        
        if (SUCCEEDED(hr) && response)
        {
            std::wstring responseWStr(response);
            std::string responseStr(responseWStr.begin(), responseWStr.end());
            
            if (nlohmann::json::accept(responseStr))
            {
                nlohmann::json responseJson = nlohmann::json::parse(responseStr);
                validationPassed = responseJson.value("success", false);
            }
            MIDL_user_free(response);
        }
        else if (response)
        {
            // Defensive: free response if set, even if hr failed
            MIDL_user_free(response);
        }

        if (test.expectSuccess)
        {
            VERIFY_IS_TRUE(validationPassed, L"Valid request should pass validation");
        }
        else
        {
            VERIFY_IS_FALSE(validationPassed, L"Invalid request should fail validation");
        }
    }

    hr = rpcClient->AimxClose(contextId);
    VERIFY_SUCCEEDED(hr, L"Connection cleanup should succeed");
    delete rpcClient;
    
    Log::Comment(L"RequestHandler Component test completed successfully");
}

void AimxServiceTests::TestPlannerIntegration()
{
    Log::Comment(L"Testing Planner Integration");
    
    GUID contextId = {};
    HRESULT hr = S_OK;
    AimxRpcClient* rpcClient = new AimxRpcClient();
    
    hr = rpcClient->AimxConnect(&contextId);
    VERIFY_SUCCEEDED(hr, L"Connection should succeed");

    // Test 1: Asynchronous planning workflow
    Log::Comment(L"Test 1: Asynchronous planning via RequestHandler");
    
    nlohmann::json planningRequest;
    planningRequest["requestType"] = 1; // AIMX_CHATBOT_QUERY
    planningRequest["query"] = "Test planner integration with RequestHandler";
    planningRequest["executionMode"] = 2; // AIMX_MODE_INTERACTIVE

    std::string requestStr = planningRequest.dump();
    std::wstring requestWStr(requestStr.begin(), requestStr.end());

    LPWSTR response = nullptr;
    hr = rpcClient->AimxProcessPrompt(contextId, requestWStr.c_str(), &response);
    VERIFY_SUCCEEDED(hr, L"Planning request should succeed");
    VERIFY_IS_NOT_NULL(response, L"Response should not be null");

    // Parse initial response
    std::wstring responseWStr(response);
    std::string responseStr(responseWStr.begin(), responseWStr.end());
    nlohmann::json responseJson = nlohmann::json::parse(responseStr);

    VERIFY_IS_TRUE(responseJson["success"].get<bool>(), L"Planning should start successfully");
    if (0 != strcmp("PLANNING", responseJson["status"].get<std::string>().c_str()))
    {
        VERIFY_FAIL(L"Initial status should be PLANNING");
    }

    std::string operationId = responseJson["operationId"].get<std::string>();
    Log::Comment(String().Format(L"Started planning operation: %hs", operationId.c_str()));
    
    MIDL_user_free(response);

    // Test 2: Status polling during planning
    Log::Comment(L"Test 2: Status polling during planning phase");
    
    bool planningComplete = false;
    int pollCount = 0;
    const int maxPolls = 20; // 20 seconds timeout
    std::vector<std::string> statusHistory;

    while (!planningComplete && pollCount < maxPolls)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        pollCount++;

        nlohmann::json statusRequest;
        statusRequest["requestType"] = 3; // AIMX_PLAN_STATUS
        statusRequest["operationId"] = operationId;

        std::string statusRequestStr = statusRequest.dump();
        std::wstring statusRequestWStr(statusRequestStr.begin(), statusRequestStr.end());

        response = nullptr;
        hr = rpcClient->AimxProcessPrompt(contextId, statusRequestWStr.c_str(), &response);
        
        if (SUCCEEDED(hr) && response)
        {
            std::wstring statusResponseWStr(response);
            std::string statusResponseStr(statusResponseWStr.begin(), statusResponseWStr.end());
            nlohmann::json statusResponseJson = nlohmann::json::parse(statusResponseStr);

            if (statusResponseJson["success"].get<bool>())
            {
                std::string currentStatus = statusResponseJson["status"].get<std::string>();
                
                if (statusHistory.empty() || statusHistory.back() != currentStatus)
                {
                    statusHistory.push_back(currentStatus);
                    Log::Comment(String().Format(L"Status change: %hs (poll #%d)", 
                        currentStatus.c_str(), pollCount));
                }

                if (currentStatus == "PLAN_READY" || currentStatus == "FAILED")
                {
                    planningComplete = true;
                    
                    if (currentStatus == "PLAN_READY" && statusResponseJson.contains("executionPlan"))
                    {
                        Log::Comment(L"✓ Execution plan generated successfully");
                        
                        std::string planStr = statusResponseJson["executionPlan"].get<std::string>();
                        Log::Comment(L"Plan preview (first 100 chars):");
                        std::string preview = planStr.substr(0, 100);
                        Log::Comment(String().Format(L"%hs...", preview.c_str()));
                    }
                }
            }

            MIDL_user_free(response);
        }
    }

    VERIFY_IS_TRUE(planningComplete, L"Planning should complete within timeout");
    VERIFY_IS_TRUE(statusHistory.size() >= 1, L"Should see status transitions");
    
    Log::Comment(L"Status transition history:");
    for (size_t i = 0; i < statusHistory.size(); i++)
    {
        Log::Comment(String().Format(L"  %d: %hs", static_cast<int>(i), statusHistory[i].c_str()));
    }

    hr = rpcClient->AimxClose(contextId);
    VERIFY_SUCCEEDED(hr, L"Connection cleanup should succeed");
    delete rpcClient;
    
    Log::Comment(L"Planner Integration test completed successfully");
}

void AimxServiceTests::TestOrchestratorIntegration()
{
    Log::Comment(L"Testing Orchestrator Integration");
    
    GUID contextId = {};
    HRESULT hr = S_OK;
    AimxRpcClient* rpcClient = new AimxRpcClient();
    
    hr = rpcClient->AimxConnect(&contextId);
    VERIFY_SUCCEEDED(hr, L"Connection should succeed");

    // Test 1: Automated execution (direct to orchestrator)
    Log::Comment(L"Test 1: Automated execution via RequestHandler");
    
    nlohmann::json automatedRequest;
    automatedRequest["requestType"] = 2; // AIMX_DIRECT_QUERY
    automatedRequest["command"] = "Test orchestrator integration";
    automatedRequest["executionMode"] = 1; // AIMX_MODE_AUTOMATED

    std::string requestStr = automatedRequest.dump();
    std::wstring requestWStr(requestStr.begin(), requestStr.end());

    LPWSTR response = nullptr;
    hr = rpcClient->AimxProcessPrompt(contextId, requestWStr.c_str(), &response);
    VERIFY_SUCCEEDED(hr, L"Automated request should succeed");

    // Parse response
    std::wstring responseWStr(response);
    std::string responseStr(responseWStr.begin(), responseWStr.end());
    nlohmann::json responseJson = nlohmann::json::parse(responseStr);

    VERIFY_IS_TRUE(responseJson["success"].get<bool>(), L"Automated request should succeed");
    if (0 != strcmp("EXECUTING", responseJson["status"].get<std::string>().c_str()))
    {
        VERIFY_FAIL(L"Should start executing immediately");
    }

    std::string operationId = responseJson["operationId"].get<std::string>();
    Log::Comment(String().Format(L"Started automated execution: %hs", operationId.c_str()));
    
    MIDL_user_free(response);

    // Test 2: Monitor execution progress
    Log::Comment(L"Test 2: Monitor execution progress");
    
    bool executionComplete = false;
    int pollCount = 0;
    const int maxPolls = 30; // 30 seconds timeout

    while (!executionComplete && pollCount < maxPolls)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        pollCount++;

        nlohmann::json statusRequest;
        statusRequest["requestType"] = 3; // AIMX_PLAN_STATUS
        statusRequest["operationId"] = operationId;

        std::string statusRequestStr = statusRequest.dump();
        std::wstring statusRequestWStr(statusRequestStr.begin(), statusRequestStr.end());

        response = nullptr;
        hr = rpcClient->AimxProcessPrompt(contextId, statusRequestWStr.c_str(), &response);
        
        if (SUCCEEDED(hr) && response)
        {
            std::wstring statusResponseWStr(response);
            std::string statusResponseStr(statusResponseWStr.begin(), statusResponseWStr.end());
            nlohmann::json statusResponseJson = nlohmann::json::parse(statusResponseStr);

            if (statusResponseJson["success"].get<bool>())
            {
                std::string currentStatus = statusResponseJson["status"].get<std::string>();
                
                Log::Comment(String().Format(L"Execution status: %hs (poll #%d)", 
                    currentStatus.c_str(), pollCount));

                if (currentStatus == "COMPLETED" || currentStatus == "FAILED")
                {
                    executionComplete = true;
                    
                    if (currentStatus == "COMPLETED" && statusResponseJson.contains("result"))
                    {
                        Log::Comment(L"✓ Execution completed with results");
                        
                        std::string resultStr = statusResponseJson["result"].get<std::string>();
                        Log::Comment(L"Result preview (first 100 chars):");
                        std::string preview = resultStr.substr(0, 100);
                        Log::Comment(String().Format(L"%hs...", preview.c_str()));
                    }
                }
            }

            MIDL_user_free(response);
        }
    }

    VERIFY_IS_TRUE(executionComplete, L"Execution should complete within timeout");

    hr = rpcClient->AimxClose(contextId);
    VERIFY_SUCCEEDED(hr, L"Connection cleanup should succeed");
    delete rpcClient;
    
    Log::Comment(L"Orchestrator Integration test completed successfully");
}

void AimxServiceTests::TestConcurrentOperations()
{
    Log::Comment(L"Testing Concurrent Operations");
    
    GUID contextId = {};
    HRESULT hr = S_OK;
    AimxRpcClient* rpcClient = new AimxRpcClient();
    
    hr = rpcClient->AimxConnect(&contextId);
    VERIFY_SUCCEEDED(hr, L"Connection should succeed");

    // Test: Multiple concurrent operations
    Log::Comment(L"Starting multiple concurrent operations");
    
    const int NUM_OPERATIONS = 5;
    std::vector<std::string> operationIds;
      // Start multiple operations
    for (int i = 0; i < NUM_OPERATIONS; i++)
    {
        nlohmann::json request;
        request["requestType"] = 1; // AIMX_CHATBOT_QUERY
        
        // Create query string using wide string format, then convert to narrow string
        std::wstring queryWide = String().Format(L"Concurrent operation test #%d", i + 1);
        std::string queryNarrow(queryWide.begin(), queryWide.end());
        request["query"] = queryNarrow;
        
        request["executionMode"] = 2; // AIMX_MODE_INTERACTIVE

        std::string requestStr = request.dump();
        std::wstring requestWStr(requestStr.begin(), requestStr.end());

        LPWSTR response = nullptr;
        hr = rpcClient->AimxProcessPrompt(contextId, requestWStr.c_str(), &response);
        
        if (SUCCEEDED(hr) && response)
        {
            std::wstring responseWStr(response);
            std::string responseStr(responseWStr.begin(), responseWStr.end());
            nlohmann::json responseJson = nlohmann::json::parse(responseStr);

            if (responseJson["success"].get<bool>())
            {
                std::string operationId = responseJson["operationId"].get<std::string>();
                operationIds.push_back(operationId);
                
                Log::Comment(String().Format(L"Started operation %d: %hs", 
                    i + 1, operationId.c_str()));
            }

            MIDL_user_free(response);
        }
    }

    VERIFY_ARE_EQUAL(NUM_OPERATIONS, static_cast<int>(operationIds.size()), 
        L"All operations should start successfully");

    // Monitor all operations
    Log::Comment(L"Monitoring concurrent operations");
    
    std::vector<bool> operationCompleted(NUM_OPERATIONS, false);
    int completedCount = 0;
    int pollRound = 0;
    const int maxPollRounds = 20;

    while (completedCount < NUM_OPERATIONS && pollRound < maxPollRounds)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        pollRound++;

        for (int i = 0; i < NUM_OPERATIONS; i++)
        {
            if (operationCompleted[i]) continue;

            nlohmann::json statusRequest;
            statusRequest["requestType"] = 3; // AIMX_PLAN_STATUS
            statusRequest["operationId"] = operationIds[i];

            std::string statusRequestStr = statusRequest.dump();
            std::wstring statusRequestWStr(statusRequestStr.begin(), statusRequestStr.end());

            LPWSTR response = nullptr;
            hr = rpcClient->AimxProcessPrompt(contextId, statusRequestWStr.c_str(), &response);
            
            if (SUCCEEDED(hr) && response)
            {
                std::wstring statusResponseWStr(response);
                std::string statusResponseStr(statusResponseWStr.begin(), statusResponseWStr.end());
                nlohmann::json statusResponseJson = nlohmann::json::parse(statusResponseStr);

                if (statusResponseJson["success"].get<bool>())
                {
                    std::string status = statusResponseJson["status"].get<std::string>();
                    
                    if (status == "PLAN_READY" || status == "COMPLETED" || status == "FAILED")
                    {
                        if (!operationCompleted[i])
                        {
                            operationCompleted[i] = true;
                            completedCount++;
                            
                            Log::Comment(String().Format(L"Operation %d completed with status: %hs", 
                                i + 1, status.c_str()));
                        }
                    }
                }

                MIDL_user_free(response);
            }
        }

        if (pollRound % 5 == 0)
        {
            Log::Comment(String().Format(L"Poll round %d: %d/%d operations completed", 
                pollRound, completedCount, NUM_OPERATIONS));
        }
    }

    VERIFY_IS_TRUE(completedCount >= NUM_OPERATIONS / 2, 
        L"At least half of concurrent operations should complete");

    hr = rpcClient->AimxClose(contextId);
    VERIFY_SUCCEEDED(hr, L"Connection cleanup should succeed");
    delete rpcClient;
    
    Log::Comment(String().Format(L"Concurrent Operations test completed: %d/%d operations finished", 
        completedCount, NUM_OPERATIONS));
}

void AimxServiceTests::TestErrorRecovery()
{
    Log::Comment(L"Testing Service Error Recovery");
    
    GUID contextId = {};
    HRESULT hr = S_OK;
    AimxRpcClient* rpcClient = new AimxRpcClient();
    
    hr = rpcClient->AimxConnect(&contextId);
    VERIFY_SUCCEEDED(hr, L"Connection should succeed");

    // Test 1: Invalid JSON handling
    Log::Comment(L"Test 1: Invalid JSON recovery");
    
    const wchar_t* invalidJson = L"{ invalid json format }";
    LPWSTR response = nullptr;
    hr = rpcClient->AimxProcessPrompt(contextId, invalidJson, &response);
    
    // Service should handle this gracefully
    if (SUCCEEDED(hr) && response)
    {
        Log::Comment(L"Service handled invalid JSON gracefully");
        MIDL_user_free(response);
    }
    else
    {
        Log::Comment(L"Service properly rejected invalid JSON");
    }

    // Test 2: Service continues after errors
    Log::Comment(L"Test 2: Service continuity after errors");
    
    nlohmann::json validRequest;
    validRequest["requestType"] = 1;
    validRequest["query"] = "Test service recovery";
    validRequest["executionMode"] = 2;

    std::string requestStr = validRequest.dump();
    std::wstring requestWStr(requestStr.begin(), requestStr.end());

    response = nullptr;
    hr = rpcClient->AimxProcessPrompt(contextId, requestWStr.c_str(), &response);
    VERIFY_SUCCEEDED(hr, L"Service should continue working after errors");

    if (response)
    {
        std::wstring responseWStr(response);
        std::string responseStr(responseWStr.begin(), responseWStr.end());
        nlohmann::json responseJson = nlohmann::json::parse(responseStr);
        
        VERIFY_IS_TRUE(responseJson["success"].get<bool>(), 
            L"Service should process valid requests after errors");
        
        MIDL_user_free(response);
    }

    hr = rpcClient->AimxClose(contextId);
    VERIFY_SUCCEEDED(hr, L"Connection cleanup should succeed");
    delete rpcClient;
    
    Log::Comment(L"Service Error Recovery test completed successfully");
}