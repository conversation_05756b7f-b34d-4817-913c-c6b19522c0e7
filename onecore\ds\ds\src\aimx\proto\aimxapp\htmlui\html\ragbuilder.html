<!--++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    ragbuilder.html

Abstract:

    This module implements the RAG (Retrieval-Augmented Generation) Builder interface.
    Provides UI for creating, managing, and indexing knowledge bases to enhance
    LLM responses with domain-specific information.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 03/29/2025

---->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIMX RAG Database Builder</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/ragbuilder.css">
    <script>
        console.log("RAG Builder HTML loaded - Head script executed");
    </script>
</head>
<body>
    <div class="container">
        <h1>AIMX RAG Database Builder</h1>
        
        <!-- Configuration section with collapsible header -->
        <div class="section">
            <div class="collapsible" id="configToggle">
                <h2>Configuration</h2>
                <i class="arrow"></i>
            </div>
            <div class="collapsible-content" id="configContent">
                <div class="form-group">
                    <label for="modelPath">Database Name:</label>
                    <input type="text" id="databaseName" value="New Model" placeholder="Enter model name">
                </div>
                <div class="form-group">
                    <label for="outputDir">Output Directory:</label>
                    <input type="text" id="outputDir" value="./ragdb" placeholder="Enter output directory path">
                </div>
                
                <div class="form-group">
                    <label for="chunkSize">Chunk Size:</label>
                    <input type="number" id="chunkSize" value="128" min="64" max="1024">
                </div>
                
                <div class="form-group">
                    <label for="modelPath">Embedding Model:</label>
                    <input type="text" id="modelPath" value="./models/all-minilm-l6-v2_f32.gguf" readonly>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>Document Input</h2>
            
            <!-- File selection mode toggle -->
            <div class="file-selection-toggle">
                <button id="folderModeButton" class="active">Select Folder</button>
                <button id="filesModeButton">Select Files</button>
            </div>
            
            <div class="form-group">
                <label for="docsDir">Selected Files:</label>
                <input type="text" id="docsDir" placeholder="No files selected yet" readonly>
                
                <div class="path-display" id="pathDisplay"></div>
            </div>
            
            <!-- Document list to show selected files -->
            <div class="document-list" id="documentList" style="display: none;">
                <div id="documentCount"></div>
                <div id="documentItems"></div>
            </div>
            
            <button id="buildButton">Build Database</button>
        </div>
        
        <div class="section">
            <h2>Progress</h2>
            <div class="progress-container">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            <div class="status" id="statusText">Ready to build</div>
            
            <div class="log-container" id="logOutput"></div>
        </div>
    </div>
    
    <!-- Load the external JavaScript file -->
    <script src="../js/ragbuilder.js"></script>
</body>
</html>
