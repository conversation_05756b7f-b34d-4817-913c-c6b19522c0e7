/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    RagHttpServer.cpp

Abstract:

    This module implements the HTTP server class for the RAG service.
    Provides HTTP endpoints for RAG database operations.

Author:

    <PERSON><PERSON><PERSON> (r<PERSON><PERSON>) 04/12/2025

--*/

#include "RagHttpServer.h"
#include "RagDatabaseBuilder.h"
#include "RagJobManager.h"
#include "../common/debug.h"
#include "../common/httplib/httplib.h"
#include "../common/nlohmann/json.hpp"
#include <filesystem>

using json = nlohmann::json;

/*++

Routine Description:

    Constructor for the RagHttpServer class.

Arguments:

    None.

Return Value:

    None.

--*/
RagHttpServer::RagHttpServer()
    : m_host("127.0.0.1")
    , m_port(8000)
    , m_initialized(false)
    , m_running(false)
{
    LOGINFO("RagHttpServer: Initializing");
}

/*++

Routine Description:

    Destructor for the RagHttpServer class.

Arguments:

    None.

Return Value:

    None.

--*/
RagHttpServer::~RagHttpServer()
{
    LOGINFO("RagHttpServer: Destroying");
    Stop();
}

/*++

Routine Description:

    Starts the HTTP server.

Arguments:

    runningFlag - Reference to the running flag from the service.

Return Value:

    bool - True if the server was started, false otherwise.

--*/
bool
RagHttpServer::Start(
    std::atomic<bool>& runningFlag
)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_running)
    {
        LOGINFO("RagHttpServer: Server is already running");
        return true;
    }

    LOGINFO("RagHttpServer: Starting server on " + m_host +":"+ std::to_string(m_port));

    // Create server instance
    m_server = std::make_unique<httplib::Server>();

    // Create RAG components
    m_ragBuilder = std::make_unique<RagDatabaseBuilder>();
    m_jobManager = std::make_unique<RagJobManager>();

    // Register endpoints
    RegisterEndpoints();

    // Start server thread
    m_serverThread = std::thread(&RagHttpServer::RunServerThread, this, std::ref(runningFlag));

    m_initialized = true;
    m_running = true;

    LOGINFO("RagHttpServer: Server started successfully");
    return true;
}

/*++

Routine Description:

    Stops the HTTP server.

Arguments:

    None.

Return Value:

    None.

--*/
void
RagHttpServer::Stop()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_running)
    {
        return;
    }

    LOGINFO("RagHttpServer: Stopping server");

    // Stop the server
    if (m_server)
    {
        m_server->stop();
    }

    // Wait for server thread to finish
    if (m_serverThread.joinable())
    {
        m_serverThread.join();
    }

    // Reset components
    m_server.reset();
    m_ragBuilder.reset();
    m_jobManager.reset();

    m_running = false;
    LOGINFO("RagHttpServer: Server stopped");
}

/*++

Routine Description:

    Registers HTTP endpoints with the server.

Arguments:

    None.

Return Value:

    None.

--*/
void
RagHttpServer::RegisterEndpoints()
{
    if (!m_server)
    {
        LOGERROR("RagHttpServer: Server not initialized");
        return;
    }

    LOGINFO("RagHttpServer: Registering endpoints");

    // POST /build - Start building a RAG database
    m_server->Post("/build", [this](const httplib::Request& req, httplib::Response& res) {
        HandleBuildRequest(req, res);
    });

    // GET /status/{job_id} - Get status of a specific job
    m_server->Get(R"(/status/([^/]+))", [this](const httplib::Request& req, httplib::Response& res) {
        HandleStatusRequest(req, res);
    });

    // GET /jobs - List all jobs
    m_server->Get("/jobs", [this](const httplib::Request& req, httplib::Response& res) {
        HandleJobsListRequest(req, res);
    });

    // POST /test - Test the RAG index
    m_server->Post("/test", [this](const httplib::Request& req, httplib::Response& res) {
        HandleTestRequest(req, res);
    });

    // GET /health - Health check endpoint
    m_server->Get("/health", [this](const httplib::Request& req, httplib::Response& res) {
        HandleHealthRequest(req, res);
    });

    LOGINFO("RagHttpServer: Endpoints registered");
}

/*++

Routine Description:

    Handler for the /build endpoint. Starts building a RAG database.

Arguments:

    req - The HTTP request.
    res - The HTTP response.

Return Value:

    None.

--*/
void
RagHttpServer::HandleBuildRequest(
    const httplib::Request& req,
    httplib::Response& res
)
{
    LOGINFO("RagHttpServer: Handling build request");

    try
    {
        // Parse request JSON
        json requestJson = json::parse(req.body);

        // Extract parameters
        std::string folderPath = requestJson["folder_path"].get<std::string>();
        std::string outputDir = requestJson["output_dir"].get<std::string>();
        int maxChunkSize = requestJson.value("max_chunk_size", 1000);
        float similarityThreshold = requestJson.value("similarity_threshold", 0.7f);
        std::string modelName = requestJson.value("model_name", "all-MiniLM-L6-v2");
        bool useGpu = requestJson.value("use_gpu", true);

        // Start build job
        std::string jobId = m_jobManager->CreateJob();

        // Initialize the RAG builder
        m_ragBuilder->Initialize(maxChunkSize, modelName, useGpu, outputDir);

        // Start the build process asynchronously
        m_ragBuilder->StartBuildAsync(
            jobId,
            folderPath,
            outputDir,
            maxChunkSize,
            similarityThreshold,
            modelName,
            useGpu,
            m_jobManager
        );

        // Create response
        json responseJson =
        {
            {"job_id", jobId},
            {"status", "started"},
            {"message", "RAG database build started from " + folderPath},
            {"using_gpu", m_ragBuilder->IsGpuAvailable() && useGpu}
        };

        res.set_content(responseJson.dump(), "application/json");
        res.status = 200;
    }
    catch (const std::exception& e)
    {
        LOGERROR("RagHttpServer: Error handling build request: ", e.what());

        // Create error response
        json errorJson =
        {
            {"error", e.what()}
        };

        res.set_content(errorJson.dump(), "application/json");
        res.status = 400;
    }
}

/*++

Routine Description:

    Handler for the /status/{job_id} endpoint. Gets the status of a job.

Arguments:

    req - The HTTP request.
    res - The HTTP response.

Return Value:

    None.

--*/
void
RagHttpServer::HandleStatusRequest(
    const httplib::Request& req,
    httplib::Response& res
)
{
    std::string jobId = req.matches[1];
    LOGINFO("RagHttpServer: Handling status request for job ", jobId.c_str());

    try
    {
        // Get job status
        if (!m_jobManager->HasJob(jobId))
        {
            // Job not found
            json errorJson = {
                {"error", "Job not found"}
            };

            res.set_content(errorJson.dump(), "application/json");
            res.status = 404;
            return;
        }

        // Get job status
        json jobStatus = m_jobManager->GetJobStatus(jobId);

        res.set_content(jobStatus.dump(), "application/json");
        res.status = 200;
    }
    catch (const std::exception& e)
    {
        LOGERROR("RagHttpServer: Error handling status request: ", e.what());

        // Create error response
        json errorJson = {
            {"error", e.what()}
        };

        res.set_content(errorJson.dump(), "application/json");
        res.status = 500;
    }
}

/*++

Routine Description:

    Handler for the /jobs endpoint. Lists all jobs.

Arguments:

    req - The HTTP request.
    res - The HTTP response.

Return Value:

    None.

--*/
void
RagHttpServer::HandleJobsListRequest(
    const httplib::Request& /* req */,
    httplib::Response& res
)
{
    LOGINFO("RagHttpServer: Handling jobs list request");

    try
    {
        // Get all jobs
        json jobsJson = m_jobManager->GetAllJobs();

        res.set_content(jobsJson.dump(), "application/json");
        res.status = 200;
    }
    catch (const std::exception& e)
    {
        LOGERROR("RagHttpServer: Error handling jobs list request: ", e.what());

        // Create error response
        json errorJson =
        {
            {"error", e.what()}
        };

        res.set_content(errorJson.dump(), "application/json");
        res.status = 500;
    }
}

/*++

Routine Description:

    Handler for the /test endpoint. Tests a RAG index.

Arguments:

    req - The HTTP request.
    res - The HTTP response.

Return Value:

    None.

--*/
void
RagHttpServer::HandleTestRequest(
    const httplib::Request& req,
    httplib::Response& res
)
{
    LOGINFO("RagHttpServer: Handling test request");

    try
    {
        // Parse request JSON
        json requestJson = json::parse(req.body);

        // Extract parameters
        std::string indexPath = requestJson["index_path"].get<std::string>();
        std::string query = requestJson["query"].get<std::string>();
        int k = requestJson.value("k", 5);

        // Test the index
        json results = m_ragBuilder->TestIndex(indexPath, query, k);

        res.set_content(results.dump(), "application/json");
        res.status = 200;
    }
    catch (const std::exception& e)
    {
        LOGERROR("RagHttpServer: Error handling test request: ", e.what());

        // Create error response
        json errorJson =
        {
            {"error", e.what()}
        };

        res.set_content(errorJson.dump(), "application/json");
        res.status = 500;
    }
}

/*++

Routine Description:

    Handler for the /health endpoint. Provides service health information.

Arguments:

    req - The HTTP request.
    res - The HTTP response.

Return Value:

    None.

--*/
void
RagHttpServer::HandleHealthRequest(
    const httplib::Request& /* req */,
    httplib::Response& res
)
{
    LOGINFO("RagHttpServer: Handling health request");

    try
    {
        // Get health information
        json healthJson =
        {
            {"status", "ok"},
            {"service", "RAG Database Builder"},
            {"version", "1.0.0"},
            {"gpu_available", m_ragBuilder->IsGpuAvailable()},
            {"gpu_name", m_ragBuilder->GetGpuName()},
            {"active_jobs", m_jobManager->GetActiveJobCount()},
            {"total_jobs", m_jobManager->GetTotalJobCount()}
        };

        res.set_content(healthJson.dump(), "application/json");
        res.status = 200;
    }
    catch (const std::exception& e)
    {
        LOGERROR("RagHttpServer: Error handling health request: ", e.what());

        // Create error response
        json errorJson =
        {
            {"error", e.what()},
            {"status", "error"}
        };

        res.set_content(errorJson.dump(), "application/json");
        res.status = 500;
    }
}

/*++

Routine Description:

    Runs the HTTP server in a thread.

Arguments:

    runningFlag - Reference to the running flag from the service.

Return Value:

    None.

--*/
void
RagHttpServer::RunServerThread(
    std::atomic<bool>& /* runningFlag */
)
{
LOGINFO("RagHttpServer: Starting server thread");

    // Listen on configured host/port
    if (!m_server->listen(m_host.c_str(), m_port))
    {
        LOGERROR("RagHttpServer: Failed to start server on " + m_host + ":" + std::to_string(m_port));
        return;
    }

    LOGINFO("RagHttpServer: Server thread exited");
}