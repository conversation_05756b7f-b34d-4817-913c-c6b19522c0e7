/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    dllmain.cpp

Abstract:
    DLL entry point for AIMX service DLL.

Author:
    <PERSON> (SNAKE FIGHTER) (lindakup) 06/12/2025

--*/

// DLL entry point for service

#include "pch.hxx"

#include "dllmain.cpp.tmh"


extern "C"
BOOL WINAPI
DllMain(
    _In_ HINSTANCE hinstDLL,
    _In_ DWORD fdwReason,
    _In_ LPVOID lpvReserved)
{

    UNREFERENCED_PARAMETER(lpvReserved);

    BOOL fResult = TRUE;

    switch (fdwReason)
    {
    case DLL_PROCESS_ATTACH:

        DisableThreadLibraryCalls(hinstDLL);

        WPP_INIT_TRACING(L"AIMXSrvService");

        TraceInfo(AimxService, "DLL_PROCESS_ATTACH: Initializing AIMX service");

        // SNAKE_TODO: register event log source here if needed
        // register telemetry or other initialization

        fResult = TRUE;
        break;

    case DLL_PROCESS_DETACH:

        // SNAKE_TODO: unregister event log source here if needed

        TraceInfo(AimxService, "DLL_PROCESS_DETACH: Cleaning up AIMX service");

        WPP_CLEANUP();

        fResult = TRUE;
        break;

    default:
        // For any other reason, just return TRUE
        fResult = TRUE;
        break;
    }

    return fResult;
}
