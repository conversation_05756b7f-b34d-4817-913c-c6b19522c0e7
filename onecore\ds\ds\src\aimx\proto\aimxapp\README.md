# AIMX - Active Directory AI Management Experience

AIMX is an AI-powered application that enhances Active Directory management through an intuitive interface combining LLM (Large Language Model) capabilities with specialized tools for monitoring, diagnostics, and knowledge management.


## File Structure

os/src/onecore/ds/ds/src/adai/proto/win32/
├── aimx/                                  # Main AIMX application
│   ├── htmlui/                            # WebView interface files
│   │   ├── html/                          # HTML templates
│   │   │   ├── addashboard.html           # AD Dashboard interface
│   │   │   ├── chat.html                  # Chat interface
│   │   │   ├── mainframe.html             # Main application container
│   │   │   ├── ragbuilder.html            # RAG Builder interface
│   │   │   └── settings.html              # Configuration interface
│   │   ├── css/                           # Styling files
│   │   │   ├── addashboard.css            # AD Dashboard styles
│   │   │   ├── ragbuilder.css             # RAG Builder styles
│   │   │   └── styles.css                 # Common application styles
│   │   └── js/                            # JavaScript functionality
│   │       ├── addashboard.js             # AD Dashboard logic
│   │       ├── chat.js                    # Chat interface logic
│   │       ├── mainframe.js               # Core application controller
│   │       ├── ragbuilder.js              # RAG Builder logic
│   │       └── settings.js                # Configuration management
│   ├── aimx.cpp                           # Application entry point
│   ├── aimx.rc                            # Resource script file
│   ├── Debug.cpp                          # Debug utilities
│   ├── Debug.h                            # Debug header
│   ├── LlmService.cpp                     # LLM API integration
│   ├── LlmService.h                       # LLM service header
│   ├── packages.config                    # NuGet packages configuration
│   ├── pch.h                              # Precompiled header
│   ├── product.pbxproj                    # Project build configuration
│   ├── RAGBuilder.cpp                     # RAG database builder
│   ├── RAGBuilder.h                       # RAG builder header
│   ├── RAGHelper.cpp                      # RAG utilities
│   ├── RAGHelper.h                        # RAG helper header
│   ├── resource.h                         # Resource definitions
│   ├── sources                            # Build configuration
│   ├── StringUtils.h                      # String manipulation utilities
│   ├── WebViewMessageHandler.cpp          # Base message handler
│   ├── WebViewMessageHandler.h            # webview2 message handler interface central routing
│   ├── WebViewMessageHandlerADHealth.cpp  # AD Dashboard handler
│   ├── WebViewMessageHandlerFileBrowser.cpp # File webview2 message browser handler (rag)
│   ├── WebViewMessageHandlerPrompt.cpp    # Chat webview2 message prompt handler
│   └── WebViewMessageHandlerRag.cpp       # RAG webview2 message handler
└──common/                                # Common libraries and utilities
    ├── Debug.h                            # Shared debug logging facility
    ├── hnswlib/                           # Vector DB library
    │   ├── bruteforce.h
    │   ├── hnswalg.h
    │   ├── hnswlib.h
    │   ├── space_ip.h
    │   ├── space_l2.h
    │   ├── stop_condition.h
    │   └── visited_list_pool.h
    ├── httplib/                           # HTTP library for network communication
    │   └── httplib.h
    └── nlohmann/                          # JSON library
        └── json.hpp