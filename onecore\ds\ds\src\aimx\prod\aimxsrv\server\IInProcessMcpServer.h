/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    IInProcessMcpServer.h

Abstract:

    Interface definition for in-process MCP servers that provide direct API access
    while maintaining full MCP protocol compliance. In-process servers implement
    the same ListTools/CallTool interface as out-of-process servers but operate
    through direct C++ function calls for improved performance.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/13/2025

--*/

#pragma once

#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include "nlohmann/json.hpp"
#include "../../McpProtocolLib/McpJsonRpc.h"
#include "McpSvrMgr.h"
#include "InProcessMcpUtils.h"
#include "AimxConstants.h"

// Forward declarations
struct MCP_TOOL_INFO;
struct MCP_TOOL_EXECUTION_RESULT;

// Tool parameter validation result
struct MCP_TOOL_PARAMETER_VALIDATION_RESULT
{
    bool isValid;
    std::wstring errorMessage;
    std::vector<std::wstring> missingRequiredParameters;
    std::vector<std::wstring> invalidParameters;
};

// Tool execution context for in-process servers
struct MCP_INPROCESS_EXECUTION_CONTEXT
{
    std::wstring serverName;
    std::wstring toolName;
    nlohmann::json parameters;
    DWORD timeoutMs;
    FILETIME startTime;
    bool isCancelled;
};

// In-process MCP server interface
// This interface maintains the exact same JSON blob interface as out-of-process servers
// but operates through direct API calls instead of JSON-RPC over stdio/pipes
class IInProcessMcpServer
{
public:
    virtual ~IInProcessMcpServer() = default;

    // MCP Protocol Methods - Core Interface (JSON blob based)

    // List all available tools (equivalent to MCP "tools/list")
    // Returns JSON response matching MCP protocol format
    virtual HRESULT ListTools(
        _Out_ nlohmann::json& toolsResponse
        ) = 0;

    // Execute a tool (equivalent to MCP "tools/call")
    // Accepts and returns JSON blobs matching MCP protocol format
    virtual HRESULT CallTool(
        _In_ const nlohmann::json& callRequest,
        _Out_ nlohmann::json& callResponse
        ) = 0;

    // Server Information Methods
    
    // Get server name (used for identification and routing)
    virtual std::wstring GetServerName() const = 0;
    
    // Get server description (for user display and documentation)
    virtual std::wstring GetServerDescription() const = 0;
    
    // Get server version (for compatibility and debugging)
    virtual std::wstring GetServerVersion() const = 0;

    // Initialize server (called during registration)
    virtual HRESULT Initialize()
    {
        // Default implementation - no initialization needed
        return S_OK;
    }

    // Cleanup server resources (called during shutdown)
    virtual void Uninitialize()
    {
        // Default implementation - no cleanup needed
    }
};

// Factory interface for creating in-process MCP servers
class IInProcessMcpServerFactory
{
public:
    virtual ~IInProcessMcpServerFactory() = default;

    // Create a new instance of the server
    virtual std::shared_ptr<IInProcessMcpServer> CreateServer() = 0;

    // Get server type identifier
    virtual std::wstring GetServerType() const = 0;

    // Check if this factory can create the requested server
    virtual bool CanCreateServer(
        _In_ const std::wstring& serverName
        ) const = 0;
};

// Registration helper macros for in-process servers
#define DECLARE_INPROCESS_MCP_SERVER(className) \
    class className##Factory : public IInProcessMcpServerFactory { \
    public: \
        std::shared_ptr<IInProcessMcpServer> CreateServer() override { \
            return std::make_shared<className>(); \
        } \
        std::wstring GetServerType() const override { \
            return L#className; \
        } \
        bool CanCreateServer(const std::wstring& serverName) const override { \
            return serverName == L#className; \
        } \
    };

#define REGISTER_INPROCESS_MCP_SERVER(className) \
    static className##Factory g_##className##Factory; \
    static bool g_##className##Registered = []() { \
        return SUCCEEDED(McpSvrMgr::RegisterInProcessServerFactory( \
            std::make_shared<className##Factory>())); \
    }();
