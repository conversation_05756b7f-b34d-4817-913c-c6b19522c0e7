TARGETNAME=Aimx.ServiceInstaller
TARGETTYPE=PROGRAM
TARGET_DESTINATION=retail

BUILD_FOR_CORESYSTEM=1
USE_DEFAULT_WIN32_LIBS=0

# Use dynamic Ucrt
USE_UNICRT=1
USE_MSVCRT=1

UMTYPE = console 
UMENTRY = wmain

C_DEFINES=$(C_DEFINES) \
    -DUNICODE \
    -D_UNICODE \
    
SOURCES=\
    main.cpp \

TARGETLIBS=$(TARGETLIBS)                                      \
    $(ONECORE_EXTERNAL_SDK_LIB_PATH)\mincore.lib                                 \
    $(MINWIN_INTERNAL_SDK_LIB_VPATH_L)\api-ms-win-core-crt-l2.lib \

DELAYLOAD = \
    api-ms-win-service-management-l1.dll; \
    userenv.dll; \
    api-ms-win-core-crt-l2.dll; \

DLOAD_ERROR_HANDLER=kernelbase