/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    AimxMcpToolTests.h

Abstract:
    Header file for comprehensive Active Directory MCP Tools testing.
    Tests MCP server functionality, tool definitions, command mapping,
    and LLM integration for AD command generation.

--*/

#pragma once

#include "WexTestClass.h"
#include "AdMcpSrv.h"
#include "AdMcpCommandMap.h"
#include "AdMcpLlmInference.h"
#include <nlohmann/json.hpp>

using namespace WEX::Logging;
using namespace WEX::Common;
using namespace WEX::TestExecution;
using json = nlohmann::json;

// Global dummy GUID for use in all test code
GUID DummyOperationId = { 0x12345678, 0x1234, 0x5678, {0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0} };

class AdMcpToolTests : public WEX::TestClass<AdMcpToolTests>
{
public:
    BEGIN_TEST_CLASS(AdMcpToolTests)
        TEST_CLASS_PROPERTY(L"TestClass", L"AD MCP Tools")
        TEST_CLASS_PROPERTY(L"Description", L"Comprehensive Active Directory MCP Tools testing")
    END_TEST_CLASS()

    // Test MCP server initialization
    BEGIN_TEST_METHOD(TestMcpServerInitialization)
        TEST_METHOD_PROPERTY(L"Priority", L"1")
        TEST_METHOD_PROPERTY(L"Description", L"Tests MCP server initialization and command map setup")
    END_TEST_METHOD()

    // Test MCP tool definitions
    BEGIN_TEST_METHOD(TestMcpToolDefinitions)
        TEST_METHOD_PROPERTY(L"Priority", L"1")
        TEST_METHOD_PROPERTY(L"Description", L"Tests MCP tool definition schemas for get_ad_command and call_llm")
    END_TEST_METHOD()

    // Test tools/list MCP endpoint
    BEGIN_TEST_METHOD(TestToolsListEndpoint)
        TEST_METHOD_PROPERTY(L"Priority", L"2")
        TEST_METHOD_PROPERTY(L"Description", L"Tests MCP tools/list endpoint returns correct tool definitions")
    END_TEST_METHOD()

    // Test get_ad_command tool
    BEGIN_TEST_METHOD(TestGetAdCommandTool)
        TEST_METHOD_PROPERTY(L"Priority", L"2")
        TEST_METHOD_PROPERTY(L"Description", L"Tests get_ad_command tool with various prompts")
    END_TEST_METHOD()

    // Test command mapping functionality
    BEGIN_TEST_METHOD(TestCommandMapping)
        TEST_METHOD_PROPERTY(L"Priority", L"2")
        TEST_METHOD_PROPERTY(L"Description", L"Tests AD command mapping from natural language")
    END_TEST_METHOD()

    // Test exported API
    BEGIN_TEST_METHOD(TestExportedGetAdToolsPromptApi)
        TEST_METHOD_PROPERTY(L"Priority", L"1")
        TEST_METHOD_PROPERTY(L"Description", L"Tests exported GetADToolsPrompt API functionality")
    END_TEST_METHOD()

    // Test error handling and edge cases
    BEGIN_TEST_METHOD(TestErrorHandlingAndEdgeCases)
        TEST_METHOD_PROPERTY(L"Priority", L"3")
        TEST_METHOD_PROPERTY(L"Description", L"Tests error handling for invalid inputs and edge cases")
    END_TEST_METHOD()

    // Test concurrent operations
    BEGIN_TEST_METHOD(TestConcurrentMcpOperations)
        TEST_METHOD_PROPERTY(L"Priority", L"3")
        TEST_METHOD_PROPERTY(L"Description", L"Tests concurrent MCP tool operations")
    END_TEST_METHOD()

    // Test class lifecycle methods for config setup/teardown
    TEST_CLASS_SETUP(ClassSetup);
    TEST_CLASS_CLEANUP(ClassCleanup);

private:
    // Helper methods for testing
    json CreateMcpRequest(const std::string& method, const json& params, GUID operationId = DummyOperationId);
    bool ValidateJsonRpcResponse(const json& response, const GUID& expectedId);
    bool ValidateToolDefinition(const json& toolDef, const std::string& expectedName);
};
