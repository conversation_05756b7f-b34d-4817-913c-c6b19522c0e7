/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    GetADComputerTool.cpp

Abstract:

    Implementation of Get-ADComputer tool for Active Directory MCP server.
    Provides comprehensive AD computer query functionality using native Win32 LDAP APIs
    while maintaining PowerShell cmdlet parameter compatibility.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 2025-7-16

--*/

#include "AdMcpSvr.h"
#include "StringUtils.h"
#include "AimxConstants.h"
#include "../aimxsrv/inc/wpp.h"

// WPP tracing
#include "GetADComputerTool.cpp.tmh"

HRESULT AdMcpSvr::GetADComputerTool(
    _In_ const nlohmann::json& parameters,
    _Out_ nlohmann::json& result
    )
/*++

Routine Description:
    Implementation of Get-ADComputer tool.

Arguments:
    parameters - Input parameters matching PowerShell Get-ADComputer cmdlet
    result - Receives computer query results as JSON

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    TraceInfo(AdMcpSvr, L"AdMcpSvr::GetADComputerTool called");

    try
    {
        result = nlohmann::json::object();

        // Initialize LDAP connection if needed
        std::wstring serverName;
        if (parameters.contains("Server") && parameters["Server"].is_string())
        {
            serverName = Utf8ToWide(parameters["Server"].get<std::string>());
        }

        HRESULT hr = InitializeLdapConnection(serverName);
        if (FAILED(hr))
        {
            return CreateLdapErrorResponse(hr, L"Failed to initialize LDAP connection", result);
        }

        // Parse search parameters
        std::wstring searchBase = m_defaultNamingContext;
        if (parameters.contains("SearchBase") && parameters["SearchBase"].is_string())
        {
            searchBase = Utf8ToWide(parameters["SearchBase"].get<std::string>());
        }

        std::wstring filter = L"(objectClass=computer)";
        if (parameters.contains("Identity") && parameters["Identity"].is_string())
        {
            std::wstring identity = Utf8ToWide(parameters["Identity"].get<std::string>());
            filter = L"(|(distinguishedName=" + identity + L")(objectGUID=" + identity +
                    L")(objectSid=" + identity + L")(sAMAccountName=" + identity + L"))";
        }
        else if (parameters.contains("Filter") && parameters["Filter"].is_string())
        {
            hr = ParseFilterParameter(parameters["Filter"], filter);
            if (FAILED(hr))
            {
                return CreateLdapErrorResponse(hr, L"Failed to parse Filter parameter", result);
            }
        }
        else if (parameters.contains("LDAPFilter") && parameters["LDAPFilter"].is_string())
        {
            filter = Utf8ToWide(parameters["LDAPFilter"].get<std::string>());
        }

        // Parse attributes to retrieve
        std::vector<std::wstring> attributes = GetDefaultComputerAttributes();
        if (parameters.contains("Properties"))
        {
            hr = ParsePropertiesParameter(parameters["Properties"], attributes);
            if (FAILED(hr))
            {
                return CreateLdapErrorResponse(hr, L"Failed to parse Properties parameter", result);
            }
        }

        // Parse search scope
        ULONG searchScope = LDAP_SCOPE_SUBTREE;
        if (parameters.contains("SearchScope"))
        {
            hr = ParseSearchScopeParameter(parameters["SearchScope"], searchScope);
            if (FAILED(hr))
            {
                return CreateLdapErrorResponse(hr, L"Failed to parse SearchScope parameter", result);
            }
        }

        // Execute LDAP search
        std::vector<nlohmann::json> computers;
        hr = ExecuteLdapSearch(searchBase, filter, attributes, searchScope, computers);
        if (FAILED(hr))
        {
            return CreateLdapErrorResponse(hr, L"LDAP search failed", result);
        }

        result["computers"] = computers;
        result["count"] = computers.size();
        result["server"] = WideToUtf8(m_domainController);

        TraceInfo(AdMcpSvr, L"AdMcpSvr::GetADComputerTool completed successfully, found %d computers", static_cast<int>(computers.size()));
        return S_OK;
    }
    catch (const std::exception& ex)
    {
        TraceErr(AdMcpSvr, L"Exception in GetADComputerTool: %s", ex.what());
        result = CreateErrorResponse(L"Failed to execute Get-ADComputer: " + Utf8ToWide(ex.what()), L"execution_error");
        return E_FAIL;
    }
}

std::vector<std::wstring> AdMcpSvr::GetDefaultComputerAttributes()
/*++

Routine Description:
    Get default attributes for computer objects.

Return Value:
    Vector of default computer attribute names

--*/
{
    return {
        L"distinguishedName", L"objectGUID", L"objectSid", L"sAMAccountName",
        L"name", L"dNSHostName", L"operatingSystem", L"operatingSystemVersion",
        L"operatingSystemServicePack", L"lastLogonTimestamp", L"pwdLastSet",
        L"userAccountControl", L"objectClass", L"whenCreated", L"whenChanged",
        L"servicePrincipalName", L"location", L"managedBy"
    };
}
