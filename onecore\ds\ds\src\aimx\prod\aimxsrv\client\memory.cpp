/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    memory.cpp

Abstract:
    Memory allocation and deallocation helpers for AIMXSRV.

Author:
    <PERSON> (SNAKE FIGHTER) (lindakup) 06/10/2025

--*/
#include "pch.hxx"
#include "memory.h"


PVOID AimxAlloc(ULONG Size)
{
    return malloc(Size);
}

PVOID AimxAlloc(size_t Size)
{
    return malloc(Size);
}

extern "C" AIMXCLIENT_API void AimxFree(void* p)
{
    if (p) {
        MIDL_user_free(p);
    }
}

PVOID __RPC_USER MIDL_user_allocate(size_t NumBytes)
{
    return malloc(NumBytes);
}

VOID __RPC_USER MIDL_user_free(PVOID p)
{
    free(p);
}
