<!--++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    addashboard.html

Abstract:

    This module implements the Active Directory health monitoring dashboard.
    Provides visualizations of server status, authentication activity, replication health,
    and other critical AD metrics for system administrators.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 03/29/2025

---->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AD Health Dashboard</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/addashboard.css">
</head>
<body>
    <div class="dashboard-container">
        <header class="dashboard-header">
            <h1>Active Directory Health Dashboard</h1>
            <div class="dashboard-controls">
                <div class="timeframe-selector">
                    <button class="timeframe-btn active" data-timeframe="day">24 Hours</button>
                    <button class="timeframe-btn" data-timeframe="week">7 Days</button>
                    <button class="timeframe-btn" data-timeframe="month">30 Days</button>
                </div>
                <button id="refreshBtn" class="control-btn refresh-btn">
                    <span class="icon">↻</span> Refresh
                </button>
                <button id="reportBtn" class="control-btn primary-btn">
                    <span class="icon">📊</span> Report
                </button>
            </div>
        </header>

        <section class="dashboard-overview">
            <div class="overview-card">
                <div class="overview-stats">
                    <div class="stat-item">
                        <div class="stat-value" id="dcCount">--</div>
                        <div class="stat-label">Domain Controllers</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="serverCount">--</div>
                        <div class="stat-label">Member Servers</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="userCount">--</div>
                        <div class="stat-label">Active Users</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value health-indicator" id="overallHealth">--</div>
                        <div class="stat-label">Overall Health</div>
                    </div>
                </div>
            </div>
        </section>

        <div class="dashboard-grid">
            <!-- Server Status Chart -->
            <div class="dashboard-card">
                <h2>Server Status</h2>
                <div class="chart-container">
                    <canvas id="serverStatusChart"></canvas>
                </div>
            </div>

            <!-- Authentication Activity Chart -->
            <div class="dashboard-card">
                <h2>Authentication Activity</h2>
                <div class="chart-container">
                    <canvas id="authActivityChart"></canvas>
                </div>
            </div>

            <!-- Replication Health Chart -->
            <div class="dashboard-card">
                <h2>Replication Health</h2>
                <div class="chart-container">
                    <canvas id="replicationChart"></canvas>
                </div>
            </div>

            <!-- Resource Utilization Chart -->
            <div class="dashboard-card">
                <h2>Resource Utilization</h2>
                <div class="chart-container">
                    <canvas id="resourceUtilizationChart"></canvas>
                </div>
            </div>

            <!-- Security Incidents Chart -->
            <div class="dashboard-card">
                <h2>Security Incidents</h2>
                <div class="chart-container">
                    <canvas id="securityIncidentsChart"></canvas>
                </div>
            </div>

            <!-- Active Alerts -->
            <div class="dashboard-card">
                <h2>Active Alerts</h2>
                <div class="alerts-container" id="alertsContainer">
                    <div class="alert-placeholder">No alerts at this time.</div>
                </div>
            </div>

            <!-- Critical Issues -->
            <div class="dashboard-card">
                <h2>Critical Issues</h2>
                <div class="issues-container" id="issuesContainer">
                    <div class="issue-placeholder">No critical issues detected.</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Generation Modal -->
    <div id="reportModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Generate Report</h2>
                <button class="close-button">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="reportType">Report Type:</label>
                    <select id="reportType">
                        <option value="summary">Health Summary</option>
                        <option value="detailed">Detailed Health Report</option>
                        <option value="security">Security Analysis</option>
                        <option value="performance">Performance Report</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="reportTimeframe">Time Period:</label>
                    <select id="reportTimeframe">
                        <option value="day">Last 24 Hours</option>
                        <option value="week">Last 7 Days</option>
                        <option value="month">Last 30 Days</option>
                        <option value="custom">Custom Range</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="reportFormat">Format:</label>
                    <select id="reportFormat">
                        <option value="pdf">PDF</option>
                        <option value="html">HTML</option>
                        <option value="csv">CSV Data</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Include Sections:</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" name="sections" value="overview" checked> Overview Stats</label>
                        <label><input type="checkbox" name="sections" value="servers" checked> Server Status</label>
                        <label><input type="checkbox" name="sections" value="auth" checked> Authentication</label>
                        <label><input type="checkbox" name="sections" value="replication" checked> Replication</label>
                        <label><input type="checkbox" name="sections" value="resources" checked> Resource Usage</label>
                        <label><input type="checkbox" name="sections" value="security" checked> Security</label>
                        <label><input type="checkbox" name="sections" value="alerts" checked> Active Alerts</label>
                        <label><input type="checkbox" name="sections" value="issues" checked> Critical Issues</label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="generateReportBtn" class="primary-btn">Generate Report</button>
                <button class="cancel-btn">Cancel</button>
            </div>
        </div>
    </div>

    <script src="../js/addashboard.js"></script>
</body>
</html>
