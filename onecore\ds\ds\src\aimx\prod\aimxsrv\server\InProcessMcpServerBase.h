/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    InProcessMcpServerBase.h

Abstract:

    Base class implementation for in-process MCP servers providing common functionality
    like tool registration, parameter validation, error handling, and result formatting.
    Follows patterns from C# MCP SDK reference implementations while maintaining
    C++ performance characteristics.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/12/2025

--*/

#pragma once

#include "IInProcessMcpServer.h"
#include "StringUtils.h"
#include "../../McpProtocolLib/McpJsonRpc.h"
#include <unordered_map>
#include <functional>
#include <mutex>
#include <shared_mutex>
#include <atomic>

// Tool handler function signature - accepts and returns JSON blobs
using McpToolHandler = std::function<HRESULT(const nlohmann::json&, nlohmann::json&)>;

// Tool definition structure for registration
struct MCP_TOOL_DEFINITION
{
    std::wstring name;
    std::wstring description;
    nlohmann::json inputSchema;
    nlohmann::json outputSchema;
    McpToolHandler handler;
    bool requiresElevation;
    DWORD estimatedExecutionTimeMs;
};

// Base class for in-process MCP servers
class InProcessMcpServerBase : public IInProcessMcpServer
{
public:
    // Constructor
    InProcessMcpServerBase(
        _In_ const std::wstring& serverName,
        _In_ const std::wstring& description,
        _In_ const std::wstring& version = L"1.0.0"
        );

    // Destructor
    virtual ~InProcessMcpServerBase();

    // IInProcessMcpServer implementation - JSON blob interface
    HRESULT ListTools(
        _Out_ nlohmann::json& toolsResponse
        ) override;

    HRESULT CallTool(
        _In_ const nlohmann::json& callRequest,
        _Out_ nlohmann::json& callResponse
        ) override;

    std::wstring GetServerName() const override;
    std::wstring GetServerDescription() const override;
    std::wstring GetServerVersion() const override;

    HRESULT Initialize() override;
    void Uninitialize() override;

protected:
    // Tool registration methods for derived classes
    
    // Register a tool with handler function
    HRESULT RegisterTool(
        _In_ const MCP_TOOL_DEFINITION& toolDefinition
        );

    // Register a simple tool with lambda handler
    HRESULT RegisterSimpleTool(
        _In_ const std::wstring& name,
        _In_ const std::wstring& description,
        _In_ McpToolHandler handler
        );

    // Unregister a tool
    HRESULT UnregisterTool(
        _In_ const std::wstring& toolName
        );

    // Utility methods for derived classes
    
    // Create standard success response
    nlohmann::json CreateSuccessResponse(
        _In_ const nlohmann::json& content
        ) const;

    // Create standard error response
    nlohmann::json CreateErrorResponse(
        _In_ const std::wstring& errorMessage,
        _In_ const std::wstring& errorCode = L"execution_error"
        ) const;

    // Log tool execution
    void LogToolExecution(
        _In_ const std::wstring& toolName,
        _In_ const nlohmann::json& parameters,
        _In_ HRESULT result,
        _In_ DWORD executionTimeMs
        ) const;

    // Virtual methods for derived classes to override
    
    // Called during initialization - override to register tools
    virtual HRESULT OnInitialize() { return S_OK; }
    
    // Called during cleanup - override for custom cleanup
    virtual void OnUninitialize() { }

    // Called before tool execution - override for custom validation
    virtual HRESULT OnBeforeToolExecution(
        _In_ const std::wstring& toolName,
        _In_ const nlohmann::json& parameters
        ) {
        UNREFERENCED_PARAMETER(toolName);
        UNREFERENCED_PARAMETER(parameters);
        return S_OK;
    }

    // Called after tool execution - override for custom post-processing
    virtual HRESULT OnAfterToolExecution(
        _In_ const std::wstring& toolName,
        _In_ const nlohmann::json& parameters,
        _In_ const nlohmann::json& result,
        _In_ HRESULT executionResult
        ) {
        UNREFERENCED_PARAMETER(toolName);
        UNREFERENCED_PARAMETER(parameters);
        UNREFERENCED_PARAMETER(result);
        UNREFERENCED_PARAMETER(executionResult);
        return S_OK;
    }

private:
    // Internal data members
    std::wstring m_serverName;
    std::wstring m_description;
    std::wstring m_version;
    bool m_initialized;

    // Tool registry
    std::unordered_map<std::wstring, MCP_TOOL_DEFINITION> m_registeredTools;
    mutable std::shared_mutex m_toolsMutex;

    // Statistics
    std::atomic<DWORD> m_totalExecutions;
    std::atomic<DWORD> m_successfulExecutions;
    std::atomic<DWORD> m_failedExecutions;

    // Internal helper methods
    HRESULT ExecuteToolInternal(
        _In_ const std::wstring& toolName,
        _In_ const nlohmann::json& parameters,
        _Out_ nlohmann::json& result
        );
    void UpdateExecutionStatistics(
        _In_ bool success
        );
};

// Utility macros for tool registration
#define REGISTER_MCP_TOOL(toolName, description, handlerMethod) \
    RegisterSimpleTool(L#toolName, L##description, \
        [this](const nlohmann::json& params, nlohmann::json& result) -> HRESULT { \
            return this->handlerMethod(params, result); \
        })

#define REGISTER_MCP_TOOL_WITH_SCHEMA(toolName, description, inputSchema, outputSchema, handlerMethod) \
    do { \
        MCP_TOOL_DEFINITION toolDef; \
        toolDef.name = L#toolName; \
        toolDef.description = L##description; \
        toolDef.inputSchema = inputSchema; \
        toolDef.outputSchema = outputSchema; \
        toolDef.handler = [this](const nlohmann::json& params, nlohmann::json& result) -> HRESULT { \
            return this->handlerMethod(params, result); \
        }; \
        toolDef.requiresElevation = false; \
        toolDef.estimatedExecutionTimeMs = 1000; \
        RegisterTool(toolDef); \
    } while(0)
